import sqlite3

DB_PATH = 'crm_database.db'

ALTER_SQL = "ALTER TABLE billing ADD COLUMN created_date TEXT;"

if __name__ == "__main__":
    conn = sqlite3.connect(DB_PATH)
    try:
        # Check if column already exists
        cursor = conn.execute("PRAGMA table_info(billing);")
        columns = [row[1] for row in cursor.fetchall()]
        if 'created_date' in columns:
            print("Column 'created_date' already exists in 'billing' table.")
        else:
            conn.execute(ALTER_SQL)
            print("Column 'created_date' added to 'billing' table.")
    except Exception as e:
        print(f"Error: {e}")
    finally:
        conn.close() 