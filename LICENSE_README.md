# CRM System License Management

## Overview

The CRM System uses a license validation system to ensure proper usage and access control. This document explains how the license system works and how to troubleshoot common issues.

## License File Location

The license file is stored at:
```
%APPDATA%\CRM_System\license.json
```

On Windows, this typically resolves to:
```
C:\Users\<USER>\AppData\Roaming\CRM_System\license.json
```

## License File Format

The license file should contain valid JSON with the following structure:

```json
{
    "license_key": "76C4EDB3C004BA89",
    "expiry_date": "2025-12-31",
    "customer_name": "Sample Customer",
    "license_type": "standard",
    "features": ["basic", "advanced", "premium"],
    "activation_date": "2024-01-01",
    "max_users": 10,
    "version": "1.0"
}
```

## License Setup Utility

Use the `setup_license.py` script to manage your license file:

### Commands

1. **Create a new license file:**
   ```bash
   python setup_license.py create [license_key] [expiry_date]
   ```

2. **Validate existing license:**
   ```bash
   python setup_license.py validate
   ```

3. **Show license information:**
   ```bash
   python setup_license.py info
   ```

4. **Interactive mode:**
   ```bash
   python setup_license.py
   ```

## Common Issues and Solutions

### 1. License File Validation Failed

**Error:** `License validation error: Extra data: line 1 column 2 (char 1)`

**Solution:**
- Check that the license file contains valid JSON
- Remove any extra characters or formatting
- Use the validation command: `python setup_license.py validate`

### 2. License Key Not Found

**Error:** `License key not found: [key]`

**Solution:**
- Verify the license key in the JSON file matches your valid key
- Check that the license file exists in the correct location
- Recreate the license file using the setup utility

### 3. Google Sheets API Connection Issues

**Error:** Connection timeouts or authentication failures

**Solution:**
- Verify internet connectivity
- Check that `google_auth.json` exists and is valid
- Ensure the Google Sheets API is enabled
- Verify the spreadsheet ID is correct

### 4. Application Won't Start

**Error:** Application crashes during startup

**Solution:**
- Check the log file at `%APPDATA%\CRM_System\crm_license.log`
- Verify all required files are present
- Run the application with error logging enabled

## Limited Functionality Mode

If license validation fails, the application will run in "limited functionality mode":

- Basic features remain available
- Advanced features may be restricted
- License warnings will be displayed
- The application will continue to function

## Troubleshooting Steps

1. **Check License File:**
   ```bash
   python setup_license.py validate
   ```

2. **Recreate License File:**
   ```bash
   python setup_license.py create YOUR_LICENSE_KEY 2025-12-31
   ```

3. **Check Logs:**
   - Look for log files in `%APPDATA%\CRM_System\`
   - Check for error messages in the console

4. **Verify File Permissions:**
   - Ensure the application has write access to `%APPDATA%\CRM_System\`
   - Check that the license file is not read-only

5. **Network Connectivity:**
   - Verify internet connection for online license validation
   - Check firewall settings if using corporate network

## Support

If you continue to experience issues:

1. Check the log files for detailed error messages
2. Verify your license key is valid and not expired
3. Ensure all required files are present in the application directory
4. Contact support with the specific error messages from the logs

## File Structure

```
CRM_System/
├── main.py                 # Main application file
├── license_manager.py      # License validation logic
├── setup_license.py        # License setup utility
├── license.json           # Sample license file (local)
└── LICENSE_README.md      # This documentation
```

The actual license file is stored in `%APPDATA%\CRM_System\license.json` 