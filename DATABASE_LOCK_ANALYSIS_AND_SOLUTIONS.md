# Database Lock Analysis and Solutions

## **Root Causes of Database Locking Issues**

Your CRM application is experiencing database locks due to several critical issues in connection management:

### **1. Multiple Concurrent Connection Patterns**

**Problem**: Your application uses **inconsistent database connection patterns**:

- **Database class**: Maintains persistent connections via `self.conn`
- **Direct sqlite3.connect()**: Found 21+ instances in `views/customers.py` alone
- **Mixed patterns**: Some methods use Database class, others bypass it entirely

**Evidence**:
```python
# In Database class methods:
def add_customer(self):
    conn = self.get_connection()  # Uses persistent connection
    # ... operations ...
    self.close_connection()  # Closes persistent connection

# In view files (customers.py):
def _load_customers(self):
    with sqlite3.connect(self.db_path, timeout=10) as conn:  # Creates new connection
        # ... operations ...
```

### **2. Connection Lifecycle Issues**

**Problems**:
- Database class maintains persistent connection (`self.conn`) that's rarely properly closed
- Methods call `self.close_connection()` but immediately reopen connections in next operation
- WAL mode enabled but not properly managed across multiple connections
- No connection pooling or proper resource management

### **3. Threading Conflicts**

**Evidence**:
```python
# In backup_restore.py
def _start_schedule_thread(self):
    def run_schedule():
        while getattr(self, 'scheduler_running', True):
            schedule.run_pending()  # May trigger database operations
```

**Background threads** performing database operations concurrently with main thread.

### **4. Transaction Management Issues**

- Long-running transactions without proper timeout handling
- Nested transactions causing deadlocks
- Inconsistent commit/rollback patterns

## **Implemented Solutions**

### **1. Database Connection Context Manager**

Created `DatabaseConnectionContext` class in `database.py`:

```python
class DatabaseConnectionContext:
    """Context manager for safe database connections"""
    def __enter__(self):
        self.conn = sqlite3.connect(self.db_path, timeout=30)
        self.conn.execute("PRAGMA journal_mode=WAL")
        self.conn.execute("PRAGMA synchronous=NORMAL")
        self.conn.execute("PRAGMA busy_timeout=10000")  # 10 seconds
        self.conn.execute("PRAGMA temp_store=MEMORY")
        self.conn.execute("PRAGMA cache_size=10000")
        return self.conn
        
    def __exit__(self, exc_type, exc_val, exc_tb):
        if self.conn:
            if exc_type is None:
                self.conn.commit()
            else:
                self.conn.rollback()
            self.conn.close()
```

### **2. Database Utilities Module**

Created `database_utils.py` with standardized connection functions:

```python
@contextmanager
def get_db_connection(db_path=None, timeout=30):
    """Context manager for database connections with proper configuration"""
    conn = sqlite3.connect(db_path, timeout=timeout)
    # Optimal SQLite configuration for concurrency
    conn.execute("PRAGMA journal_mode=WAL")
    conn.execute("PRAGMA synchronous=NORMAL") 
    conn.execute("PRAGMA busy_timeout=10000")
    conn.execute("PRAGMA temp_store=MEMORY")
    conn.execute("PRAGMA cache_size=10000")
    conn.execute("PRAGMA foreign_keys=ON")
    
    try:
        yield conn
        conn.commit()
    except Exception:
        conn.rollback()
        raise
    finally:
        conn.close()
```

### **3. Updated Database Class Methods**

Modified key Database class methods to use context managers:

```python
def add_customer(self, user_name, name, phone, package_id, region, credit_balance=0):
    with self.get_connection_context() as conn:
        c = conn.cursor()
        # ... operations ...
        # Automatic commit/rollback and connection cleanup
```

## **Immediate Action Items**

### **1. Update All View Files**

Replace direct `sqlite3.connect()` calls with `get_db_connection()`:

**Before**:
```python
with sqlite3.connect(self.db_path, timeout=10) as conn:
```

**After**:
```python
from database_utils import get_db_connection
with get_db_connection() as conn:
```

### **2. Fix Threading Issues**

For background operations, use connection-per-thread pattern:

```python
def background_operation():
    with get_db_connection() as conn:
        # Database operations in separate thread
```

### **3. Implement Connection Pooling**

For high-concurrency scenarios, consider implementing connection pooling.

## **SQLite Configuration Optimizations**

The new connection pattern includes optimal SQLite settings:

- **WAL Mode**: Allows concurrent readers with single writer
- **Busy Timeout**: 10 seconds to handle temporary locks
- **Memory Temp Store**: Faster temporary operations
- **Increased Cache**: Better performance for repeated queries

## **Testing and Validation**

### **1. Connection Leak Detection**

Add logging to track connection lifecycle:

```python
logging.debug(f"Opening connection: {id(conn)}")
logging.debug(f"Closing connection: {id(conn)}")
```

### **2. Lock Monitoring**

Monitor for lock errors and retry patterns:

```python
except sqlite3.OperationalError as e:
    if "database is locked" in str(e):
        logging.warning(f"Database lock detected: {e}")
```

## **Next Steps**

1. **Update remaining view files** to use `database_utils.py`
2. **Test thoroughly** with concurrent operations
3. **Monitor logs** for remaining lock issues
4. **Consider connection pooling** for high-load scenarios
5. **Implement proper error handling** for lock scenarios

## **Files Modified**

- `database.py`: Added `DatabaseConnectionContext` and updated methods
- `database_utils.py`: New utility module for consistent connections
- `views/customers.py`: Updated `_load_customers()` method
- `login.py`: Updated login method

## **Files Requiring Updates**

- `views/register.py`: 1 direct connection
- `views/forgot_password.py`: Multiple direct connections
- `views/backup_restore.py`: Threading and connection issues
- All other view files with direct `sqlite3.connect()` calls
