import sqlite3

DB_PATH = 'crm_database.db'

with sqlite3.connect(DB_PATH) as conn:
    c = conn.cursor()
    c.execute("PRAGMA table_info(payment_history)")
    columns = [col[1] for col in c.fetchall()]
    if 'bill_id' not in columns:
        print('Adding bill_id column...')
        c.execute("ALTER TABLE payment_history ADD COLUMN bill_id INTEGER")
    else:
        print('bill_id column already exists.')
    if 'remaining_outstanding' not in columns:
        print('Adding remaining_outstanding column...')
        c.execute("ALTER TABLE payment_history ADD COLUMN remaining_outstanding REAL DEFAULT 0")
    else:
        print('remaining_outstanding column already exists.')
    if 'applied_credit' not in columns:
        print('Adding applied_credit column...')
        c.execute("ALTER TABLE payment_history ADD COLUMN applied_credit REAL DEFAULT 0")
    else:
        print('applied_credit column already exists.')
    conn.commit()
print('Migration complete.') 