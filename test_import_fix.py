#!/usr/bin/env python3
"""
Test script to verify the import billing calculation fix:
- Imported outstanding amount should be shown directly in Month Bill column
- Not package_price + imported_outstanding
"""

import sqlite3
import sys
import os
from datetime import datetime

# Add the current directory to the path so we can import our modules
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from database import get_db_connection

def test_import_scenario():
    """Test the import scenario with outstanding amount"""
    print("Testing import billing calculation fix...")
    
    with get_db_connection() as conn:
        c = conn.cursor()
        
        # Get a test customer
        c.execute("SELECT id, name FROM customers WHERE name LIKE '%Irfan%' LIMIT 1")
        customer = c.fetchone()
        if not customer:
            print("No test customer found. Please ensure Irfan <PERSON>war exists.")
            return
            
        customer_id, customer_name = customer
        print(f"Testing with customer: {customer_name} (ID: {customer_id})")
        
        # Get the billing record for this customer
        c.execute('''
            SELECT id, month, year, amount, outstanding_amount, credit_amount, status
            FROM billing 
            WHERE customer_id = ?
            ORDER BY year DESC, month DESC, id DESC
            LIMIT 1
        ''', (customer_id,))
        
        bill = c.fetchone()
        if not bill:
            print("No billing record found for this customer.")
            return
            
        bill_id, month, year, amount, outstanding, credit, status = bill
        
        print(f"\nBilling Record Analysis:")
        print(f"Bill ID: {bill_id}")
        print(f"Month/Year: {month:02d}/{year}")
        print(f"Package Amount: {amount} PKR")
        print(f"Outstanding Amount (stored): {outstanding} PKR")
        print(f"Credit Amount (stored): {credit} PKR")
        print(f"Status: {status}")
        
        # Test the Month Bill calculation
        from views.billing import BillingManager
        import tkinter as tk
        
        root = tk.Tk()
        root.withdraw()
        
        billing_manager = BillingManager(root, {})
        
        # Calculate what the Month Bill should show
        calculated_month_bill = billing_manager._calculate_month_bill_amount(
            c, customer_id, bill_id, amount, outstanding
        )
        
        print(f"\nMonth Bill Calculation:")
        print(f"Expected (for imported bill): {outstanding} PKR")
        print(f"Calculated: {calculated_month_bill} PKR")
        
        if calculated_month_bill == outstanding:
            print("✅ CORRECT: Month Bill shows imported outstanding amount directly")
        else:
            print("❌ INCORRECT: Month Bill calculation is wrong")
            print(f"   Should be {outstanding} PKR, but got {calculated_month_bill} PKR")
        
        # Check if this is the first bill for the customer
        c.execute('SELECT COUNT(*) FROM billing WHERE customer_id = ? AND id < ?', (customer_id, bill_id))
        previous_count = c.fetchone()[0]
        
        print(f"\nBill Analysis:")
        print(f"Previous bills for this customer: {previous_count}")
        print(f"Is first bill: {'Yes' if previous_count == 0 else 'No'}")
        print(f"Has outstanding: {'Yes' if outstanding > 0 else 'No'}")
        
        if previous_count == 0 and outstanding > 0:
            print("✅ This is an imported bill (first bill with outstanding)")
        else:
            print("ℹ️  This is not an imported bill")
        
        root.destroy()

def main():
    """Run the import fix test"""
    print("=== Testing Import Billing Calculation Fix ===\n")
    
    try:
        test_import_scenario()
        
        print("\n" + "="*50)
        print("Test completed!")
        print("\nExpected behavior for imported bills:")
        print("- Month Bill column should show imported outstanding amount directly")
        print("- NOT package_price + imported_outstanding")
        print("- Example: If imported outstanding is 1500 PKR, Month Bill should show 1500 PKR")
        
    except Exception as e:
        print(f"\n❌ Error during testing: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
