#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Create Update Package for License Keys
This script creates update packages that can be distributed to clients
"""

import os
import json
import argparse
from datetime import datetime, timedelta
from generate_license import generate_license

def create_update_package(customer_name, email, license_type="full", days_valid=365, 
                         activations=1000, bind_to_hardware=False, version="1.0.0", 
                         description="License key update"):
    """Create an update package with new license keys"""
    
    print("=" * 60)
    print("CREATING LICENSE UPDATE PACKAGE")
    print("=" * 60)
    
    # Generate the license key
    print(f"\n1. Generating license key...")
    license_key = generate_license(
        customer_name=customer_name,
        email=email,
        license_type=license_type,
        days_valid=days_valid,
        activations=activations,
        bind_to_hardware=bind_to_hardware
    )
    
    # Calculate expiry date
    expiry_date = (datetime.now() + timedelta(days=days_valid)).strftime("%Y-%m-%d")
    
    # Create update package
    print(f"\n2. Creating update package...")
    update_data = {
        "version": version,
        "date": datetime.now().isoformat(),
        "description": description,
        "requires_restart": False,
        "new_keys": [
            {
                "key": license_key,
                "name": f"Year {datetime.now().year + 1} License",
                "valid_until": expiry_date,
                "max_activations": activations,
                "description": f"License for {customer_name} ({email})"
            }
        ]
    }
    
    # Save update package
    update_filename = f"license_update_{version}.json"
    with open(update_filename, 'w') as f:
        json.dump(update_data, f, indent=2)
    
    print(f"✓ Update package created: {update_filename}")
    print(f"  Version: {version}")
    print(f"  License Key: {license_key}")
    print(f"  Valid Until: {expiry_date}")
    print(f"  Max Activations: {activations}")
    
    # Create distribution instructions
    print(f"\n3. Distribution Instructions:")
    print(f"   - Copy {update_filename} to client's app data directory:")
    print(f"     %APPDATA%\\CRM_System\\")
    print(f"   - Rename to: license_update.json")
    print(f"   - Restart the application")
    print(f"   - The application will automatically apply the update")
    
    return update_filename

def create_bulk_update_package(keys_info, version="1.0.0", description="Bulk license key update"):
    """Create an update package with multiple license keys"""
    
    print("=" * 60)
    print("CREATING BULK LICENSE UPDATE PACKAGE")
    print("=" * 60)
    
    update_data = {
        "version": version,
        "date": datetime.now().isoformat(),
        "description": description,
        "requires_restart": False,
        "new_keys": []
    }
    
    for i, key_info in enumerate(keys_info, 1):
        print(f"\n{i}. Generating license key for {key_info['customer_name']}...")
        
        license_key = generate_license(
            customer_name=key_info['customer_name'],
            email=key_info['email'],
            license_type=key_info.get('license_type', 'full'),
            days_valid=key_info.get('days_valid', 365),
            activations=key_info.get('activations', 1000),
            bind_to_hardware=key_info.get('bind_to_hardware', False)
        )
        
        expiry_date = (datetime.now() + timedelta(days=key_info.get('days_valid', 365))).strftime("%Y-%m-%d")
        
        update_data["new_keys"].append({
            "key": license_key,
            "name": f"Year {datetime.now().year + 1} License - {key_info['customer_name']}",
            "valid_until": expiry_date,
            "max_activations": key_info.get('activations', 1000),
            "description": f"License for {key_info['customer_name']} ({key_info['email']})"
        })
        
        print(f"   ✓ Key: {license_key}")
        print(f"   ✓ Expires: {expiry_date}")
    
    # Save update package
    update_filename = f"license_update_{version}.json"
    with open(update_filename, 'w') as f:
        json.dump(update_data, f, indent=2)
    
    print(f"\n✓ Bulk update package created: {update_filename}")
    print(f"  Version: {version}")
    print(f"  Total Keys: {len(update_data['new_keys'])}")
    
    return update_filename

def main():
    parser = argparse.ArgumentParser(description="Create license key update packages")
    parser.add_argument("--customer", required=True, help="Customer name")
    parser.add_argument("--email", required=True, help="Customer email")
    parser.add_argument("--type", default="full", choices=["full", "trial"], help="License type")
    parser.add_argument("--days", type=int, default=365, help="Validity period in days")
    parser.add_argument("--activations", type=int, default=1000, help="Number of allowed activations")
    parser.add_argument("--hardware", action="store_true", help="Bind license to hardware")
    parser.add_argument("--version", default="1.0.0", help="Update version")
    parser.add_argument("--description", default="License key update", help="Update description")
    parser.add_argument("--bulk", help="Bulk update JSON file with multiple customers")
    
    args = parser.parse_args()
    
    if args.bulk:
        # Bulk update from JSON file
        try:
            with open(args.bulk, 'r') as f:
                keys_info = json.load(f)
            
            create_bulk_update_package(keys_info, args.version, args.description)
        except Exception as e:
            print(f"Error reading bulk file: {str(e)}")
    else:
        # Single key update
        create_update_package(
            customer_name=args.customer,
            email=args.email,
            license_type=args.type,
            days_valid=args.days,
            activations=args.activations,
            bind_to_hardware=args.hardware,
            version=args.version,
            description=args.description
        )

if __name__ == "__main__":
    main() 