import tkinter as tk
from tkinter import messagebox, ttk
import datetime
import os
import sys
from PIL import Image, ImageTk
import logging

# Add project directory to sys.path to ensure module resolution
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from license_manager import LicenseManager
except ImportError as e:
    logging.error(f"Failed to import LicenseManager: {str(e)}")
    raise

try:
    from database import Database
except ImportError as e:
    logging.error(f"Failed to import Database: {str(e)}")
    raise

def resource_path(relative_path):
    """Get absolute path to resource, works for dev and for PyInstaller"""
    try:
        # PyInstaller creates a temp folder and stores path in _MEIPASS
        base_path = sys._MEIPASS
    except Exception:
        base_path = os.path.abspath(".")

    path = os.path.join(base_path, relative_path)
    path = os.path.normpath(path)  # Normalize path for different OS
    
    # Debugging - verify the path exists
    if not os.path.exists(path):
        logging.warning(f"Resource path not found: {path}")
    
    return path

class App(tk.Tk):
    def __init__(self, license_mgr):
        super().__init__()
        self.license_mgr = license_mgr
        self.title("CRM System")
        self.geometry("1922x1080")
        self.configure(bg='#F8F9FA')  # Updated to match login.py background

        # Define color scheme to match login.py and license_manager.py
        self.COLORS = {
            'background': '#F8F9FA',
            'card_bg': '#FFFFFF',
            'primary_accent': '#4A6FA5',
            'secondary_accent': '#6C8FC7',
            'text_primary': '#2D3748',
            'text_secondary': '#718096',
            'button_start': '#4A6FA5',
            'button_end': '#3A5A8C',
            'transparent': 'SystemTransparent',
            'warning': '#E53E3E',
            'input_bg': '#EDF2F7',
            'border': '#E2E8F0'
        }

        # Set favicon with resource_path
        self.set_favicon()

        # Initialize the database
        self.db = Database()
        self.db_path = self.db.db_path

        # Setup navigation commands
        self._setup_navigation()

        self.container = tk.Frame(self, bg=self.COLORS['background'])
        self.container.pack(fill="both", expand=True)

        self.current_page = None
        self.billing_page = None

        logging.info("Application initialized, showing login page")
        self.show_login()

    def set_favicon(self):
        """Try multiple possible favicon locations using resource_path"""
        icon_paths = [
            resource_path(os.path.join('assets', 'dashboard', 'CRM.png')),
            resource_path(os.path.join('assets', 'app_icon.ico')),
            # Fallback paths
            resource_path('CRM.png'),
            resource_path('assets/CRM.png')
        ]
        
        for icon_path in icon_paths:
            try:
                if os.path.exists(icon_path):
                    img = Image.open(icon_path)
                    self.favicon = ImageTk.PhotoImage(img)
                    self.iconphoto(True, self.favicon)
                    logging.info(f"Successfully loaded favicon from {icon_path}")
                    break
                else:
                    logging.warning(f"Favicon path does not exist: {icon_path}")
            except Exception as e:
                logging.warning(f"Couldn't load favicon from {icon_path}: {str(e)}")

    def _setup_navigation(self):
        """Initialize all navigation commands"""
        self.nav_commands = {
            'show_login': self.show_login,
            'show_register': self.show_register,
            'show_forgot_password': self.show_forgot_password,
            'show_email_config': self.show_email_config,
            'show_dashboard': self.show_dashboard,
            'show_customers': self.show_customers,
            'show_billing': self.show_billing,
            'show_packages': self.show_packages,
            'show_products': self.show_products,
            'show_regions': self.show_regions,
            'show_stock': self.show_stock,
            'show_backup_config': self.show_backup_config,
            'show_backup_restore': self.show_backup_restore,
            'refresh_billing': self.refresh_billing,
            'refresh_stock': self.refresh_stock
        }

    def _clear_frame(self):
        """Clear the current page"""
        if self.current_page:
            self.current_page.destroy()
        for widget in self.container.winfo_children():
            widget.destroy()

    def _show_error_message(self, title: str, message: str):
        """Show error message with styling matching login.py and license_manager.py"""
        dialog = tk.Toplevel(self)
        dialog.title(title)
        dialog.configure(bg=self.COLORS['background'])
        dialog.transient(self)
        dialog.grab_set()
        
        # Center the dialog
        dialog_width = 350
        dialog_height = 200
        screen_width = self.winfo_screenwidth()
        screen_height = self.winfo_screenheight()
        x = (screen_width - dialog_width) // 2
        y = (screen_height - dialog_height) // 2
        dialog.geometry(f"{dialog_width}x{dialog_height}+{x}+{y}")
        
        # Main frame
        main_frame = tk.Frame(dialog, bg=self.COLORS['card_bg'], padx=20, pady=20)
        main_frame.pack(fill="both", expand=True)

        # Error icon
        tk.Label(
            main_frame,
            text="⚠",
            font=("Helvetica", 24),
            fg=self.COLORS['warning'],
            bg=self.COLORS['card_bg']
        ).pack(pady=(0, 10))

        # Error message
        tk.Label(
            main_frame,
            text=message,
            font=("Helvetica", 12),
            fg=self.COLORS['text_primary'],
            bg=self.COLORS['card_bg'],
            wraplength=300
        ).pack(pady=(0, 20))

        # OK button
        tk.Button(
            main_frame,
            text="OK",
            font=("Helvetica", 12, "bold"),
            bg=self.COLORS['primary_accent'],
            fg="#FFFFFF",
            activebackground=self.COLORS['secondary_accent'],
            activeforeground="#FFFFFF",
            width=15,
            height=1,
            bd=0,
            relief="flat",
            command=lambda: dialog.destroy()
        ).pack()

        dialog.wait_window()

    def show_login(self):
        """Show login page"""
        logging.debug("Showing login page")
        try:
            from login import LoginPage
            self._clear_frame()
            self.current_page = LoginPage(self.container, self.nav_commands)
            self.current_page.pack(fill="both", expand=True)
            logging.info("Login page loaded successfully")
            # Schedule license expiry check after login, if license_mgr exists
            if self.license_mgr:
                self.license_mgr.check_expiry_reminder(self)
        except ImportError as e:
            error_msg = f"Failed to import LoginPage: {str(e)}"
            logging.error(error_msg)
            self._show_error_message("Error", error_msg)
        except Exception as e:
            error_msg = f"Failed to load login page: {str(e)}"
            logging.error(error_msg)
            self._show_error_message("Error", error_msg)

    def show_register(self):
        """Show registration page"""
        logging.debug("Showing register page")
        try:
            from views.register import Register
            self._clear_frame()
            self.current_page = Register(self.container, self.nav_commands)
            self.current_page.pack(fill="both", expand=True)
        except Exception as e:
            self._show_error_message("Error", f"Failed to load register page: {str(e)}")
            logging.error(f"Register page error: {str(e)}")

    def show_forgot_password(self):
        """Show password recovery page"""
        logging.debug("Showing forgot password page")
        try:
            from views.forgot_password import ForgotPassword
            self._clear_frame()
            self.current_page = ForgotPassword(self.container, self.nav_commands)
            self.current_page.pack(fill="both", expand=True)
        except Exception as e:
            self._show_error_message("Error", f"Failed to load forgot password page: {str(e)}")
            logging.error(f"Forgot password page error: {str(e)}")

    def show_email_config(self):
        """Show email configuration page"""
        logging.debug("Showing email config page")
        try:
            from views.email_config import EmailConfig
            self._clear_frame()
            self.current_page = EmailConfig(self.container, self.nav_commands)
            self.current_page.pack(fill="both", expand=True)
        except Exception as e:
            self._show_error_message("Error", f"Failed to load email config page: {str(e)}")
            logging.error(f"Email config page error: {str(e)}")

    def show_dashboard(self):
        """Show main dashboard"""
        logging.debug("Showing dashboard page")
        try:
            from views.dashboard import Dashboard
            self._clear_frame()
            self.current_page = Dashboard(self.container, self.nav_commands)
            self.current_page.pack(fill="both", expand=True)
            # Schedule license expiry check after dashboard load
            if self.license_mgr:
                self.license_mgr.check_expiry_reminder(self)
        except Exception as e:
            self._show_error_message("Error", f"Failed to load dashboard: {str(e)}")
            logging.error(f"Dashboard error: {str(e)}")

    def show_customers(self, customer_id=None):
        """Show customer management page"""
        logging.debug("Showing customers page")
        try:
            from views.customers import CustomerManager
            self._clear_frame()
            self.current_page = CustomerManager(self.container, self.nav_commands, customer_id=customer_id)
            self.current_page.pack(fill="both", expand=True)
        except Exception as e:
            self._show_error_message("Error", f"Failed to load customers page: {str(e)}")
            logging.error(f"Customers page error: {str(e)}")

    def show_billing(self, customer_id=None):
        """Show billing page"""
        logging.debug("Showing billing page")
        try:
            from views.billing import BillingManager
            self._clear_frame()
            self.current_page = BillingManager(self.container, self.nav_commands, customer_id=customer_id)
            self.billing_page = self.current_page
            self.current_page.pack(fill="both", expand=True)
        except Exception as e:
            self._show_error_message("Error", f"Failed to load billing page: {str(e)}")
            logging.error(f"Billing page error: {str(e)}")

    def show_packages(self):
        """Show packages page"""
        logging.debug("Showing packages page")
        try:
            from views.packages import PackageManager
            self._clear_frame()
            self.current_page = PackageManager(self.container, self.nav_commands)
            self.current_page.pack(fill="both", expand=True)
        except Exception as e:
            self._show_error_message("Error", f"Failed to load packages page: {str(e)}")
            logging.error(f"Packages page error: {str(e)}")

    def show_products(self):
        """Show products page"""
        logging.debug("Showing products page")
        try:
            from views.products import ProductManager
            self._clear_frame()
            self.current_page = ProductManager(self.container, self.nav_commands)
            self.current_page.pack(fill="both", expand=True)
        except Exception as e:
            self._show_error_message("Error", f"Failed to load products page: {str(e)}")
            logging.error(f"Products page error: {str(e)}")

    def show_regions(self):
        """Show regions page"""
        logging.debug("Showing regions page")
        try:
            from views.regions import RegionManager
            self._clear_frame()
            self.current_page = RegionManager(self.container, self.nav_commands)
            self.current_page.pack(fill="both", expand=True)
        except Exception as e:
            self._show_error_message("Error", f"Failed to load regions page: {str(e)}")
            logging.error(f"Regions page error: {str(e)}")

    def show_stock(self):
        """Show stock page"""
        logging.debug("Showing stock page")
        try:
            from views.stock import StockManager
            self._clear_frame()
            self.current_page = StockManager(self.container, self.nav_commands)
            self.current_page.pack(fill="both", expand=True)
        except Exception as e:
            self._show_error_message("Error", f"Failed to load stock page: {str(e)}")
            logging.error(f"Stock page error: {str(e)}")

    def show_backup_config(self):
        """Show backup config page"""
        logging.debug("Showing backup config page")
        try:
            from backup_config import BackupConfig
            self._clear_frame()
            self.current_page = BackupConfig(self.container, self.nav_commands)
            self.current_page.pack(fill="both", expand=True)
        except Exception as e:
            self._show_error_message("Error", f"Failed to load backup config page: {str(e)}")
            logging.error(f"Backup config page error: {str(e)}")

    def show_backup_restore(self):
        """Show backup/restore page"""
        logging.debug("Showing backup/restore page")
        try:
            from views.backup_restore import BackupRestore
            self._clear_frame()
            self.current_page = BackupRestore(self.container, self.nav_commands)
            self.current_page.pack(fill="both", expand=True)
        except Exception as e:
            self._show_error_message("Error", f"Failed to load backup/restore page: {str(e)}")
            logging.error(f"Backup/restore page error: {str(e)}")

    def refresh_billing(self):
        """Refresh billing page"""
        logging.debug("Refreshing billing page")
        if self.billing_page:
            customer_id = getattr(self.billing_page, 'customer_id', None)
            self.show_billing(customer_id=customer_id)

    def refresh_stock(self):
        """Refresh stock page"""
        logging.debug("Refreshing stock page")
        if hasattr(self.current_page, 'refresh_stock'):
            self.current_page.refresh_stock()

    def on_register_again(self):
        """Callback for license re-registration, navigates to login page and prompts for license key"""
        logging.info("Initiating re-registration process")
        self.show_login()

def setup_logging():
    """Configure logging for license validation"""
    log_dir = os.path.join(os.getenv('APPDATA', ''), 'CRM_System')
    os.makedirs(log_dir, exist_ok=True)
    log_path = os.path.join(log_dir, 'crm_license.log')
    
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_path),
            logging.StreamHandler()
        ])

def run_application():
    """Run the application with license validation"""
    setup_logging()
    
    # Special handling for compiled EXE
    if getattr(sys, 'frozen', False):
        os.chdir(os.path.dirname(sys.executable))
    
    # Initialize license manager with on_register_again callback
    app = App(None)  # Create the App instance first
    license_mgr = LicenseManager(on_register_again=lambda: app.on_register_again())  # Bind the callback to the instance
    
    # Pass the license manager to the app
    app.license_mgr = license_mgr
    
    # Enforce license validation
    if not license_mgr.enforce_license(app):
        logging.error("License validation failed. Exiting application.")
        app.destroy()
        return
    
    # Check for license expiry reminder after successful license enforcement
    license_mgr.check_expiry_reminder(app)
    
    # Start the application
    app.mainloop()

if __name__ == "__main__":
    run_application()
