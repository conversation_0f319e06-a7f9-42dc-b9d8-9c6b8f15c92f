import tkinter as tk
from tkinter import ttk, messagebox, filedialog
from datetime import datetime
from tkcalendar import Calendar
import logging
from PIL import Image, ImageTk
import os
import re
import pandas as pd
from database_utils import get_db_connection, execute_with_retry
from .PaymentReceipt import PaymentReceipt
from login import current_user
from resources import resource_path
from database import Database

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class Tooltip:
    def __init__(self, widget, text):
        self.widget = widget
        self.text = text
        self.tooltip = None
        self.widget.bind("<Enter>", self.enter)
        self.widget.bind("<Leave>", self.leave)
        self.widget.bind("<ButtonPress>", self.leave)

    def enter(self, event=None):
        self.tooltip = tk.Toplevel(self.widget)
        self.tooltip.wm_overrideredirect(True)
        self.tooltip.wm_attributes("-topmost", True)
        
        label = tk.Label(
            self.tooltip,
            text=self.text,
            background="#4A6FA5",
            relief="solid",
            borderwidth=1,
            font=("Helvetica", 10),
            fg="#FFFFFF"
        )
        label.pack()
        
        widget_x = self.widget.winfo_rootx()
        widget_y = self.widget.winfo_rooty()
        widget_width = self.widget.winfo_width()
        widget_height = self.widget.winfo_height()
        
        self.tooltip.update_idletasks()
        tooltip_width = self.tooltip.winfo_width()
        tooltip_height = self.tooltip.winfo_height()
        
        screen_width = self.widget.winfo_screenwidth()
        screen_height = self.widget.winfo_screenheight()
        
        x = widget_x + (widget_width - tooltip_width) // 2
        y = widget_y + widget_height + 5
        
        if x + tooltip_width > screen_width:
            x = screen_width - tooltip_width - 5
        elif x < 0:
            x = 5
            
        if y + tooltip_height > screen_height:
            y = widget_y - tooltip_height - 5
            if y < 0:
                y = 5
        
        self.tooltip.wm_geometry(f"+{int(x)}+{int(y)}")

    def leave(self, event=None):
        if self.tooltip:
            self.tooltip.destroy()
            self.tooltip = None

class BillingManager(tk.Frame):
    COLORS = {
        'background': '#F8F9FA',
        'card_bg': '#FFFFFF',
        'primary_accent': '#4A6FA5',
        'secondary_accent': '#6C8FC7',
        'text_primary': '#2D3748',
        'text_secondary': '#718096',
        'button_start': '#4A6FA5',
        'button_end': '#3A5A8C',
        'transparent': 'transparent',
        'warning': '#E53E3E',
        'input_bg': '#EDF2F7',
        'border': '#E2E8F0'
    }

    def __init__(self, parent, nav_commands, customer_id=None):
        super().__init__(parent)
        self.nav_commands = nav_commands
        self.customer_id = customer_id
        self.current_date = datetime.now()
        self.current_year = self.current_date.year
        self.current_month = self.current_date.month
        self.current_day = self.current_date.day
        self.payment_receipt = PaymentReceipt(self)
        self.specific_customer_view = customer_id is not None
        self.db = Database()  # Add database instance
        self.config(bg=self.COLORS['background'])
        self.pack(fill="both", expand=True)

        try:
            self._setup_ui()
            self._load_regions()
            self._load_customers()
            self._generate_missing_invoice_numbers()  # Generate invoice numbers for bills that don't have them
            self._load_bills()
        except Exception as e:
            logger.error(f"Initialization error: {str(e)}")
            messagebox.showerror("Error", f"Failed to initialize: {str(e)}")

    def _setup_ui(self):
        try:
            header = tk.Frame(self, bg=self.COLORS['primary_accent'], height=80)
            header.pack(fill="x", pady=(0, 20))

            def load_and_process_image(relative_path):
                try:
                    full_path = resource_path(relative_path)
                    if os.path.exists(full_path):
                        img = Image.open(full_path).convert("RGBA")
                        data = img.getdata()
                        new_data = []
                        for item in data:
                            if item[3] > 0:
                                new_data.append((255, 255, 255, item[3]))
                            else:
                                new_data.append(item)
                        img.putdata(new_data)
                        img = img.resize((30, 30), Image.Resampling.LANCZOS)
                        return ImageTk.PhotoImage(img)
                    return ImageTk.PhotoImage(Image.new('RGBA', (30, 30), (255, 255, 255, 0)))
                except Exception as e:
                    print(f"Error loading image: {str(e)}")
                    return ImageTk.PhotoImage(Image.new('RGBA', (30, 30), (255, 255, 255, 0)))

            self.dashboard_icon = load_and_process_image('assets/dashboard/billing/dashboard.png')
            self.customers_icon = load_and_process_image('assets/dashboard/billing/left-arrow.png')
            self.add_bill_icon = load_and_process_image('assets/dashboard/billing/addmanualbill.png')
            self.mark_paid_icon = load_and_process_image('assets/dashboard/billing/markpaid.png')
            self.delete_bill_icon = load_and_process_image('assets/dashboard/billing/deletebill.png')
            self.export_icon = load_and_process_image('assets/dashboard/billing/export.png')
            self.import_icon = load_and_process_image('assets/dashboard/billing/import.png')
            self.payment_history_icon = load_and_process_image('assets/dashboard/billing/payment_history.png')
            self.view_icon = load_and_process_image('assets/dashboard/billing/view.png')

            dashboard_btn = tk.Button(header, image=self.dashboard_icon, 
                                     command=self.nav_commands['show_dashboard'],
                                     bg=self.COLORS['primary_accent'], relief=tk.FLAT,
                                     activebackground=self.COLORS['secondary_accent'])
            dashboard_btn.pack(side="left", padx=10)
            Tooltip(dashboard_btn, "Dashboard")

            header_label = tk.Label(header, text="Billing Management", 
                                  font=("Helvetica", 24, "bold"),
                                  fg="#FFFFFF", bg=self.COLORS['primary_accent'])
            header_label.place(relx=0.5, rely=0.5, anchor="center")

            button_frame = tk.Frame(header, bg=self.COLORS['primary_accent'])
            button_frame.pack(side="right", padx=10)

            customers_btn = tk.Button(button_frame, image=self.customers_icon, 
                                    command=self.nav_commands['show_customers'],
                                    bg=self.COLORS['primary_accent'], relief=tk.FLAT,
                                    activebackground=self.COLORS['secondary_accent'])
            customers_btn.pack(side="left", padx=5)
            Tooltip(customers_btn, "Customers")

            add_bill_btn = tk.Button(button_frame, image=self.add_bill_icon, 
                                    command=self._add_manual_bill,
                                    bg=self.COLORS['primary_accent'], relief=tk.FLAT,
                                    activebackground=self.COLORS['secondary_accent'])
            add_bill_btn.pack(side="left", padx=5)
            Tooltip(add_bill_btn, "Add Manual Bill")

            self.mark_paid_btn = tk.Button(button_frame, image=self.mark_paid_icon, 
                                        command=self._mark_paid,
                                        bg=self.COLORS['primary_accent'], relief=tk.FLAT,
                                        activebackground=self.COLORS['secondary_accent'])
            self.mark_paid_btn.pack(side="left", padx=5)
            Tooltip(self.mark_paid_btn, "Pay Bill")

            delete_bill_btn = tk.Button(button_frame, image=self.delete_bill_icon, 
                                       command=self._delete_bill,
                                       bg=self.COLORS['primary_accent'], relief=tk.FLAT,
                                       activebackground=self.COLORS['secondary_accent'])
            delete_bill_btn.pack(side="left", padx=5)
            Tooltip(delete_bill_btn, "Delete Bill")

            export_btn = tk.Button(button_frame, image=self.export_icon,
                                 command=self._export_billing_data,
                                 bg=self.COLORS['primary_accent'], relief=tk.FLAT,
                                 activebackground=self.COLORS['secondary_accent'])
            export_btn.pack(side="left", padx=5)
            Tooltip(export_btn, "Export Billing Data")

            import_btn = tk.Button(button_frame, image=self.import_icon,
                                  command=self._import_billing_data,
                                  bg=self.COLORS['primary_accent'], relief=tk.FLAT,
                                  activebackground=self.COLORS['secondary_accent'])
            import_btn.pack(side="left", padx=5)
            Tooltip(import_btn, "Import Billing Data")

            view_btn = tk.Button(button_frame, image=self.view_icon,
                                command=self._view_selected_bill,
                                bg=self.COLORS['primary_accent'], relief=tk.FLAT,
                                activebackground=self.COLORS['secondary_accent'])
            view_btn.pack(side="left", padx=5)
            Tooltip(view_btn, "View Bill Details")

            content = tk.Frame(self, bg=self.COLORS['card_bg'])
            content.pack(fill="both", expand=True, padx=5, pady=5)

            filter_frame = tk.Frame(content, bg=self.COLORS['card_bg'])
            filter_frame.pack(fill="x", pady=(0, 5))

            tk.Label(filter_frame, text="Filter by Region:", bg=self.COLORS['card_bg'],
                     fg=self.COLORS['text_secondary'], font=("Helvetica", 10)).pack(side="left", padx=5)

            self.region_filter_var = tk.StringVar(value="All")
            self.region_filter_dropdown = ttk.Combobox(filter_frame, textvariable=self.region_filter_var,
                                                      state="readonly", font=("Helvetica", 12), width=20)
            self.region_filter_dropdown.pack(side="left", padx=5)
            self.region_filter_dropdown.bind("<<ComboboxSelected>>", lambda e: self._load_bills())

            tk.Label(filter_frame, text="Search Customer:", font=("Helvetica", 10),
                     bg=self.COLORS['card_bg'], fg=self.COLORS['text_secondary']).pack(side="left", padx=(10, 5))
            
            self.customer_search_var = tk.StringVar()
            if not self.specific_customer_view:
                self.customer_search_var.trace('w', self._on_search_change)
            
            self.customer_combobox = ttk.Combobox(filter_frame, textvariable=self.customer_search_var,
                                                font=("Helvetica", 12), width=20)
            self.customer_combobox.pack(side="left", padx=5)
            
            if not self.specific_customer_view:
                self.customer_combobox.bind("<<ComboboxSelected>>", lambda e: self._on_customer_selected())
            else:
                self.customer_combobox.config(state='disabled')

            tk.Label(filter_frame, text="Search by Paid Date:", font=("Helvetica", 10),
                     bg=self.COLORS['card_bg'], fg=self.COLORS['text_secondary']).pack(side="left", padx=(10, 5))
            
            self.paid_date_var = tk.StringVar()
            self.paid_date_entry = tk.Entry(filter_frame, textvariable=self.paid_date_var,
                                            font=("Helvetica", 12), width=20, state='readonly')
            self.paid_date_entry.pack(side="left", padx=5)
            self.paid_date_entry.bind("<Button-1>", self._show_calendar)
            self.paid_date_var.trace('w', lambda *args: self._load_bills())

            tk.Label(filter_frame, text="Filter:", font=("Helvetica", 10),
                     bg=self.COLORS['card_bg'], fg=self.COLORS['text_secondary']).pack(side="left", padx=(10, 0))
            self.filter_var = tk.StringVar(value="all")
            ttk.Radiobutton(filter_frame, text="All", variable=self.filter_var, value="all",
                            command=self._load_bills).pack(side="left", padx=5)
            ttk.Radiobutton(filter_frame, text="Paid", variable=self.filter_var, value="paid",
                            command=self._load_bills).pack(side="left", padx=5)
            ttk.Radiobutton(filter_frame, text="Unpaid", variable=self.filter_var, value="unpaid",
                            command=self._load_bills).pack(side="left", padx=5)

            tree_frame = tk.Frame(content, bg=self.COLORS['card_bg'])
            tree_frame.pack(fill="both", expand=True, pady=(0, 5))

            style = ttk.Style()
            style.configure("Treeview",
                            background=self.COLORS['card_bg'],
                            foreground=self.COLORS['text_secondary'],
                            fieldbackground=self.COLORS['card_bg'],
                            font=("Helvetica", 10),
                            rowheight=25,
                            borderwidth=1,
                            relief="solid",
                            bordercolor=self.COLORS['border'])
            style.configure("Treeview.Heading",
                            font=("Helvetica", 10, "bold"),
                            foreground=self.COLORS['text_secondary'],
                            background=self.COLORS['input_bg'],
                            relief="flat",
                            padding=5)
            style.map("Treeview",
                      background=[('selected', self.COLORS['primary_accent'])],
                      foreground=[('selected', '#FFFFFF')])

            scrollbar = ttk.Scrollbar(tree_frame, orient=tk.VERTICAL)
            scrollbar.pack(side="right", fill="y")

            # In _setup_ui, define columns as a tuple with 'total_paid' after 'paid_amount' and before 'status'
            columns = (
                "id", "invoice", "customer", "region", "package", "package_price", "month",
                "amount", "paid_amount", "total_paid", "status", "paid_date",
                "customer_status", "paid_by", "balance", "credit", "view_history"
            )
            self.tree = ttk.Treeview(
                tree_frame,
                columns=columns,
                selectmode="browse",
                show="headings",
                yscrollcommand=scrollbar.set,
                style="Treeview"
            )
            # Set up headings and columns
            headings = [
                ("id", "Bill ID", 50, "center"),
                ("invoice", "Invoice #", 90, "center"),
                ("customer", "Customer", 110, "w"),
                ("region", "Region", 70, "center"),
                ("package", "Package", 90, "center"),
                ("package_price", "Package Price", 90, "center"),
                ("month", "Month", 65, "center"),
                ("amount", "Month Bill", 80, "center"),
                ("paid_amount", "Paid Amount", 90, "center"),
                ("total_paid", "Total Paid", 90, "center"),
                ("status", "Status", 70, "center"),
                ("paid_date", "Paid Date", 80, "center"),
                ("customer_status", "Suspend", 60, "center"),
                ("paid_by", "Paid By", 90, "center"),
                ("balance", "Outstanding", 90, "center"),
                ("credit", "Credit", 100, "center"),
                ("view_history", "", 40, "center")
            ]
            for col_id, heading, width, anchor in headings:
                self.tree.heading(col_id, text=heading, anchor=anchor)
                self.tree.column(col_id, width=width, anchor=anchor, minwidth=width)

            self.tree.pack(fill="both", expand=True)



        except Exception as e:
            logger.error(f"Error in UI setup: {str(e)}")
            messagebox.showerror("Error", f"Failed to set up UI: {str(e)}")

    def _show_calendar(self, event):
        def on_date_select():
            selected_date = cal.get_date()
            try:
                parsed_date = datetime.strptime(selected_date, '%m/%d/%y').strftime('%Y-%m-%d')
                self.paid_date_var.set(parsed_date)
            except ValueError:
                messagebox.showerror("Error", "Invalid date format selected")
            top.destroy()

        top = tk.Toplevel(self)
        top.transient(self)
        top.grab_set()
        top.title("Select Paid Date")
        top.geometry("300x300")
        
        top.update_idletasks()
        width = top.winfo_width()
        height = top.winfo_height()
        x = (top.winfo_screenwidth() // 2) - (width // 2)
        y = (top.winfo_screenheight() // 2) - (height // 2)
        top.geometry(f"{width}x{height}+{x}+{y}")

        cal = Calendar(top, selectmode='day', date_pattern='mm/dd/yy')
        cal.pack(padx=10, pady=10, fill="both", expand=True)

        tk.Button(top, text="Select", command=on_date_select, bg=self.COLORS['primary_accent'], fg="#FFFFFF",
                  font=("Helvetica", 12, "bold"), relief=tk.FLAT, padx=10, pady=5,
                  activebackground=self.COLORS['secondary_accent'], activeforeground="#FFFFFF").pack(pady=5)

    def _load_regions(self):
        try:
            with get_db_connection() as conn:
                cursor = conn.cursor()
                cursor.execute("SELECT name FROM regions ORDER BY name")
                self.regions = [row[0] for row in cursor.fetchall()]
                self.region_filter_dropdown['values'] = ['All'] + self.regions
                if not self.region_filter_var.get():
                    self.region_filter_var.set('All')
        except Exception as e:
            logger.error(f"Error loading regions: {str(e)}")
            messagebox.showerror("Error", f"Failed to load regions: {str(e)}")
            self.regions = []

    def _on_search_change(self, *args):
        search_term = self.customer_search_var.get().lower()
        if not hasattr(self, 'customer_names'):
            return

        if not search_term:
            self.customer_combobox['values'] = self.customer_names
            self._load_bills()
        else:
            filtered = [name for name in self.customer_names if name.lower().startswith(search_term)]
            self.customer_combobox['values'] = filtered
            # Also reload bills for filtered customer if exact match
            if search_term in [name.lower() for name in self.customer_names]:
                self._load_bills()

    def _on_customer_selected(self):
        self._load_bills()

    def _load_customers(self):
        try:
            with get_db_connection() as conn:
                cursor = conn.cursor()
                # Include both active and inactive customers, ordered by name
                cursor.execute("SELECT id, name FROM customers ORDER BY name")
                self.customers = {name: id for id, name in cursor.fetchall()}
                self.customer_names = sorted(self.customers.keys())
                self.customer_combobox['values'] = self.customer_names

                if self.customer_id is not None:
                    cursor.execute("SELECT name FROM customers WHERE id = ?", (self.customer_id,))
                    customer_name = cursor.fetchone()
                    if customer_name:
                        self.customer_search_var.set(customer_name[0])
        except Exception as e:
            logger.error(f"Error loading customers: {str(e)}")
            messagebox.showerror("Error", f"Failed to load customers: {str(e)}")
            self.customers = {}
            self.customer_names = []

    def calculate_customer_balance(self, customer_id):
        """Calculate total outstanding and credit for a customer from billing records"""
        with get_db_connection() as conn:
            c = conn.cursor()
            
            # Calculate total outstanding from unpaid bills
            c.execute('''
                SELECT COALESCE(SUM(outstanding_amount), 0) 
                FROM billing 
                WHERE customer_id = ? AND status != 'Paid'
            ''', (customer_id,))
            outstanding = c.fetchone()[0] or 0.0
            
            # Calculate total credit from paid bills
            c.execute('''
                SELECT COALESCE(SUM(credit_amount), 0)
                FROM billing
                WHERE customer_id = ? AND status = 'Paid'
            ''', (customer_id,))
            credit = c.fetchone()[0] or 0.0
            
            return outstanding, credit

    def sync_customer_financials(self, customer_id):
        """Update customer's financial summary based on billing records"""
        with get_db_connection() as conn:
            c = conn.cursor()
            
            # Calculate total outstanding from unpaid bills
            c.execute('''
                SELECT COALESCE(SUM(outstanding_amount), 0) 
                FROM billing 
                WHERE customer_id = ? AND status != 'Paid'
            ''', (customer_id,))
            outstanding = c.fetchone()[0] or 0.0
            
            # Calculate total credit from billing records
            c.execute('''
                SELECT COALESCE(SUM(credit_amount), 0)
                FROM billing
                WHERE customer_id = ? AND status = 'Paid'
            ''', (customer_id,))
            credit = c.fetchone()[0] or 0.0
            
            # Update customer record
            c.execute('''
                UPDATE customers 
                SET outstanding_amount = ?, credit_balance = ?
                WHERE id = ?
            ''', (outstanding, credit, customer_id))
            conn.commit()

    def _load_bills(self):
        try:
            with get_db_connection() as conn:
                cursor = conn.cursor()
                # Clear existing tree entries
                for item in self.tree.get_children():
                    self.tree.delete(item)
                # Build query with filters - include inactive customers
                query = '''
                    SELECT b.id, b.invoice_number, c.name, c.region, b.month, b.year, b.amount,
                           b.paid_amount, b.status, b.paid_date, p.name as package_name,
                           c.id as customer_id, c.status as customer_status, b.paid_by, b.is_manual,
                           b.outstanding_amount, b.credit_amount, b.month_bill_amount
                    FROM billing b
                    JOIN customers c ON b.customer_id = c.id
                    LEFT JOIN packages p ON c.package_id = p.id
                    WHERE 1=1
                '''
                conditions = []
                params = []
                selected_region = self.region_filter_var.get()
                if selected_region != "All":
                    conditions.append("c.region = ?")
                    params.append(selected_region)
                customer_name = self.customer_search_var.get()
                if customer_name and customer_name in self.customers and self.customer_combobox.get() == customer_name:
                    conditions.append("c.id = ?")
                    params.append(self.customers[customer_name])
                if self.filter_var.get() == "paid":
                    conditions.append("b.status = 'Paid'")
                elif self.filter_var.get() == "unpaid":
                    conditions.append("b.status != 'Paid'")
                selected_paid_date = self.paid_date_var.get()
                if selected_paid_date:
                    conditions.append("b.paid_date = ?")
                    params.append(selected_paid_date)
                if conditions:
                    query += " AND " + " AND ".join(conditions)
                query += " ORDER BY b.year DESC, b.month DESC, b.id DESC"
                cursor.execute(query, params)
                bills = cursor.fetchall()
                # Populate the treeview
                for bill in bills:
                    bill_id = str(bill[0]).zfill(4)
                    invoice_number = bill[1] if bill[1] else ""
                    customer_name = bill[2]
                    region = bill[3] if bill[3] else "N/A"
                    package_name = bill[10]
                    customer_id = bill[11]
                    customer_status = 'Active' if bill[12] == 1 else 'Inactive'
                    bill_month = bill[4]
                    bill_year = bill[5]
                    paid_by = bill[13] if bill[13] else ""
                    paid_amount = bill[7] if bill[7] else 0.0
                    manual_amount = bill[6] if bill[6] else 0.0
                    is_manual = bill[14] if len(bill) > 14 else 0
                    outstanding_amount = bill[15] if len(bill) > 15 else 0.0
                    credit_amount = bill[16] if len(bill) > 16 else 0.0
                    stored_month_bill_amount = bill[17] if len(bill) > 17 else 0.0
                    status = bill[8] if bill[8] else "Unpaid"
                    paid_date = bill[9] if bill[8] in ('Paid', 'Partially Paid') and bill[9] else ""
                    if is_manual == 1:
                        base_amount = manual_amount
                    else:
                        base_amount = bill[6] if bill[6] else 0.0
                    # Get package price for regular bills, but use actual amount for manual bills and product purchases
                    if is_manual == 1:
                        # For manual bills, use the manual amount as the "package price"
                        package_price = manual_amount
                    elif purchased_products:
                        # For product purchases, sum up the product prices
                        cursor.execute('''
                            SELECT SUM(cp.price) FROM customer_purchases cp
                            WHERE cp.billing_id = ?
                        ''', (bill[0],))
                        product_total = cursor.fetchone()[0]
                        package_price = product_total if product_total else manual_amount
                    else:
                        # For regular package bills, use the package price
                        cursor.execute('SELECT price FROM packages WHERE name = ?', (package_name,))
                        pkg_price_result = cursor.fetchone()
                        package_price = pkg_price_result[0] if pkg_price_result else 0.0
                    # --- Fetch display values from payment_history table (latest payment for this bill) ---
                    cursor.execute('''
                        SELECT credit_amount, outstanding_amount
                        FROM payment_history
                        WHERE bill_id = ?
                        ORDER BY timestamp DESC
                        LIMIT 1
                    ''', (bill[0],))
                    history_result = cursor.fetchone()

                    if history_result:
                        display_credit = history_result[0] or 0.0
                        display_outstanding = history_result[1] or 0.0
                    else:
                        # Fallback to billing table if no payment history
                        display_credit = credit_amount or 0.0
                        display_outstanding = outstanding_amount or 0.0

                    # Also get customer's current credit balance and add it to display
                    cursor.execute('''
                        SELECT credit_balance FROM customers WHERE id = ?
                    ''', (customer_id,))
                    customer_credit_result = cursor.fetchone()
                    customer_credit_balance = customer_credit_result[0] if customer_credit_result else 0.0

                    # Show total credit (bill credit + customer credit balance)
                    total_credit = display_credit + customer_credit_balance

                    outstanding_display = f"{display_outstanding:.2f}" if display_outstanding > 0 else ""
                    credit_display = f"{total_credit:.2f}" if total_credit > 0 else ""

                    # Use stored month_bill_amount if available, otherwise calculate and store it
                    if stored_month_bill_amount and stored_month_bill_amount > 0:
                        month_bill_amount = stored_month_bill_amount
                    else:
                        # Calculate Month Bill amount and store it for future use
                        month_bill_amount = self._calculate_month_bill_amount(cursor, customer_id, bill_id, bill[6], outstanding_amount)
                        # Store the calculated amount in the database
                        cursor.execute('''
                            UPDATE billing SET month_bill_amount = ? WHERE id = ?
                        ''', (month_bill_amount, bill_id))

                    # Fetch purchased products for this bill
                    cursor.execute('''
                        SELECT pr.name, pr.id FROM customer_purchases cp
                        JOIN products pr ON cp.product_id = pr.id
                        WHERE cp.billing_id = ?
                    ''', (bill[0],))
                    purchased_products = cursor.fetchall()
                    # Determine package display
                    if is_manual == 1:
                        display_package = "Manual Bill"
                    elif purchased_products:
                        product_names = [name for name, _ in purchased_products]
                        display_package = ', '.join(product_names)
                    else:
                        display_package = package_name
                    # Fetch cumulative total paid by this customer across all bills
                    cursor.execute('''SELECT COALESCE(SUM(amount_paid), 0) FROM payment_history WHERE customer_id = ?''', (customer_id,))
                    customer_total_paid = cursor.fetchone()[0] or 0.0
                    # Insert into treeview - show inactive customers with different styling
                    if customer_status == 'Inactive':
                        self.tree.insert("", "end", values=(
                            bill_id, 
                            invoice_number, 
                            customer_name, 
                            region, 
                            display_package,
                            f"{package_price:.2f}",
                            f"{bill[4]:02d}/{bill[5]}", 
                            f"{month_bill_amount:.2f}",
                            f"{paid_amount:.2f}" if paid_amount else "0.00",
                            f"{customer_total_paid:.2f}",
                            status, 
                            paid_date, 
                            customer_status, 
                            paid_by,
                            outstanding_display,
                            credit_display,
                            ''
                        ), tags=("inactive",))
                        self.tree.set(self.tree.get_children()[-1], 'view_history', '')
                        self.tree.item(self.tree.get_children()[-1], image=self.payment_history_icon)
                        self.tree.tag_bind(self.tree.get_children()[-1], '<Button-1>', lambda e, iid=self.tree.get_children()[-1], inv=invoice_number: self._show_payment_history_popup(inv))
                        # Add view button functionality
                        self.tree.tag_bind(self.tree.get_children()[-1], '<Button-3>', lambda e, iid=self.tree.get_children()[-1], bid=bill[0]: self._show_bill_details_popup(bid))
                    else:
                        self.tree.insert("", "end", values=(
                            bill_id, 
                            invoice_number, 
                            customer_name, 
                            region, 
                            display_package,
                            f"{package_price:.2f}",
                            f"{bill[4]:02d}/{bill[5]}", 
                            f"{month_bill_amount:.2f}",
                            f"{paid_amount:.2f}" if paid_amount else "0.00",
                            f"{customer_total_paid:.2f}",
                            status, 
                            paid_date, 
                            customer_status, 
                            paid_by,
                            outstanding_display,
                            credit_display,
                            ''
                        ))
                        self.tree.set(self.tree.get_children()[-1], 'view_history', '')
                        self.tree.item(self.tree.get_children()[-1], image=self.payment_history_icon)
                        self.tree.tag_bind(self.tree.get_children()[-1], '<Button-1>', lambda e, iid=self.tree.get_children()[-1], inv=invoice_number: self._show_payment_history_popup(inv))
                        # Add view button functionality
                        self.tree.tag_bind(self.tree.get_children()[-1], '<Button-3>', lambda e, iid=self.tree.get_children()[-1], bid=bill[0]: self._show_bill_details_popup(bid))
                # Configure styling for inactive rows
                self.tree.tag_configure("inactive", background="#FDF6BE")
                
                # No need for individual view buttons since we have a header button
        except Exception as e:
            logger.error(f"Error loading bills: {str(e)}")
            messagebox.showerror("Error", f"Failed to load bills: {str(e)}")

    def _view_selected_bill(self):
        """View details of the selected bill"""
        selected = self.tree.selection()
        if not selected:
            messagebox.showwarning("Warning", "Please select a bill to view details")
            return
        
        try:
            bill_id = int(self.tree.item(selected[0])['values'][0])
            self._show_bill_details_popup(bill_id)
        except Exception as e:
            logger.error(f"Error viewing selected bill: {str(e)}")
            messagebox.showerror("Error", f"Failed to view bill details: {str(e)}")



    def _generate_invoice_number(self):
        try:
            with get_db_connection() as conn:
                cursor = conn.cursor()
                cursor.execute("SELECT MAX(CAST(SUBSTR(invoice_number, 5) AS INTEGER)) FROM billing WHERE invoice_number IS NOT NULL")
                max_inv_num = cursor.fetchone()[0] or 0
                return f"INV-{str(max_inv_num + 1).zfill(4)}"
        except Exception as e:
            logger.error(f"Error generating invoice number: {str(e)}")
            return f"INV-{str(1).zfill(4)}"

    def _generate_import_invoice_number(self):
        try:
            with get_db_connection() as conn:
                cursor = conn.cursor()
                cursor.execute("SELECT MAX(CAST(SUBSTR(invoice_number, 5) AS INTEGER)) FROM billing WHERE invoice_number LIKE 'IMP-%'")
                max_inv_num = cursor.fetchone()[0] or 0
                return f"IMP-{str(max_inv_num + 1).zfill(4)}"
        except Exception as e:
            logger.error(f"Error generating import invoice number: {str(e)}")
            return f"IMP-{str(1).zfill(4)}"

    def _mark_paid(self):
        selected = self.tree.selection()
        if not selected:
            messagebox.showwarning("Warning", "Please select a bill to mark as paid")
            return

        bill_id = int(self.tree.item(selected[0])['values'][0])
        
        try:
            with get_db_connection() as conn:
                cursor = conn.cursor()
                
                # Get customer ID and unpaid bills with enhanced outstanding calculation
                cursor.execute("SELECT customer_id FROM billing WHERE id = ?", (bill_id,))
                customer_id = cursor.fetchone()[0]
                
                # Get customer's current financial status including imported amounts
                cursor.execute('''
                    SELECT outstanding_amount, credit_balance 
                    FROM customers 
                    WHERE id = ?
                ''', (customer_id,))
                customer_financials = cursor.fetchone()
                customer_outstanding = customer_financials[0] if customer_financials and customer_financials[0] else 0.0
                customer_credit = customer_financials[1] if customer_financials and customer_financials[1] else 0.0
                
                cursor.execute('''
                    SELECT b.id, b.month, b.year,
                           b.amount - COALESCE(b.paid_amount, 0) as pending,
                           b.amount as original_amount,
                           b.is_manual, b.outstanding_amount, b.credit_amount, b.month_bill_amount
                    FROM billing b
                    WHERE b.customer_id = ? AND (b.amount - COALESCE(b.paid_amount, 0)) > 0
                    ORDER BY b.year, b.month ASC
                ''', (customer_id,))
                unpaid_bills = cursor.fetchall()

                if not unpaid_bills:
                    messagebox.showinfo("Info", "No pending bills found")
                    return

                # Calculate total payable - use the actual unpaid amounts, not recalculated month_bill_amounts
                total_payable = 0.0
                enhanced_unpaid_bills = []

                for bill in unpaid_bills:
                    bill_id, month, year, pending, original_amount, is_manual, outstanding_amount, credit_amount, stored_month_bill_amount = bill

                    # For payment calculation, use the actual pending amount (what's still owed on this bill)
                    # This is the amount - paid_amount, which is the 'pending' field
                    payable_amount = pending

                    # Use stored month_bill_amount for display purposes
                    if stored_month_bill_amount and stored_month_bill_amount > 0:
                        display_month_bill_amount = stored_month_bill_amount
                    else:
                        # For display, use the original amount (this is what should be shown in the dialog)
                        display_month_bill_amount = original_amount
                        # Store it for future reference
                        cursor.execute('''
                            UPDATE billing SET month_bill_amount = ? WHERE id = ?
                        ''', (original_amount, bill_id))

                    # Use pending amount for total payable calculation (what user actually needs to pay)
                    total_payable += payable_amount

                    # Store enhanced bill info with display amount
                    enhanced_unpaid_bills.append((
                        bill_id, month, year, payable_amount, original_amount,
                        is_manual, outstanding_amount, credit_amount
                    ))

                # Use enhanced bills list for payment processing
                unpaid_bills = enhanced_unpaid_bills

                # No need to add additional outstanding since Month Bill amounts already include it
                
                # Show payment dialog with enhanced total calculation
                self._show_payment_dialog(customer_id, unpaid_bills, total_payable, customer_credit)
                
        except Exception as e:
            logger.error(f"Error in payment dialog: {str(e)}")
            messagebox.showerror("Error", f"Failed to process payment: {str(e)}")

    def _show_payment_dialog(self, customer_id, unpaid_bills, total_payable, customer_credit=0.0):
        payment_dialog = tk.Toplevel(self)
        payment_dialog.title("Payment Processing")
        payment_dialog.geometry("600x500")
        payment_dialog.transient(self)
        payment_dialog.grab_set()
        
        # Center the dialog
        payment_dialog.update_idletasks()
        width = payment_dialog.winfo_width()
        height = payment_dialog.winfo_height()
        x = (payment_dialog.winfo_screenwidth() // 2) - (width // 2)
        y = (payment_dialog.winfo_screenheight() // 2) - (height // 2)
        payment_dialog.geometry(f"{width}x{height}+{x}+{y}")
        
        main_frame = tk.Frame(payment_dialog, bg=self.COLORS['card_bg'], 
                            highlightbackground=self.COLORS['border'], 
                            highlightthickness=2, padx=20, pady=20)
        main_frame.pack(fill="both", expand=True)
        
        tk.Label(main_frame, text="Pending Bills Breakdown", 
                font=("Helvetica", 14, "bold"), 
                bg=self.COLORS['card_bg'], fg=self.COLORS['primary_accent']).pack(pady=10)
        
        # Create treeview for bills
        columns = ("Month", "Bill Type", "Amount", "Status")
        tree = ttk.Treeview(main_frame, columns=columns, show="headings", height=5)
        for col in columns:
            tree.heading(col, text=col)
            tree.column(col, width=120, anchor="center")
        
        for bill in unpaid_bills:
            bill_id, month, year, month_bill_amount, original_amount, is_manual, outstanding_amount, credit_amount = bill
            bill_type = "Manual" if is_manual else "Package"
            tree.insert("", "end", values=(
                f"{month:02d}/{year}",
                bill_type,
                f"{month_bill_amount:.2f}",  # Show Month Bill amount instead of pending
                "Unpaid"
            ))
        
        tree.pack(fill="x", pady=10)
        
        # Enhanced total payable label with credit information
        total_info = f"Total Payable Amount: {total_payable:.2f} PKR"
        if customer_credit > 0:
            total_info += f"\nAvailable Credit: {customer_credit:.2f} PKR"
            total_info += f"\nNet Payable: {max(0, total_payable - customer_credit):.2f} PKR"
        
        tk.Label(main_frame, text=total_info, 
                font=("Helvetica", 12, "bold"), 
                bg=self.COLORS['card_bg'], fg=self.COLORS['text_primary']).pack(pady=5)
        
        # Payment entry
        payment_container = tk.Frame(main_frame, bg=self.COLORS['card_bg'])
        payment_container.pack(pady=10)
        
        tk.Label(payment_container, text="Payment Amount:", 
                bg=self.COLORS['card_bg'], font=("Helvetica", 10), 
                fg=self.COLORS['text_primary']).pack(side="left", padx=5)
        
        payment_var = tk.StringVar()
        payment_entry = tk.Entry(payment_container, textvariable=payment_var, 
                               font=("Helvetica", 10), width=15)
        payment_entry.pack(side="left", padx=5)
        
        # Buttons
        button_container = tk.Frame(main_frame, bg=self.COLORS['card_bg'])
        button_container.pack(pady=10)
        
        button_subframe = tk.Frame(button_container, bg=self.COLORS['card_bg'])
        button_subframe.pack()
        
        # Pay full button (considering credit)
        net_payable = max(0, total_payable - customer_credit)
        pay_full_btn = tk.Button(button_subframe, text="Pay Full", 
                               font=("Helvetica", 10, "bold"),
                               command=lambda: payment_var.set(str(net_payable)),
                               bg=self.COLORS['primary_accent'], fg="#FFFFFF",
                               relief=tk.FLAT, padx=20, pady=5,
                               activebackground=self.COLORS['secondary_accent'], 
                               activeforeground="#FFFFFF")
        pay_full_btn.pack(side="left", padx=10)
        
        # Process payment button
        process_btn = tk.Button(button_subframe, text="Process Payment", 
                              font=("Helvetica", 10, "bold"),
                              command=lambda: self._process_payment(
                                  payment_var, unpaid_bills, total_payable, 
                                  payment_dialog, customer_id, customer_credit),
                              bg=self.COLORS['primary_accent'], fg="#FFFFFF",
                              relief=tk.FLAT, padx=20, pady=5,
                              activebackground=self.COLORS['secondary_accent'], 
                              activeforeground="#FFFFFF")
        process_btn.pack(side="left", padx=10)

    def _process_payment(self, payment_var, unpaid_bills, total_payable, payment_dialog, customer_id, customer_credit=0.0):
        try:
            payment_amount = float(payment_var.get())
            if payment_amount <= 0:
                raise ValueError("Payment amount must be positive")

            recorded_by = current_user or "System"
            remaining_payment = payment_amount + customer_credit

            # Use database connection context manager for transaction safety
            with get_db_connection() as conn:
                try:
                    conn.execute("BEGIN")

                    for bill in unpaid_bills:
                        bill_id = bill[0]
                        month_bill_amount = bill[3]  # This is now the Month Bill amount
                        if remaining_payment <= 0:
                            break
                        pay_amount = min(remaining_payment, month_bill_amount)

                        # Process payment using direct database operations
                        cursor = conn.cursor()

                        # Get current bill state
                        cursor.execute('''
                            SELECT amount, paid_amount, outstanding_amount, credit_amount
                            FROM billing WHERE id = ?
                        ''', (bill_id,))
                        bill_data = cursor.fetchone()
                        if not bill_data:
                            raise ValueError(f"Bill {bill_id} not found")

                        # Calculate new values - use month_bill_amount for proper calculation
                        _, current_paid, _, _ = bill_data
                        new_paid = (current_paid or 0) + pay_amount

                        # Calculate outstanding and credit for this specific bill
                        # Outstanding = what's still owed on this bill
                        new_outstanding = max(month_bill_amount - new_paid, 0)
                        # Credit for this bill = excess payment on this bill only
                        bill_credit = max(new_paid - month_bill_amount, 0)
                        new_status = 'Paid' if new_outstanding == 0 else 'Partially Paid'

                        # Update billing record - store only final amounts for this bill
                        cursor.execute('''
                            UPDATE billing SET
                            paid_amount = ?,
                            outstanding_amount = ?,
                            credit_amount = ?,
                            status = ?,
                            paid_date = CURRENT_TIMESTAMP,
                            paid_by = ?
                            WHERE id = ?
                        ''', (new_paid, new_outstanding, bill_credit, new_status, recorded_by, bill_id))

                        # Clear previous bills' outstanding/credit amounts since this is the latest bill
                        self._update_previous_bills_amounts(cursor, customer_id, bill_id)

                        # Get or generate invoice number
                        cursor.execute('''
                            SELECT invoice_number FROM billing WHERE id = ?
                        ''', (bill_id,))
                        invoice_result = cursor.fetchone()
                        invoice_number = invoice_result[0] if invoice_result and invoice_result[0] else None

                        # Generate invoice number if it doesn't exist
                        if not invoice_number:
                            cursor.execute("SELECT MAX(CAST(SUBSTR(invoice_number, 5) AS INTEGER)) FROM billing WHERE invoice_number IS NOT NULL AND invoice_number LIKE 'INV-%'")
                            max_inv_num = cursor.fetchone()[0] or 0
                            invoice_number = f"INV-{str(max_inv_num + 1).zfill(4)}"

                            # Update the billing record with the invoice number
                            cursor.execute('''
                                UPDATE billing SET invoice_number = ? WHERE id = ?
                            ''', (invoice_number, bill_id))

                        # Record in payment history with proper credit and outstanding amounts
                        cursor.execute('''
                            INSERT INTO payment_history
                            (bill_id, invoice_number, customer_id, payment_date, amount_paid,
                             credit_amount, outstanding_amount, remaining_outstanding, applied_credit,
                             payment_method, recorded_by, timestamp)
                            VALUES (?, ?, ?, CURRENT_TIMESTAMP, ?, ?, ?, ?, ?, ?, ?, datetime('now'))
                        ''', (bill_id, invoice_number, customer_id, pay_amount, bill_credit, new_outstanding, new_outstanding, bill_credit, None, recorded_by))

                        remaining_payment -= pay_amount

                    # Handle excess payment (remaining credit after paying all bills)
                    if remaining_payment > 0:
                        # Get customer's package price for future bill generation
                        cursor.execute('''
                            SELECT p.price FROM customers c
                            JOIN packages p ON c.package_id = p.id
                            WHERE c.id = ?
                        ''', (customer_id,))
                        package_result = cursor.fetchone()
                        package_price = package_result[0] if package_result else 0.0

                        # If credit >= package price, generate and pay future bills
                        if remaining_payment >= package_price:
                            self._generate_future_bills_with_credit(cursor, customer_id, remaining_payment, package_price)
                        else:
                            # Store remaining credit in customer balance
                            cursor.execute('''
                                UPDATE customers SET credit_balance = ?
                                WHERE id = ?
                            ''', (remaining_payment, customer_id))

                    # Sync customer financials
                    cursor.execute('''
                        SELECT COALESCE(SUM(outstanding_amount), 0)
                        FROM billing
                        WHERE customer_id = ?
                    ''', (customer_id,))
                    outstanding = cursor.fetchone()[0]

                    # Get customer's current credit balance
                    cursor.execute('''
                        SELECT credit_balance FROM customers WHERE id = ?
                    ''', (customer_id,))
                    customer_credit_balance = cursor.fetchone()[0] or 0.0

                    cursor.execute('''
                        UPDATE customers SET
                        outstanding_amount = ?,
                        credit_balance = ?
                        WHERE id = ?
                    ''', (outstanding, customer_credit_balance, customer_id))

                    conn.commit()

                    messagebox.showinfo(
                        "Payment Successful",
                        f"Payment of {payment_amount:.2f} PKR processed successfully."
                    )
                    payment_dialog.destroy()
                    self._load_bills()

                except Exception as e:
                    conn.rollback()
                    raise e

        except ValueError as ve:
            messagebox.showerror("Error", str(ve))
        except Exception as e:
            logger.error(f"Payment processing error: {str(e)}")
            messagebox.showerror("Error", f"Payment failed: {str(e)}")

    def _add_manual_bill(self):
        dialog = tk.Toplevel(self)
        dialog.title("Add Manual Bill")
        dialog.transient(self)
        dialog.grab_set()
        dialog.configure(bg=self.COLORS['background'])
        
        dialog.geometry("900x700")
        dialog.minsize(800, 600)
        
        dialog.update_idletasks()
        width = dialog.winfo_width()
        height = dialog.winfo_height()
        x = (dialog.winfo_screenwidth() // 2) - (width // 2)
        y = (dialog.winfo_screenheight() // 2) - (height // 2)
        dialog.geometry(f"{width}x{height}+{x}+{y}")

        main_container = tk.Frame(dialog, bg=self.COLORS['background'])
        main_container.pack(fill="both", expand=True)

        canvas = tk.Canvas(main_container, bg=self.COLORS['background'])
        canvas.pack(side="left", fill="both", expand=True)

        scrollbar = ttk.Scrollbar(main_container, orient="vertical", command=canvas.yview)
        scrollbar.pack(side="right", fill="y")

        canvas.configure(yscrollcommand=scrollbar.set)
        canvas.bind('<Configure>', lambda e: canvas.configure(scrollregion=canvas.bbox("all")))

        main_frame = tk.Frame(canvas, bg=self.COLORS['card_bg'], highlightbackground=self.COLORS['border'], 
                             highlightthickness=2, padx=20, pady=20)
        canvas.create_window((0, 0), window=main_frame, anchor="nw")

        def _on_mousewheel(event):
            canvas.yview_scroll(int(-1*(event.delta/120)), "units")
        canvas.bind_all("<MouseWheel>", _on_mousewheel)

        tk.Label(main_frame, text="Add Bill", bg=self.COLORS['card_bg'], 
                font=("Helvetica", 16, "bold"), fg=self.COLORS['primary_accent']).pack(anchor="center", pady=(0, 20))

        label_style = {"bg": self.COLORS['card_bg'], "font": ("Helvetica", 10), "fg": self.COLORS['text_primary']}
        dropdown_style = {"font": ("Helvetica", 12), "width": 10}
        entry_style = {"font": ("Helvetica", 12), "width": 30}

        # Ensure customers are loaded
        self._load_customers()

        # Use all customer names (active only if you want)
        customer_names = self.customer_names if hasattr(self, 'customer_names') else list(self.customers.keys())

        customer_frame = tk.Frame(main_frame, bg=self.COLORS['card_bg'])
        customer_frame.pack(fill="x", pady=(0, 15))
        tk.Label(customer_frame, text="Select Customer:", **label_style, width=15, anchor="w").pack(side="left")
        customer_var = tk.StringVar()
        customer_dropdown = ttk.Combobox(customer_frame, textvariable=customer_var, values=customer_names,
                                         state="readonly", **entry_style)
        customer_dropdown.pack(side="left")
        Tooltip(customer_dropdown, "Select the customer for this manual bill")

        # If a specific customer is set, pre-select and disable the dropdown
        if self.customer_id is not None:
            try:
                with get_db_connection() as conn:
                    cursor = conn.cursor()
                    cursor.execute("SELECT name FROM customers WHERE id = ?", (self.customer_id,))
                    customer_name = cursor.fetchone()
                    if customer_name:
                        customer_var.set(customer_name[0])
                        customer_dropdown.config(state='disabled')
            except Exception:
                pass

        month_frame = tk.Frame(main_frame, bg=self.COLORS['card_bg'])
        month_frame.pack(fill="x", pady=(0, 15))
        tk.Label(month_frame, text="Month:", **label_style, width=15, anchor="w").pack(side="left")
        month_var = tk.StringVar(value=str(self.current_month))
        month_dropdown = ttk.Combobox(month_frame, textvariable=month_var, values=[str(i) for i in range(1, 13)],
                                      state="readonly", **dropdown_style)
        month_dropdown.pack(side="left")
        Tooltip(month_dropdown, "Select the billing month")

        year_frame = tk.Frame(main_frame, bg=self.COLORS['card_bg'])
        year_frame.pack(fill="x", pady=(0, 15))
        tk.Label(year_frame, text="Year:", **label_style, width=15, anchor="w").pack(side="left")
        year_var = tk.StringVar(value=str(self.current_year))
        year_dropdown = ttk.Combobox(year_frame, textvariable=year_var, values=[str(i) for i in range(2000, 2101)],
                                     state="readonly", **dropdown_style)
        year_dropdown.pack(side="left")
        Tooltip(year_dropdown, "Select the billing year")

        amount_frame = tk.Frame(main_frame, bg=self.COLORS['card_bg'])
        amount_frame.pack(fill="x", pady=(0, 15))
        tk.Label(amount_frame, text="Amount (PKR):", **label_style, width=15, anchor="w").pack(side="left")
        amount_var = tk.StringVar()
        amount_entry = tk.Entry(amount_frame, textvariable=amount_var, **entry_style)
        amount_entry.pack(side="left", padx=5)
        Tooltip(amount_entry, "Enter the bill amount in PKR (must be greater than 0)")

        outstanding_frame = ttk.LabelFrame(main_frame, text="Outstanding Bills Breakdown", padding=10)
        outstanding_frame.pack(fill="x", pady=(10, 5))

        self.outstanding_tree = ttk.Treeview(
            outstanding_frame,
            columns=("id", "month", "year", "amount", "pending"),
            show="headings",
            height=4,
            selectmode="none"
        )
        self.outstanding_tree.heading("id", text="Bill ID")
        self.outstanding_tree.heading("month", text="Month")
        self.outstanding_tree.heading("year", text="Year")
        self.outstanding_tree.heading("amount", text="Original Amount")
        self.outstanding_tree.heading("pending", text="Pending Amount")
        self.outstanding_tree.column("id", width=60, anchor="center")
        self.outstanding_tree.column("month", width=80, anchor="center")
        self.outstanding_tree.column("year", width=80, anchor="center")
        self.outstanding_tree.column("amount", width=100, anchor="e")
        self.outstanding_tree.column("pending", width=100, anchor="e")
        
        scrollbar = ttk.Scrollbar(outstanding_frame, orient="vertical", command=self.outstanding_tree.yview)
        self.outstanding_tree.configure(yscrollcommand=scrollbar.set)
        self.outstanding_tree.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

        self.selective_include_var = tk.BooleanVar(value=False)
        ttk.Checkbutton(
            outstanding_frame,
            text="Select specific bills to include",
            variable=self.selective_include_var,
            command=lambda: self._toggle_selective_include(customer_var, month_var, year_var, amount_var)
        ).pack(anchor="w", pady=(5, 0))

        history_frame = ttk.LabelFrame(main_frame, text="Recent Payment History", padding=10)
        history_frame.pack(fill="x", pady=(5, 10))

        self.history_tree = ttk.Treeview(
            history_frame,
            columns=("date", "amount", "method", "invoice"),
            show="headings",
            height=3
        )
        self.history_tree.heading("date", text="Date")
        self.history_tree.heading("amount", text="Amount")
        self.history_tree.heading("method", text="Method")
        self.history_tree.heading("invoice", text="Invoice #")
        self.history_tree.column("date", width=100)
        self.history_tree.column("amount", width=80, anchor="e")
        self.history_tree.column("method", width=100)
        self.history_tree.column("invoice", width=120)
        self.history_tree.pack(fill="x")

        summary_frame = tk.Frame(main_frame, bg=self.COLORS['card_bg'])
        summary_frame.pack(fill="x", pady=(5, 10))

        self.summary_label = tk.Label(
            summary_frame,
            text="Summary:\nTotal Outstanding: PKR 0.00\nSelected Outstanding: PKR 0.00\nManual Amount: PKR 0.00\nAvailable Credit: PKR 0.00\nTotal Bill: PKR 0.00",
            bg=self.COLORS['card_bg'],
            font=("Helvetica", 10),
            justify="left"
        )
        self.summary_label.pack(anchor="w")

        self.scenario_label = tk.Label(
            summary_frame,
            text="Scenario: Select a customer",
            bg=self.COLORS['card_bg'],
            font=("Helvetica", 10, "italic")
        )
        self.scenario_label.pack(anchor="w", pady=(5, 0))

        for var in [customer_var, month_var, year_var, amount_var]:
            var.trace_add('write', lambda *_: self._update_bill_details(customer_var, month_var, year_var, amount_var))

        button_frame = tk.Frame(main_frame, bg=self.COLORS['card_bg'])
        button_frame.pack(fill="x", pady=(20, 10))
        
        add_bill_btn = tk.Button(button_frame, text="Add Bill", 
                 command=lambda: self._submit_manual_bill(dialog, customer_var, month_var, year_var, amount_var),
                 bg=self.COLORS['primary_accent'], fg="#FFFFFF",
                 font=("Helvetica", 12, "bold"), relief=tk.FLAT, padx=20, pady=10,
                 activebackground=self.COLORS['secondary_accent'], activeforeground="#FFFFFF")
        add_bill_btn.pack()
        Tooltip(add_bill_btn, "Click to create the manual bill for the selected customer")

        self._update_bill_details(customer_var, month_var, year_var, amount_var)

    def _toggle_selective_include(self, customer_var, month_var, year_var, amount_var):
        if self.selective_include_var.get():
            self.outstanding_tree.config(selectmode="extended")
        else:
            self.outstanding_tree.config(selectmode="none")
        self._update_bill_details(customer_var, month_var, year_var, amount_var)

    def _update_bill_details(self, customer_var, month_var, year_var, amount_var):
        try:
            customer_name = customer_var.get()
            if not customer_name or customer_name not in self.customers:
                self.summary_label.config(text="Summary:\nTotal Outstanding: PKR 0.00\nSelected Outstanding: PKR 0.00\nManual Amount: PKR 0.00\nAvailable Credit: PKR 0.00\nTotal Bill: PKR 0.00")
                self.scenario_label.config(text="Scenario: Select a customer")
                for item in self.outstanding_tree.get_children():
                    self.outstanding_tree.delete(item)
                for item in self.history_tree.get_children():
                    self.history_tree.delete(item)
                return

            customer_id = self.customers[customer_name]
            with get_db_connection() as conn:
                cursor = conn.cursor()

                for item in self.outstanding_tree.get_children():
                    self.outstanding_tree.delete(item)

                # Fetch all unpaid bills for this customer
                cursor.execute('''
                    SELECT b.id, b.month, b.year, b.amount,
                           b.amount - COALESCE(b.paid_amount, 0) as pending
                    FROM billing b
                    WHERE b.customer_id = ? AND (b.amount - COALESCE(b.paid_amount, 0)) > 0
                    ORDER BY b.year, b.month
                ''', (customer_id,))
                outstanding_bills = cursor.fetchall()

                total_outstanding = 0.0
                for bill in outstanding_bills:
                    bill_id, month, year, amount, pending = bill
                    self.outstanding_tree.insert("", "end", values=(
                        str(bill_id).zfill(4),
                        f"{month:02d}/{year}",
                        f"{amount:.2f}",
                        f"{pending:.2f}"
                    ))
                    total_outstanding += pending

                # Get customer's available credit and outstanding (from customers table)
                cursor.execute('SELECT outstanding_amount, credit_balance FROM customers WHERE id = ?', (customer_id,))
                row = cursor.fetchone()
                customer_outstanding = row[0] if row and row[0] else 0.0
                available_credit = row[1] if row and row[1] else 0.0

                cursor.execute('''
                    SELECT ph.payment_date, ph.amount_paid, ph.paid_by, ph.invoice_number
                    FROM payment_history ph
                    WHERE ph.customer_id = ?
                    ORDER BY ph.payment_date DESC
                    LIMIT 3
                ''', (customer_id,))
                payment_history = cursor.fetchall()

                for item in self.history_tree.get_children():
                    self.history_tree.delete(item)

                for payment in payment_history:
                    self.history_tree.insert("", "end", values=(
                        payment[0],
                        f"{payment[1]:.2f}",
                        payment[2],
                        payment[3]
                    ))

                try:
                    manual_amount = float(amount_var.get()) if amount_var.get() else 0.0
                except ValueError:
                    manual_amount = 0.0

                selected_outstanding = 0.0
                if self.selective_include_var.get():
                    for item in self.outstanding_tree.selection():
                        values = self.outstanding_tree.item(item)['values']
                        selected_outstanding += float(values[3])
                else:
                    selected_outstanding = total_outstanding

                total_amount = manual_amount + selected_outstanding

                # Calculate credit that would be applied
                credit_to_apply = min(available_credit, total_amount) if total_amount > 0 else 0.0

                # Calculate net payable after credit
                net_payable = max(total_amount - available_credit, 0.0)

                # --- Show both outstanding and credit from customers table in summary ---
                self.summary_label.config(text=f"Summary:\nTotal Outstanding (All Bills): PKR {total_outstanding:.2f}\n"
                                          f"Customer Outstanding: PKR {customer_outstanding:.2f}\n"
                                          f"Selected Outstanding: PKR {selected_outstanding:.2f}\n"
                                          f"Manual Amount: PKR {manual_amount:.2f}\n"
                                          f"Available Credit: PKR {available_credit:.2f}\n"
                                          f"Total Bill: PKR {net_payable:.2f}")

                if manual_amount > 0 and selected_outstanding > 0:
                    if credit_to_apply > 0:
                        self.scenario_label.config(text=f"Scenario: Manual bill with outstanding payment, {credit_to_apply:.2f} credit will be adjusted")
                    else:
                        self.scenario_label.config(text="Scenario: Manual bill with outstanding payment")
                elif manual_amount > 0:
                    if credit_to_apply > 0:
                        self.scenario_label.config(text=f"Scenario: Manual bill only, {credit_to_apply:.2f} credit will be adjusted")
                    else:
                        self.scenario_label.config(text="Scenario: Manual bill only")
                elif selected_outstanding > 0:
                    if credit_to_apply > 0:
                        self.scenario_label.config(text=f"Scenario: Outstanding payment only, {credit_to_apply:.2f} credit will be adjusted")
                    else:
                        self.scenario_label.config(text="Scenario: Outstanding payment only")
                else:
                    self.scenario_label.config(text="Scenario: No amount specified")

        except Exception as e:
            logger.error(f"Error updating bill details: {str(e)}")

    def _submit_manual_bill(self, dialog, customer_var, month_var, year_var, amount_var):
        try:
            customer_name = customer_var.get()
            if not customer_name or customer_name not in self.customers:
                raise ValueError("Please select a valid customer from the dropdown list")
            month = int(month_var.get())
            year = int(year_var.get())
            amount = float(amount_var.get())
            if amount <= 0:
                raise ValueError("Amount must be greater than 0 PKR")
            customer_id = self.customers[customer_name]
            if not self._is_customer_active(customer_id):
                raise ValueError("Cannot add manual bill for inactive customer. Please select an active customer.")

            with get_db_connection() as conn:
                try:
                    conn.execute("BEGIN")
                    cursor = conn.cursor()

                    # Check if bill already exists for this customer, month, and year
                    cursor.execute('''
                        SELECT id FROM billing
                        WHERE customer_id = ? AND month = ? AND year = ?
                    ''', (customer_id, month, year))
                    existing_bill = cursor.fetchone()

                    if existing_bill:
                        raise ValueError(f"A bill already exists for {customer_name} for {month:02d}/{year}")

                    # Use simplified billing calculation rules
                    # First create a temporary bill to calculate month_bill_amount
                    temp_bill_id = 999999  # Temporary ID for calculation
                    month_bill_amount = self._calculate_month_bill_amount(cursor, customer_id, temp_bill_id, amount)

                    # The total bill amount is the calculated month bill amount
                    total_bill_amount = month_bill_amount

                    # Generate invoice number
                    cursor.execute("SELECT MAX(CAST(SUBSTR(invoice_number, 5) AS INTEGER)) FROM billing WHERE invoice_number IS NOT NULL")
                    max_inv_num = cursor.fetchone()[0] or 0
                    invoice_number = f"INV-{str(max_inv_num + 1).zfill(4)}"

                    # Get customer's available credit (from customers.credit_balance)
                    cursor.execute('SELECT credit_balance FROM customers WHERE id = ?', (customer_id,))
                    row = cursor.fetchone()
                    available_credit = row[0] if row and row[0] else 0.0

                    # Apply credit to the new bill
                    if available_credit >= total_bill_amount:
                        paid_amount = total_bill_amount
                        outstanding_amount = 0.0
                        new_credit_balance = available_credit - total_bill_amount
                        status = 'Paid'
                    else:
                        paid_amount = available_credit
                        outstanding_amount = total_bill_amount - available_credit
                        new_credit_balance = 0.0
                        status = 'Unpaid'

                    # For manual bills, month_bill_amount is the calculated total bill amount
                    month_bill_amount = total_bill_amount

                    # Create the manual bill
                    cursor.execute('''
                        INSERT INTO billing (
                            customer_id, month, year, amount, month_bill_amount, paid_amount, outstanding_amount,
                            credit_amount, status, invoice_number, is_manual, created_date
                        ) VALUES (?, ?, ?, ?, ?, ?, ?, 0, ?, ?, 1, datetime('now'))
                    ''', (customer_id, month, year, amount, month_bill_amount, paid_amount, outstanding_amount, status, invoice_number))

                    new_bill_id = cursor.lastrowid

                    # Update customer credit balance and clear previous outstanding/credit since it's now included in this bill
                    cursor.execute('UPDATE customers SET credit_balance = ?, outstanding_amount = 0 WHERE id = ?', (new_credit_balance, customer_id))

                    # Sync customer financials
                    cursor.execute('''
                        SELECT
                            COALESCE(SUM(outstanding_amount), 0),
                            COALESCE(SUM(credit_amount), 0)
                        FROM billing
                        WHERE customer_id = ?
                    ''', (customer_id,))
                    outstanding, credit = cursor.fetchone()

                    cursor.execute('''
                        UPDATE customers SET
                        outstanding_amount = ?,
                        credit_balance = ?
                        WHERE id = ?
                    ''', (outstanding, new_credit_balance, customer_id))

                    conn.commit()

                    messagebox.showinfo("Success", f"Manual bill added successfully for {customer_name} ({month:02d}/{year}): {total_bill_amount:.2f} PKR\nCredit applied: {paid_amount:.2f} PKR\nOutstanding: {outstanding_amount:.2f} PKR")
                    dialog.destroy()
                    self._load_bills()

                except Exception as e:
                    conn.rollback()
                    raise e

        except ValueError as ve:
            messagebox.showerror("Error", str(ve))
        except Exception as e:
            logger.error(f"Error adding manual bill: {str(e)}")
            messagebox.showerror("Error", f"Failed to add manual bill: {str(e)}")

    def _export_billing_data(self):
        """Export all billing data with customer information"""
        try:
            export_path = filedialog.asksaveasfilename(
                defaultextension=".xlsx",
                filetypes=[("Excel files", "*.xlsx")],
                title="Export Billing Data"
            )
            if not export_path:
                return
            with get_db_connection() as conn:
                query = '''
                    SELECT 
                        c.id as customer_id,
                        c.user_name,
                        c.name,
                        c.phone,
                        p.name as package_name,
                        c.region,
                        CASE WHEN c.status = 1 THEN 'Active' ELSE 'Inactive' END as status,
                        b.month,
                        b.year,
                        b.amount,
                        b.paid_amount,
                        b.status as bill_status,
                        b.outstanding_amount,
                        b.credit_amount,
                        b.paid_date,
                        b.invoice_number,
                        b.paid_by,
                        b.is_manual
                    FROM billing b
                    JOIN customers c ON b.customer_id = c.id
                    LEFT JOIN packages p ON c.package_id = p.id
                    ORDER BY c.user_name, b.year DESC, b.month DESC
                '''
                df = pd.read_sql_query(query, conn)
                df.to_excel(export_path, index=False)
                messagebox.showinfo("Success", "Billing data exported successfully")
                
        except Exception as e:
            messagebox.showerror("Error", f"Failed to export billing data: {str(e)}")

    def _import_billing_data(self):
        """Import billing data from Excel with new requirements: only user_name, package_name, and region are required."""
        try:
            import_path = filedialog.askopenfilename(
                filetypes=[("Excel files", "*.xlsx")],
                title="Select Billing Data File"
            )
            if not import_path:
                return
            confirm = messagebox.askyesno(
                "Confirm Import",
                "WARNING: This will completely replace all billing data including:\n"
                "• All billing records\n"
                "• All payment history\n\n"
                "This action cannot be undone. Do you want to continue?",
                icon='warning'
            )
            if not confirm:
                return
            df = pd.read_excel(import_path)
            # Only keep columns that are in the header (ignore extra values in rows)
            df = df.loc[:, ~df.columns.duplicated()]
            required_columns = ['user_name', 'package_name', 'region']
            missing_cols = [col for col in required_columns if col not in df.columns]
            if missing_cols:
                messagebox.showerror("Error", f"Missing required columns: {', '.join(missing_cols)}")
                return
            progress_window = self._create_progress_dialog(len(df))
            try:
                import_result = self._process_billing_import_modular(df, progress_window)
                progress_window.destroy()
                self._show_import_summary(import_result)
                if import_result['successful_imports'] > 0:
                    # Refresh all data to show imported data instantly
                    self._load_regions()
                    self._load_customers()
                    self._load_bills()
                    self._sync_all_customer_financials()
            except Exception as e:
                progress_window.destroy()
                messagebox.showerror("Error", f"Failed to import billing data: {str(e)}")
        except Exception as e:
            messagebox.showerror("Error", f"Failed to import billing data: {str(e)}")

    def _process_billing_import_modular(self, df, progress_window):
        import_result = {
            'total_rows': len(df),
            'successful_imports': 0,
            'failed_imports': 0,
            'failed_rows': []
        }
        try:
            with get_db_connection() as conn:
                c = conn.cursor()
                try:
                    c.execute("BEGIN TRANSACTION")
                    # Clear all relevant tables for a full data reset
                    c.execute("DELETE FROM customer_purchases")
                    c.execute("DELETE FROM billing")
                    c.execute("DELETE FROM payment_history")
                    c.execute("DELETE FROM customers")

                    processed_users = set()

                    # Only process columns that exist in the DataFrame header
                    allowed_columns = list(df.columns)

                    # Precompute first bill id for each customer for fast lookup
                    # REMOVE the following two lines from import logic:
                    # cursor.execute('SELECT customer_id, MIN(id) FROM billing GROUP BY customer_id')
                    # first_bill_map = {row[0]: row[1] for row in cursor.fetchall()}

                    for idx, row in df.iterrows():
                        row = row[allowed_columns] if hasattr(row, '__getitem__') else row
                        row_errors = []
                        try:
                            user_name = str(row['user_name']).strip()
                            package_name = str(row['package_name']).strip()
                            region = str(row['region']).strip()
                            name = str(row['name']).strip() if 'name' in row and pd.notna(row['name']) else user_name
                            phone = str(row['phone']).strip() if 'phone' in row and pd.notna(row['phone']) else None
                            # Parse status
                            status_str = str(row['status']).strip().lower() if 'status' in row and pd.notna(row['status']) else 'active'
                            status = 0 if status_str == 'inactive' else 1
                            # Business/required field checks
                            if not user_name:
                                row_errors.append("user_name is required")
                            if not package_name:
                                row_errors.append("package_name is required")
                            if not region:
                                row_errors.append("region is required")
                            if user_name in processed_users:
                                row_errors.append(f"Duplicate user_name '{user_name}' in import file")
                            # Data type and logical validations
                            row_errors.extend(self._validate_bill_row(row))
                            if row_errors:
                                raise ValueError("; ".join(row_errors))
                            region_id = self._get_or_create_region(c, region)
                            package_id, package_price = self._get_or_create_package(c, package_name)
                            customer_id = self._get_or_create_customer(c, user_name, name, phone, package_id, region, status)
                            now = datetime.now()
                            month = now.month
                            year = now.year
                            if self._bill_exists(c, customer_id, month, year):
                                row_errors.append(f"Bill for user '{user_name}' already exists for {month:02d}/{year}")
                                raise ValueError("; ".join(row_errors))
                            status_str = str(row['status']).strip() if 'status' in row and pd.notna(row['status']) else 'active'
                            imported_outstanding = float(row['outstanding_amount']) if 'outstanding_amount' in row and pd.notna(row['outstanding_amount']) else 0.0
                            imported_credit = float(row['credit_amount']) if 'credit_amount' in row and pd.notna(row['credit_amount']) else 0.0
                            if status == 0:  # Inactive customer
                                # Always create an unpaid bill for inactive customers, do not apply credit
                                # For imported bills, use the imported outstanding amount directly
                                bill_outstanding = imported_outstanding if imported_outstanding > 0 else package_price
                                paid_amount = 0.0
                                paid_date = now.strftime('%Y-%m-%d')
                                credit_amount = 0.0
                                is_manual = 0
                                # Calculate month bill amount using business rules (no credit for inactive customers)
                                month_bill_amount = self._calculate_month_bill_for_import(package_price, 0.0, imported_outstanding)
                                c.execute('''INSERT INTO billing (
                                    customer_id, month, year, amount, month_bill_amount, paid_amount, status,
                                    outstanding_amount, credit_amount, paid_date, invoice_number,
                                    paid_by, is_manual
                                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)''', (
                                    customer_id, month, year, package_price, month_bill_amount, paid_amount, 'Unpaid',
                                    bill_outstanding, credit_amount, paid_date, None,
                                    None, is_manual
                                ))
                                # Store credit in customer account, do not apply to bill
                                c.execute("UPDATE customers SET outstanding_amount = ?, credit_balance = ? WHERE id = ?", (bill_outstanding, imported_credit, customer_id))
                                processed_users.add(user_name)
                                import_result['successful_imports'] += 1
                                self._update_progress(progress_window, idx + 1, f"Imported inactive customer row {idx+2}")
                                continue
                            # Active customer bill creation with proper import handling
                            status = str(row['status']).strip() if 'status' in row and pd.notna(row['status']) else 'Active'
                            # Outstanding import logic
                            imported_outstanding = float(row['outstanding_amount']) if 'outstanding_amount' in row and pd.notna(row['outstanding_amount']) else 0.0
                            # Credit import logic
                            imported_credit = float(row['credit_amount']) if 'credit_amount' in row and pd.notna(row['credit_amount']) else 0.0

                            # Validate business rule: credit and outstanding cannot both be > 0
                            if imported_credit > 0 and imported_outstanding > 0:
                                raise ValueError(f"Row {idx+2}: Credit ({imported_credit}) and outstanding ({imported_outstanding}) cannot both be greater than 0 for customer {user_name}")

                            # --- ENHANCED CREDIT SCENARIOS WITH PROPER IMPORT HANDLING ---
                            if 0 < imported_credit < package_price:
                                # Partial payment for current month with imported outstanding
                                paid_amount = imported_credit
                                # For imported bills, store the final outstanding/credit amounts directly
                                outstanding = package_price - imported_credit + imported_outstanding
                                credit_amount = 0.0
                                bill_status = 'Partially Paid' if outstanding > 0 else 'Paid'
                                paid_date = now.strftime('%Y-%m-%d')
                                is_manual = 0
                                import_invoice = self._generate_import_invoice_number()
                                paid_by = f"Sys. {current_user}" if current_user else "Sys. System"
                                # Calculate month bill amount using business rules
                                month_bill_amount = self._calculate_month_bill_for_import(package_price, imported_credit, imported_outstanding)
                                c.execute('''INSERT INTO billing (
                                    customer_id, month, year, amount, month_bill_amount, paid_amount, status,
                                    outstanding_amount, credit_amount, paid_date, invoice_number,
                                    paid_by, is_manual
                                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)''', (
                                    customer_id, month, year, package_price, month_bill_amount, paid_amount, bill_status,
                                    outstanding, credit_amount, paid_date, import_invoice,
                                    paid_by, is_manual
                                ))
                                bill_id = c.lastrowid

                                # Record payment history if payment was made
                                if paid_amount > 0:
                                    c.execute('''
                                        INSERT INTO payment_history
                                        (bill_id, invoice_number, customer_id, payment_date, amount_paid,
                                         credit_amount, outstanding_amount, remaining_outstanding, applied_credit,
                                         payment_method, recorded_by, timestamp)
                                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, datetime('now'))
                                    ''', (bill_id, import_invoice, customer_id, paid_date, paid_amount,
                                          credit_amount, outstanding, outstanding, credit_amount, 'Import', paid_by))

                                # Update customer outstanding/credit fields
                                c.execute("UPDATE customers SET outstanding_amount = ?, credit_balance = ? WHERE id = ?", (outstanding, 0.0, customer_id))
                            elif imported_credit >= package_price:
                                # Pay as many months as possible with proper outstanding handling
                                credit = imported_credit
                                future_month = month
                                future_year = year
                                # First, handle any imported outstanding for the first bill
                                if imported_outstanding > 0:
                                    # Create first bill with outstanding amount
                                    paid_amount = package_price
                                    outstanding = imported_outstanding
                                    credit_amount = 0.0
                                    bill_status = 'Paid'
                                    paid_date = now.strftime('%Y-%m-%d')
                                    is_manual = 0
                                    import_invoice = self._generate_import_invoice_number()
                                    paid_by = f"Sys. {current_user}" if current_user else "Sys. System"
                                    # Calculate month bill amount: package_price + outstanding (since credit will pay it)
                                    month_bill_amount = package_price + imported_outstanding
                                    c.execute('''INSERT INTO billing (
                                        customer_id, month, year, amount, month_bill_amount, paid_amount, status,
                                        outstanding_amount, credit_amount, paid_date, invoice_number,
                                        paid_by, is_manual
                                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)''', (
                                        customer_id, future_month, future_year, package_price, month_bill_amount, paid_amount, bill_status,
                                        outstanding, credit_amount, paid_date, import_invoice,
                                        paid_by, is_manual
                                    ))
                                bill_id = c.lastrowid

                                # Record payment history
                                if paid_amount > 0:
                                    c.execute('''
                                        INSERT INTO payment_history
                                        (bill_id, invoice_number, customer_id, payment_date, amount_paid,
                                         credit_amount, outstanding_amount, remaining_outstanding, applied_credit,
                                         payment_method, recorded_by, timestamp)
                                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, datetime('now'))
                                    ''', (bill_id, import_invoice, customer_id, paid_date, paid_amount,
                                          credit_amount, outstanding, outstanding, credit_amount, 'Import', paid_by))

                                c.execute("UPDATE customers SET outstanding_amount = ?, credit_balance = ? WHERE id = ?", (outstanding, 0.0, customer_id))
                                credit -= package_price
                                # Move to next month for further credit application
                                future_month += 1
                                if future_month > 12:
                                    future_month = 1
                                    future_year += 1
                                # Now, use remaining credit to pay as many full months as possible
                                while credit >= package_price:
                                    if self._bill_exists(c, customer_id, future_month, future_year):
                                        future_month += 1
                                        if future_month > 12:
                                            future_month = 1
                                            future_year += 1
                                        continue
                                    paid_amount = package_price
                                    outstanding = 0.0
                                    credit_amount = 0.0
                                    bill_status = 'Paid'
                                    paid_date = now.strftime('%Y-%m-%d')
                                    is_manual = 0
                                    import_invoice = self._generate_import_invoice_number()
                                    paid_by = f"Sys. {current_user}" if current_user else "Sys. System"
                                    # For future bills paid by credit, month_bill_amount is package_price
                                    month_bill_amount = package_price
                                    c.execute('''INSERT INTO billing (
                                        customer_id, month, year, amount, month_bill_amount, paid_amount, status,
                                        outstanding_amount, credit_amount, paid_date, invoice_number,
                                        paid_by, is_manual
                                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)''', (
                                        customer_id, future_month, future_year, package_price, month_bill_amount, paid_amount, bill_status,
                                        outstanding, credit_amount, paid_date, import_invoice,
                                        paid_by, is_manual
                                    ))
                                    bill_id = c.lastrowid

                                    # Record payment history
                                    if paid_amount > 0:
                                        c.execute('''
                                            INSERT INTO payment_history
                                            (bill_id, invoice_number, customer_id, payment_date, amount_paid,
                                             credit_amount, outstanding_amount, remaining_outstanding, applied_credit,
                                             payment_method, recorded_by, timestamp)
                                            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, datetime('now'))
                                        ''', (bill_id, import_invoice, customer_id, paid_date, paid_amount,
                                              credit_amount, outstanding, outstanding, credit_amount, 'Import', paid_by))

                                    c.execute("UPDATE customers SET outstanding_amount = ?, credit_balance = ? WHERE id = ?", (outstanding, 0.0, customer_id))
                                    credit -= package_price
                                    future_month += 1
                                    if future_month > 12:
                                        future_month = 1
                                        future_year += 1
                                # If some credit remains, create a partially paid bill for next month
                                if 0 < credit < package_price:
                                    if not self._bill_exists(c, customer_id, future_month, future_year):
                                        paid_amount = credit
                                        outstanding = package_price - credit
                                        credit_amount = 0.0
                                        bill_status = 'Partially Paid'
                                        paid_date = now.strftime('%Y-%m-%d')
                                        is_manual = 0
                                        import_invoice = self._generate_import_invoice_number()
                                        paid_by = f"Sys. {current_user}" if current_user else "Sys. System"
                                        # For future bills paid by credit, month_bill_amount is package_price
                                        month_bill_amount = package_price
                                        c.execute('''INSERT INTO billing (
                                            customer_id, month, year, amount, month_bill_amount, paid_amount, status,
                                            outstanding_amount, credit_amount, paid_date, invoice_number,
                                            paid_by, is_manual
                                        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)''', (
                                            customer_id, future_month, future_year, package_price, month_bill_amount, paid_amount, bill_status,
                                            outstanding, credit_amount, paid_date, import_invoice,
                                            paid_by, is_manual
                                        ))
                                        bill_id = c.lastrowid

                                        # Record payment history
                                        if paid_amount > 0:
                                            c.execute('''
                                                INSERT INTO payment_history
                                                (bill_id, invoice_number, customer_id, payment_date, amount_paid,
                                                 credit_amount, outstanding_amount, remaining_outstanding, applied_credit,
                                                 payment_method, recorded_by, timestamp)
                                                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, datetime('now'))
                                            ''', (bill_id, import_invoice, customer_id, paid_date, paid_amount,
                                                  credit_amount, outstanding, outstanding, credit_amount, 'Import', paid_by))

                                        c.execute("UPDATE customers SET outstanding_amount = ?, credit_balance = ? WHERE id = ?", (outstanding, 0.0, customer_id))
                            else:
                                # No or zero credit, use imported outstanding amount directly
                                # For imported bills, the outstanding should be exactly what's imported
                                bill_outstanding = imported_outstanding if imported_outstanding > 0 else package_price
                                paid_amount = 0.0
                                paid_date = now.strftime('%Y-%m-%d')
                                credit_amount = 0.0
                                is_manual = 0
                                bill_status = 'Unpaid'
                                # Calculate month bill amount using business rules
                                month_bill_amount = self._calculate_month_bill_for_import(package_price, 0.0, imported_outstanding)
                                c.execute('''INSERT INTO billing (
                                    customer_id, month, year, amount, month_bill_amount, paid_amount, status,
                                    outstanding_amount, credit_amount, paid_date, invoice_number,
                                    paid_by, is_manual
                                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)''', (
                                    customer_id, month, year, package_price, month_bill_amount, paid_amount, bill_status,
                                    bill_outstanding, credit_amount, paid_date, None,
                                    None, is_manual
                                ))
                                c.execute("UPDATE customers SET outstanding_amount = ?, credit_balance = ? WHERE id = ?", (bill_outstanding, 0.0, customer_id))
                            processed_users.add(user_name)
                            import_result['successful_imports'] += 1
                            self._update_progress(progress_window, idx + 1, f"Imported row {idx+2}")
                        except Exception as e:
                            import_result['failed_imports'] += 1
                            import_result['failed_rows'].append({
                                'row': idx + 2,
                                'user_name': str(row.get('user_name', '')),
                                'errors': [str(e)]
                            })
                            self._update_progress(progress_window, idx + 1, f"Failed row {idx+2}: {str(e)}")
                            continue
                    conn.commit()
                    return import_result
                except Exception as e:
                    conn.rollback()
                    raise e
        except Exception as e:
            raise e

    def _get_or_create_region(self, c, region):
        c.execute("SELECT id FROM regions WHERE name = ?", (region,))
        result = c.fetchone()
        if result:
            return result[0]
        c.execute("INSERT INTO regions (name) VALUES (?)", (region,))
        c.execute("SELECT id FROM regions WHERE name = ?", (region,))
        return c.fetchone()[0]

    def _get_or_create_package(self, c, package_name):
        c.execute("SELECT id, price FROM packages WHERE name = ?", (package_name,))
        result = c.fetchone()
        if result:
            return result[0], result[1]
        c.execute("INSERT INTO packages (name, price) VALUES (?, ?)", (package_name, 0.0))
        c.execute("SELECT id, price FROM packages WHERE name = ?", (package_name,))
        pkg = c.fetchone()
        return pkg[0], pkg[1]

    def _get_or_create_customer(self, c, user_name, name, phone, package_id, region, status=1):
        c.execute("SELECT id FROM customers WHERE user_name = ?", (user_name,))
        result = c.fetchone()
        if result:
            c.execute("UPDATE customers SET package_id = ?, region = ?, status = ? WHERE id = ?", (package_id, region, status, result[0]))
            return result[0]
        create_date = datetime.now().strftime('%Y-%m-%d')
        c.execute('''INSERT INTO customers (user_name, name, phone, package_id, create_date, status, region)
                     VALUES (?, ?, ?, ?, ?, ?, ?)''',
                  (user_name, name, phone, package_id, create_date, status, region))
        c.execute("SELECT id FROM customers WHERE user_name = ?", (user_name,))
        return c.fetchone()[0]

    def _bill_exists(self, c, customer_id, month, year):
        c.execute("SELECT COUNT(*) FROM billing WHERE customer_id = ? AND month = ? AND year = ?", (customer_id, month, year))
        return c.fetchone()[0] > 0

    def _create_bill(self, c, customer_id, month, year, amount, status):
        paid_amount = 0.0
        paid_date = datetime.now().strftime('%Y-%m-%d')
        credit_amount = 0.0
        is_manual = 0

        # Calculate month_bill_amount using simplified billing rules
        temp_bill_id = 999999  # Temporary ID for calculation
        month_bill_amount = self._calculate_month_bill_amount(c, customer_id, temp_bill_id, amount)

        # Outstanding amount is the month_bill_amount for unpaid bills
        outstanding_amount = month_bill_amount if status != 'Paid' else 0.0

        c.execute('''INSERT INTO billing (
            customer_id, month, year, amount, month_bill_amount, paid_amount, status,
            outstanding_amount, credit_amount, paid_date, invoice_number,
            paid_by, is_manual
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)''', (
            customer_id, month, year, amount, month_bill_amount, paid_amount, status,
            outstanding_amount, credit_amount, paid_date, None,
            None, is_manual
        ))

    def _calculate_customer_balance(self, c, customer_id):
        c.execute("SELECT SUM(amount - COALESCE(paid_amount, 0)) FROM billing WHERE customer_id = ? AND status != 'Paid'", (customer_id,))
        outstanding = c.fetchone()[0] or 0.0
        c.execute("SELECT SUM(credit_amount) FROM billing WHERE customer_id = ? AND status = 'Paid'", (customer_id,))
        credit = c.fetchone()[0] or 0.0
        return outstanding, credit

    def _update_customer_financials(self, c, customer_id, outstanding, credit):
        c.execute("UPDATE customers SET outstanding_amount = ?, credit_balance = ? WHERE id = ?", (outstanding, credit, customer_id))

    def _sync_all_customer_financials(self):
        """Sync financial data for all customers after import"""
        try:
            with get_db_connection() as conn:
                c = conn.cursor()
                c.execute("SELECT id FROM customers")
                for (customer_id,) in c.fetchall():
                    self.sync_customer_financials(customer_id)
        except Exception as e:
            logger.error(f"Error syncing customer financials: {str(e)}")

    def _create_progress_dialog(self, total_rows):
        """Create progress dialog for import operations"""
        progress_window = tk.Toplevel(self)
        progress_window.title("Importing Billing Data")
        progress_window.geometry("400x150")
        progress_window.transient(self)
        progress_window.grab_set()
        
        # Center the dialog
        progress_window.update_idletasks()
        width = progress_window.winfo_width()
        height = progress_window.winfo_height()
        x = (progress_window.winfo_screenwidth() // 2) - (width // 2)
        y = (progress_window.winfo_screenheight() // 2) - (height // 2)
        progress_window.geometry(f"{width}x{height}+{x}+{y}")

        # Progress frame
        frame = tk.Frame(progress_window, bg=self.COLORS['card_bg'], padx=20, pady=20)
        frame.pack(fill="both", expand=True)

        # Status label
        status_label = tk.Label(frame, text="Preparing import...",
                               font=("Helvetica", 12), bg=self.COLORS['card_bg'])
        status_label.pack(pady=(0, 10))

        # Progress bar
        progress_bar = ttk.Progressbar(frame, length=300, mode='determinate')
        progress_bar.pack(pady=(0, 10))
        progress_bar['maximum'] = total_rows

        # Progress text
        progress_text = tk.Label(frame, text="0 / 0 rows processed",
                                font=("Helvetica", 10), bg=self.COLORS['card_bg'])
        progress_text.pack()

        # Store references for updating
        progress_window.status_label = status_label
        progress_window.progress_bar = progress_bar
        progress_window.progress_text = progress_text
        progress_window.total_rows = total_rows

        return progress_window

    def _update_progress(self, progress_window, current_row, status_text):
        """Update the progress dialog"""
        if progress_window and progress_window.winfo_exists():
            progress_window.status_label.config(text=status_text)
            progress_window.progress_bar['value'] = current_row
            progress_window.progress_text.config(text=f"{current_row} / {progress_window.total_rows} rows processed")
            progress_window.update()

    def reconcile_all_customers(self):
        """Recalculate and update outstanding and credit for all customers after import."""
        with get_db_connection() as conn:
            c = conn.cursor()
            c.execute("SELECT id FROM customers")
            for (customer_id,) in c.fetchall():
                outstanding, credit = self._calculate_customer_balance(c, customer_id)
                self._update_customer_financials(c, customer_id, outstanding, credit)

    def _export_failed_rows(self, failed_rows):
        import pandas as pd
        from tkinter import filedialog
        if not failed_rows:
            messagebox.showinfo("Export", "No failed rows to export.")
            return
        df = pd.DataFrame([
            {
                "row": row['row'],
                "user_name": row.get('user_name', ''),
                "errors": "; ".join(row['errors'])
            }
            for row in failed_rows
        ])
        export_path = filedialog.asksaveasfilename(
            defaultextension=".xlsx",
            filetypes=[("Excel files", "*.xlsx")],
            title="Export Failed Rows"
        )
        if export_path:
            df.to_excel(export_path, index=False)
            messagebox.showinfo("Export", f"Failed rows exported to {export_path}")

    def _show_import_summary(self, import_result):
        """Show import summary dialog with detailed results and export option for failed rows."""
        summary_window = tk.Toplevel(self)
        summary_window.title("Billing Import Summary")
        summary_window.geometry("600x500")
        summary_window.transient(self)
        summary_window.grab_set()
        
        # Center the dialog
        summary_window.update_idletasks()
        width = summary_window.winfo_width()
        height = summary_window.winfo_height()
        x = (summary_window.winfo_screenwidth() // 2) - (width // 2)
        y = (summary_window.winfo_screenheight() // 2) - (height // 2)
        summary_window.geometry(f"{width}x{height}+{x}+{y}")

        # Main frame
        main_frame = tk.Frame(summary_window, bg=self.COLORS['card_bg'],
                             highlightbackground=self.COLORS['border'],
                             highlightthickness=2, padx=20, pady=20)
        main_frame.pack(fill="both", expand=True)

        # Title
        title_label = tk.Label(main_frame, text="Billing Import Summary",
                              font=("Helvetica", 16, "bold"), bg=self.COLORS['card_bg'],
                              fg=self.COLORS['primary_accent'])
        title_label.pack(anchor="center", pady=(0, 20))

        # Summary stats
        stats_frame = tk.Frame(main_frame, bg=self.COLORS['card_bg'])
        stats_frame.pack(fill="x", pady=(0, 20))

        label_style = {"bg": self.COLORS['card_bg'], "font": ("Helvetica", 12), "fg": self.COLORS['text_primary']}

        tk.Label(stats_frame, text=f"Total Rows Processed: {import_result['total_rows']}",
                **label_style).pack(anchor="w", pady=2)
        tk.Label(stats_frame, text=f"Successful Imports: {import_result['successful_imports']}",
                bg=self.COLORS['card_bg'], font=("Helvetica", 12), fg="#4CAF50").pack(anchor="w", pady=2)
        tk.Label(stats_frame, text=f"Failed Imports: {import_result['failed_imports']}",
                bg=self.COLORS['card_bg'], font=("Helvetica", 12), fg="#F44336").pack(anchor="w", pady=2)

        # Flag inconsistencies if any
        if import_result.get('failed_rows'):
            tk.Label(main_frame, text="Inconsistencies/Errors Found:",
                    font=("Helvetica", 12, "bold"), bg=self.COLORS['card_bg'],
                    fg="#E53E3E").pack(anchor="w", pady=(10, 5))

        # Failed rows details
        if import_result['failed_rows']:
            tk.Label(main_frame, text="Failed Rows Details:",
                    font=("Helvetica", 12, "bold"), bg=self.COLORS['card_bg'],
                    fg=self.COLORS['text_primary']).pack(anchor="w", pady=(10, 5))

            # Create scrollable text widget for errors
            text_frame = tk.Frame(main_frame, bg=self.COLORS['card_bg'])
            text_frame.pack(fill="both", expand=True, pady=(0, 20))

            scrollbar = ttk.Scrollbar(text_frame)
            scrollbar.pack(side="right", fill="y")

            error_text = tk.Text(text_frame, wrap=tk.WORD, yscrollcommand=scrollbar.set,
                                font=("Helvetica", 10), height=15, bg=self.COLORS['input_bg'],
                                fg=self.COLORS['text_primary'], relief=tk.FLAT,
                                highlightbackground=self.COLORS['border'], highlightthickness=1)
            error_text.pack(side="left", fill="both", expand=True)
            scrollbar.config(command=error_text.yview)

            # Add error details
            for failed_row in import_result['failed_rows']:
                row_num = failed_row['row']
                user_name = failed_row.get('user_name', 'Unknown')
                errors = failed_row['errors']

                error_text.insert(tk.END, f"Row {row_num} (User Name: {user_name}):\n")
                for error in errors:
                    error_text.insert(tk.END, f"  • {error}\n")
                error_text.insert(tk.END, "\n")

            error_text.config(state=tk.DISABLED)

            # Export failed rows button
            tk.Button(main_frame, text="Export Failed Rows", command=lambda: self._export_failed_rows(import_result['failed_rows']),
                     bg=self.COLORS['primary_accent'], fg="#FFFFFF",
                     font=("Helvetica", 12, "bold"), relief=tk.FLAT, padx=20, pady=10,
                     activebackground=self.COLORS['secondary_accent'], activeforeground="#FFFFFF").pack(anchor="center", pady=10)

        # Close button
        tk.Button(main_frame, text="Close", command=lambda: [summary_window.destroy(), self._load_bills()],
                 bg=self.COLORS['primary_accent'], fg="#FFFFFF",
                 font=("Helvetica", 12, "bold"), relief=tk.FLAT, padx=20, pady=10,
                 activebackground=self.COLORS['secondary_accent'], activeforeground="#FFFFFF").pack(anchor="center", pady=10)
    def refresh(self):
        self._load_customers()
        self._load_bills()
    def set_customer(self, customer_id):
        self.customer_id = customer_id
        self.specific_customer_view = True
        
        try:
            with get_db_connection() as conn:
                cursor = conn.cursor()
                cursor.execute("SELECT name FROM customers WHERE id = ?", (customer_id,))
                customer_name = cursor.fetchone()
        
                if customer_name:
                    self.customer_search_var.set(customer_name[0])
                    self.customer_combobox.config(state='disabled')
        except Exception as e:
            logger.error(f"Error setting customer: {str(e)}")
        
        self._load_bills()
    def _delete_bill(self):
        selected = self.tree.selection()
        if not selected:
            messagebox.showwarning("Warning", "Please select a bill to delete")
            return
        bill_id = int(self.tree.item(selected[0])['values'][0])
        customer = self.tree.item(selected[0])['values'][2]
        month = self.tree.item(selected[0])['values'][5]
        if not messagebox.askyesno("Confirm", f"Delete bill for {customer} ({month})?\nThis cannot be undone"):
            return
        try:
            with get_db_connection() as conn:
                cursor = conn.cursor()
                cursor.execute("BEGIN")
                cursor.execute("SELECT customer_id FROM billing WHERE id = ?", (bill_id,))
                customer_id = cursor.fetchone()[0]
                cursor.execute("DELETE FROM billing WHERE id = ?", (bill_id,))
                # Sync customer financials after deletion
                self.sync_customer_financials(customer_id)
                conn.commit()
                messagebox.showinfo("Success", "Bill deleted successfully")
                self._load_bills()
        except Exception as e:
            logger.error(f"Error deleting bill: {str(e)}")
            messagebox.showerror("Error", f"Failed to delete bill: {str(e)}")
    def _is_customer_active(self, customer_id):
        with get_db_connection() as conn:
            c = conn.cursor()
            c.execute("SELECT status FROM customers WHERE id = ?", (customer_id,))
            result = c.fetchone()
            return result and result[0] == 1

    def _show_payment_history_popup(self, invoice_number):
        with get_db_connection() as conn:
            cursor = conn.cursor()
            cursor.execute('''SELECT payment_date, amount_paid, paid_by, credit_amount, outstanding_amount FROM payment_history WHERE invoice_number = ? ORDER BY payment_date DESC''', (invoice_number,))
            payments = cursor.fetchall()
        popup = tk.Toplevel(self)
        popup.title("Payment History")
        popup.geometry("600x350")
        frame = tk.Frame(popup, padx=20, pady=20)
        frame.pack(fill="both", expand=True)
        tk.Label(frame, text=f"Payment History for Invoice {invoice_number}", font=("Helvetica", 12, "bold")).pack(pady=(0,10))
        tree = ttk.Treeview(frame, columns=("date", "amount", "by", "credit", "outstanding"), show="headings")
        tree.heading("date", text="Date")
        tree.heading("amount", text="Amount")
        tree.heading("by", text="Paid By")
        tree.heading("credit", text="Credit")
        tree.heading("outstanding", text="Outstanding")
        tree.column("date", width=120)
        tree.column("amount", width=100, anchor="e")
        tree.column("by", width=120)
        tree.column("credit", width=80, anchor="e")
        tree.column("outstanding", width=100, anchor="e")
        for p in payments:
            credit_val = f"{p[3]:.2f}" if p[3] and p[3] > 0 else "0.00"
            outstanding_val = f"{p[4]:.2f}" if p[4] and p[4] > 0 else "0.00"
            tree.insert("", "end", values=(p[0], f"{p[1]:.2f}", p[2] or "", credit_val, outstanding_val))
        tree.pack(fill="both", expand=True)
        tk.Button(frame, text="Close", command=popup.destroy).pack(pady=10)

    def _show_bill_details_popup(self, bill_id):
        """Show detailed bill information in an elegant table-style popup"""
        try:
            with get_db_connection() as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    SELECT b.id, b.invoice_number, b.month, b.year, b.amount, b.paid_amount, 
                           b.status, b.outstanding_amount, b.credit_amount, b.paid_date, 
                           b.paid_by, b.is_manual, c.name as customer_name, c.region, 
                           p.name as package_name, c.id as customer_id
                    FROM billing b
                    JOIN customers c ON b.customer_id = c.id
                    LEFT JOIN packages p ON c.package_id = p.id
                    WHERE b.id = ?
                ''', (bill_id,))
                bill_data = cursor.fetchone()
                
                if not bill_data:
                    messagebox.showerror("Error", "Bill not found")
                    return
                
                # Create popup window
                popup = tk.Toplevel(self)
                popup.title(f"Bill Details - {bill_data[12]} ({bill_data[2]:02d}/{bill_data[3]})")
                popup.geometry("700x550")
                popup.transient(self)
                popup.grab_set()
                popup.configure(bg=self.COLORS['background'])
                
                # Center the popup
                popup.update_idletasks()
                width = popup.winfo_width()
                height = popup.winfo_height()
                x = (popup.winfo_screenwidth() // 2) - (width // 2)
                y = (popup.winfo_screenheight() // 2) - (height // 2)
                popup.geometry(f"{width}x{height}+{x}+{y}")

                # Header frame
                header_frame = tk.Frame(popup, bg=self.COLORS['primary_accent'], height=60)
                header_frame.pack(fill="x", pady=(0, 15))
                
                header_label = tk.Label(header_frame, 
                                      text=f"Bill Details - {bill_data[12]} ({bill_data[2]:02d}/{bill_data[3]})", 
                                      font=("Helvetica", 16, "bold"),
                                      fg="#FFFFFF", bg=self.COLORS['primary_accent'])
                header_label.place(relx=0.5, rely=0.5, anchor="center")

                # Main content frame
                main_frame = tk.Frame(popup, bg=self.COLORS['card_bg'],
                                    highlightbackground=self.COLORS['border'],
                                    highlightthickness=1, padx=20, pady=20)
                main_frame.pack(fill="both", expand=True, padx=20, pady=(0, 20))

                # Create a canvas for scrollable content
                canvas = tk.Canvas(main_frame, bg=self.COLORS['card_bg'], highlightthickness=0)
                canvas.pack(side="left", fill="both", expand=True)

                scrollbar = ttk.Scrollbar(main_frame, orient="vertical", command=canvas.yview)
                scrollbar.pack(side="right", fill="y")

                canvas.configure(yscrollcommand=scrollbar.set)
                canvas.bind('<Configure>', lambda e: canvas.configure(scrollregion=canvas.bbox("all")))

                content_frame = tk.Frame(canvas, bg=self.COLORS['card_bg'])
                canvas.create_window((0, 0), window=content_frame, anchor="nw")

                def _on_mousewheel(event):
                    canvas.yview_scroll(int(-1*(event.delta/120)), "units")
                canvas.bind_all("<MouseWheel>", _on_mousewheel)

                # Bill information sections
                sections = [
                    {
                        "title": "Basic Information",
                        "fields": [
                            ("Bill ID", f"{bill_data[0]:04d}"),
                            ("Invoice #", bill_data[1] or "N/A"),
                            ("Customer", bill_data[12]),
                            ("Region", bill_data[13] or "N/A"),
                            ("Period", f"{bill_data[2]:02d}/{bill_data[3]}"),
                            ("Bill Type", "Manual Bill" if bill_data[11] else "Package Bill"),
                            ("Package", bill_data[14] if bill_data[14] else "N/A")
                        ]
                    },
                    {
                        "title": "Financial Details",
                        "fields": [
                            ("Amount", f"PKR {bill_data[4]:.2f}"),
                            ("Paid Amount", f"PKR {bill_data[5]:.2f}" if bill_data[5] else "PKR 0.00"),
                            ("Outstanding", f"PKR {bill_data[7]:.2f}" if bill_data[7] else "PKR 0.00"),
                            ("Credit", f"PKR {bill_data[8]:.2f}" if bill_data[8] else "PKR 0.00"),
                            ("Status", bill_data[6]),
                            ("Paid Date", bill_data[9] or "-"),
                            ("Paid By", bill_data[10] or "-")
                        ]
                    }
                ]

                for section in sections:
                    # Section header
                    section_frame = tk.Frame(content_frame, bg=self.COLORS['card_bg'])
                    section_frame.pack(fill="x", pady=(15, 5))
                    
                    tk.Label(section_frame, 
                            text=section["title"], 
                            font=("Helvetica", 12, "bold"),
                            bg=self.COLORS['card_bg'],
                            fg=self.COLORS['primary_accent']).pack(anchor="w")

                    # Section separator
                    separator = ttk.Separator(content_frame, orient="horizontal")
                    separator.pack(fill="x", pady=(0, 10))

                    # Fields grid
                    for i, (label, value) in enumerate(section["fields"]):
                        row_frame = tk.Frame(content_frame, bg=self.COLORS['card_bg'])
                        row_frame.pack(fill="x", pady=2)
                        
                        # Label
                        tk.Label(row_frame, 
                                text=label + ":", 
                                font=("Helvetica", 10),
                                bg=self.COLORS['card_bg'],
                                fg=self.COLORS['text_secondary'],
                                width=15,
                                anchor="w").pack(side="left", padx=(10, 5))
                        
                        # Value with special styling for status
                        if label == "Status":
                            color = "#4CAF50" if value == "Paid" else ("#FF9800" if value == "Partially Paid" else "#F44336")
                            tk.Label(row_frame, 
                                    text=value, 
                                    font=("Helvetica", 10, "bold"),
                                    bg=self.COLORS['card_bg'],
                                    fg=color).pack(side="left", anchor="w")
                        else:
                            tk.Label(row_frame, 
                                    text=value, 
                                    font=("Helvetica", 10),
                                    bg=self.COLORS['card_bg'],
                                    fg=self.COLORS['text_primary']).pack(side="left", anchor="w")

                # Payment history section
                payment_frame = tk.Frame(content_frame, bg=self.COLORS['card_bg'])
                payment_frame.pack(fill="x", pady=(20, 10))
                
                tk.Label(payment_frame, 
                        text="Payment History", 
                        font=("Helvetica", 12, "bold"),
                        bg=self.COLORS['card_bg'],
                        fg=self.COLORS['primary_accent']).pack(anchor="w")

                separator = ttk.Separator(content_frame, orient="horizontal")
                separator.pack(fill="x", pady=(0, 10))

                # Fetch payment history
                cursor.execute('''
                    SELECT payment_date, amount_paid, paid_by, credit_amount, outstanding_amount
                    FROM payment_history
                    WHERE invoice_number = ?
                    ORDER BY payment_date DESC
                ''', (bill_data[1],))
                payments = cursor.fetchall()

                if payments:
                    # Create treeview for payment history
                    history_tree = ttk.Treeview(
                        content_frame,
                        columns=("date", "amount", "by", "credit", "outstanding"),
                        show="headings",
                        height=min(4, len(payments)),
                        style="Treeview"
                    )
                    history_tree.heading("date", text="Date")
                    history_tree.heading("amount", text="Amount (PKR)")
                    history_tree.heading("by", text="Paid By")
                    history_tree.heading("credit", text="Credit")
                    history_tree.heading("outstanding", text="Outstanding")
                    history_tree.column("date", width=120, anchor="w")
                    history_tree.column("amount", width=100, anchor="e")
                    history_tree.column("by", width=120, anchor="w")
                    history_tree.column("credit", width=80, anchor="e")
                    history_tree.column("outstanding", width=100, anchor="e")

                    for p in payments:
                        credit_val = f"{p[3]:.2f}" if p[3] and p[3] > 0 else "0.00"
                        outstanding_val = f"{p[4]:.2f}" if p[4] and p[4] > 0 else "0.00"
                        history_tree.insert("", "end", values=(p[0], f"{p[1]:.2f}", p[2] or "", credit_val, outstanding_val))

                    history_tree.pack(fill="x", pady=(0, 20))
                else:
                    tk.Label(content_frame, 
                            text="No payment history found",
                            font=("Helvetica", 10, "italic"),
                            bg=self.COLORS['card_bg'],
                            fg=self.COLORS['text_secondary']).pack(anchor="w", pady=(0, 20))

                # Button frame
                button_frame = tk.Frame(popup, bg=self.COLORS['background'])
                button_frame.pack(fill="x", pady=(0, 15))

                # View customer button if not in specific customer view
                if not self.specific_customer_view:
                    view_customer_btn = tk.Button(
                        button_frame,
                        text="View Customer",
                        command=lambda: [self.nav_commands['show_customers'](bill_data[15]), popup.destroy()],
                        bg=self.COLORS['secondary_accent'],
                        fg="#FFFFFF",
                        font=("Helvetica", 10, "bold"),
                        relief=tk.FLAT,
                        padx=15,
                        pady=5,
                        activebackground=self.COLORS['primary_accent'],
                        activeforeground="#FFFFFF"
                    )
                    view_customer_btn.pack(side="left", padx=10)

                # Close button
                close_btn = tk.Button(
                    button_frame,
                    text="Close",
                    command=popup.destroy,
                    bg=self.COLORS['primary_accent'],
                    fg="#FFFFFF",
                    font=("Helvetica", 10, "bold"),
                    relief=tk.FLAT,
                    padx=20,
                    pady=5,
                    activebackground=self.COLORS['secondary_accent'],
                    activeforeground="#FFFFFF"
                )
                close_btn.pack(side="right", padx=10)

        except Exception as e:
            logger.error(f"Error showing bill details: {str(e)}")
            messagebox.showerror("Error", f"Failed to show bill details: {str(e)}")

    def _validate_bill_row(self, row):
        # No custom validation; always return empty list (no errors)
        return []

    def _update_previous_bills_amounts(self, cursor, customer_id, current_bill_id):
        """
        Clear outstanding/credit amounts from previous bills since only the latest bill
        should store these amounts for calculation purposes
        """
        try:
            cursor.execute('''
                UPDATE billing
                SET outstanding_amount = 0, credit_amount = 0
                WHERE customer_id = ? AND id < ?
            ''', (customer_id, current_bill_id))

        except Exception as e:
            logger.error(f"Error updating previous bills amounts: {str(e)}")

    def _calculate_month_bill_for_import(self, package_price, imported_credit, imported_outstanding):
        """
        Calculate Month Bill amount for imported data based on business rules:

        Rule 1: If credit > 0 and credit < package_price:
                Month Bill = package_price - credit

        Rule 2: If outstanding > 0:
                Month Bill = package_price + outstanding

        Rule 3: If credit >= package_price:
                Month Bill = 0 (will be handled by future bill generation)

        Note: Credit and outstanding cannot both be > 0 at the same time
        """
        # Validate business rule: credit and outstanding cannot both be > 0
        if imported_credit > 0 and imported_outstanding > 0:
            raise ValueError("Credit and outstanding cannot both be greater than 0 for the same customer")

        # Rule 1: Credit > 0 and credit < package_price
        if imported_credit > 0 and imported_credit < package_price:
            return package_price - imported_credit

        # Rule 2: Outstanding > 0 (and credit = 0)
        elif imported_outstanding > 0:
            return package_price + imported_outstanding

        # Rule 3: Credit >= package_price (will generate future bills)
        elif imported_credit >= package_price:
            return 0.0  # This bill will be fully paid by credit

        # Default case: No credit, no outstanding
        else:
            return package_price

    def _calculate_month_bill_amount(self, cursor, customer_id, current_bill_id, current_bill_amount, outstanding_amount=0.0):
        """
        Calculate Month Bill amount based on simplified business rules:

        For existing bills: Return the stored month_bill_amount or the original amount
        For new bills:
        Scenario 1: When there is any unpaid bill
                   new bill = Previous Month bill + new amount (Manual Amount/Product price/System generated Bill)

        Scenario 2A: When all bills are paid and there are previous outstandings
                    month bill = new amount + Previous Outstandings

        Scenario 2B: When all bills are paid and there are previous credits
                    month bill = new amount - Previous Credits
        """
        try:
            # If this is an existing bill (not temp ID), check if it already has a month_bill_amount
            if current_bill_id != 999999:
                cursor.execute('''
                    SELECT month_bill_amount, amount FROM billing WHERE id = ?
                ''', (current_bill_id,))
                existing_bill = cursor.fetchone()
                if existing_bill:
                    stored_month_bill = existing_bill[0]
                    if stored_month_bill and stored_month_bill > 0:
                        return stored_month_bill
                    # If no stored month_bill_amount, use the original amount
                    return existing_bill[1]

            # For new bills (temp ID), calculate based on scenarios
            # Check if customer has any unpaid bills (excluding current bill)
            cursor.execute('''
                SELECT COUNT(*), SUM(amount - COALESCE(paid_amount, 0))
                FROM billing
                WHERE customer_id = ? AND status != 'Paid' AND id != ?
            ''', (customer_id, current_bill_id))

            unpaid_result = cursor.fetchone()
            unpaid_count = unpaid_result[0] or 0
            unpaid_total = unpaid_result[1] or 0.0

            # Scenario 1: When there is any unpaid bill
            if unpaid_count > 0:
                # new bill = Previous Month bill + new amount
                month_bill = unpaid_total + current_bill_amount
                logger.debug(f"Scenario 1 - Unpaid bills exist: Month Bill = {unpaid_total} + {current_bill_amount} = {month_bill}")
                return month_bill

            # Scenario 2: When all bills are paid
            else:
                # Get customer's previous outstanding and credit amounts
                cursor.execute('''
                    SELECT outstanding_amount, credit_balance
                    FROM customers
                    WHERE id = ?
                ''', (customer_id,))

                customer_result = cursor.fetchone()
                if customer_result:
                    previous_outstanding = customer_result[0] or 0.0
                    previous_credit = customer_result[1] or 0.0
                else:
                    previous_outstanding = 0.0
                    previous_credit = 0.0

                # Scenario 2A: Previous Outstanding exists
                if previous_outstanding > 0:
                    month_bill = current_bill_amount + previous_outstanding
                    logger.debug(f"Scenario 2A - Previous Outstanding: Month Bill = {current_bill_amount} + {previous_outstanding} = {month_bill}")
                    return month_bill

                # Scenario 2B: Previous Credit exists
                elif previous_credit > 0:
                    month_bill = max(0, current_bill_amount - previous_credit)
                    logger.debug(f"Scenario 2B - Previous Credit: Month Bill = {current_bill_amount} - {previous_credit} = {month_bill}")
                    return month_bill

                # Default case: No outstanding, no credit
                else:
                    logger.debug(f"Default case: Month Bill = {current_bill_amount}")
                    return current_bill_amount

        except Exception as e:
            logger.error(f"Error calculating month bill amount: {str(e)}")
            return current_bill_amount  # Fallback to current bill amount

    def _generate_future_bills_with_credit(self, cursor, customer_id, available_credit, package_price):
        """
        Generate and pay future bills automatically when credit >= package price
        Continue until credit = 0 or credit < package price
        """
        try:
            from datetime import datetime, timedelta

            remaining_credit = available_credit
            current_date = datetime.now()

            # Get the latest bill to determine next month
            cursor.execute('''
                SELECT month, year FROM billing
                WHERE customer_id = ?
                ORDER BY year DESC, month DESC, id DESC
                LIMIT 1
            ''', (customer_id,))

            latest_bill = cursor.fetchone()
            if latest_bill:
                next_month = latest_bill[0] + 1
                next_year = latest_bill[1]
                if next_month > 12:
                    next_month = 1
                    next_year += 1
            else:
                next_month = current_date.month + 1
                next_year = current_date.year
                if next_month > 12:
                    next_month = 1
                    next_year += 1

            # Generate bills while credit can cover them
            while remaining_credit >= package_price:
                # Check if bill already exists for this month/year
                cursor.execute('''
                    SELECT id FROM billing
                    WHERE customer_id = ? AND month = ? AND year = ?
                ''', (customer_id, next_month, next_year))

                if cursor.fetchone():
                    # Bill already exists, move to next month
                    next_month += 1
                    if next_month > 12:
                        next_month = 1
                        next_year += 1
                    continue

                # Generate invoice number
                cursor.execute("SELECT MAX(CAST(SUBSTR(invoice_number, 5) AS INTEGER)) FROM billing WHERE invoice_number IS NOT NULL")
                max_inv_num = cursor.fetchone()[0] or 0
                invoice_number = f"INV-{str(max_inv_num + 1).zfill(4)}"

                # Create paid bill
                cursor.execute('''
                    INSERT INTO billing (
                        customer_id, month, year, amount, month_bill_amount, paid_amount, outstanding_amount,
                        credit_amount, status, invoice_number, is_manual, paid_date, paid_by
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, 'Paid', ?, 0, datetime('now'), 'System Credit')
                ''', (customer_id, next_month, next_year, package_price, package_price, package_price, 0, 0, invoice_number))

                bill_id = cursor.lastrowid

                # Record payment history
                cursor.execute('''
                    INSERT INTO payment_history (
                        bill_id, invoice_number, customer_id, payment_date, amount_paid,
                        credit_amount, outstanding_amount, payment_method, recorded_by
                    ) VALUES (?, ?, ?, datetime('now'), ?, 0, 0, 'Credit Applied', 'System')
                ''', (bill_id, invoice_number, customer_id, package_price))

                remaining_credit -= package_price

                logger.info(f"Generated future bill for {next_month:02d}/{next_year} paid with credit")

                # Move to next month
                next_month += 1
                if next_month > 12:
                    next_month = 1
                    next_year += 1

            # Update customer's credit balance with remaining credit
            if remaining_credit > 0:
                cursor.execute('''
                    UPDATE customers
                    SET credit_balance = ?
                    WHERE id = ?
                ''', (remaining_credit, customer_id))
                logger.info(f"Remaining credit {remaining_credit:.2f} stored in customer balance")
            else:
                # No remaining credit, set customer credit balance to 0
                cursor.execute('''
                    UPDATE customers
                    SET credit_balance = 0
                    WHERE id = ?
                ''', (customer_id,))

        except Exception as e:
            logger.error(f"Error generating future bills with credit: {str(e)}")

    def _generate_missing_invoice_numbers(self):
        """Generate invoice numbers for bills that don't have them"""
        try:
            with get_db_connection() as conn:
                cursor = conn.cursor()

                # Find bills without invoice numbers
                cursor.execute('''
                    SELECT id FROM billing
                    WHERE invoice_number IS NULL OR invoice_number = ''
                    ORDER BY id ASC
                ''')
                bills_without_invoices = cursor.fetchall()

                if not bills_without_invoices:
                    return

                # Get the highest existing invoice number
                cursor.execute('''
                    SELECT MAX(CAST(SUBSTR(invoice_number, 5) AS INTEGER))
                    FROM billing
                    WHERE invoice_number IS NOT NULL
                    AND invoice_number LIKE 'INV-%'
                    AND LENGTH(invoice_number) >= 8
                ''')
                max_inv_result = cursor.fetchone()
                max_inv_num = max_inv_result[0] if max_inv_result and max_inv_result[0] else 0

                # Generate invoice numbers for bills that don't have them
                for i, (bill_id,) in enumerate(bills_without_invoices):
                    invoice_number = f"INV-{str(max_inv_num + i + 1).zfill(4)}"
                    cursor.execute('''
                        UPDATE billing SET invoice_number = ? WHERE id = ?
                    ''', (invoice_number, bill_id))

                conn.commit()
                logger.info(f"Generated {len(bills_without_invoices)} missing invoice numbers")

        except Exception as e:
            logger.error(f"Error generating missing invoice numbers: {str(e)}")
            # Don't show error to user as this is a background operation


