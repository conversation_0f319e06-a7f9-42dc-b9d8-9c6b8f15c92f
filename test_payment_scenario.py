#!/usr/bin/env python3
"""
Test script to simulate payment scenarios and verify that:
1. Invoice numbers are generated properly
2. Credit and outstanding amounts are stored in payment history
3. Payment history displays correctly
"""

import sqlite3
import sys
import os
from datetime import datetime

# Add the current directory to the path so we can import our modules
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from database import Database

def create_test_bill():
    """Create a test bill for payment testing"""
    print("Creating test bill...")
    
    db = Database()
    
    with db.get_connection_context() as conn:
        c = conn.cursor()
        
        # Get a customer ID (use first customer)
        c.execute("SELECT id, name FROM customers LIMIT 1")
        customer = c.fetchone()
        if not customer:
            print("No customers found. Please add a customer first.")
            return None
            
        customer_id, customer_name = customer
        print(f"Using customer: {customer_name} (ID: {customer_id})")
        
        # Create a test bill
        current_date = datetime.now()
        month = current_date.month
        year = current_date.year
        amount = 1500.0  # PKR 1500
        
        c.execute('''
            INSERT INTO billing 
            (customer_id, month, year, amount, paid_amount, status, outstanding_amount, credit_amount, is_manual)
            VALUES (?, ?, ?, ?, 0, 'Unpaid', ?, 0, 1)
        ''', (customer_id, month, year, amount, amount))
        
        bill_id = c.lastrowid
        print(f"Created test bill ID: {bill_id} for {amount} PKR")
        
        return bill_id, customer_id, amount

def test_payment_scenarios(bill_id, customer_id, bill_amount):
    """Test different payment scenarios"""
    print(f"\nTesting payment scenarios for bill {bill_id}...")
    
    db = Database()
    
    # Scenario 1: Partial payment (less than bill amount)
    print("\n--- Scenario 1: Partial Payment ---")
    payment_amount = 1000.0  # Pay 1000 out of 1500
    
    with db.get_connection_context() as conn:
        c = conn.cursor()
        
        # Get current bill state
        c.execute('SELECT amount, paid_amount, outstanding_amount, credit_amount FROM billing WHERE id = ?', (bill_id,))
        bill_data = c.fetchone()
        print(f"Before payment - Amount: {bill_data[0]}, Paid: {bill_data[1]}, Outstanding: {bill_data[2]}, Credit: {bill_data[3]}")
        
        # Calculate new values
        new_paid = bill_data[1] + payment_amount
        new_outstanding = max(bill_data[0] - new_paid, 0)
        new_credit = max(new_paid - bill_data[0], 0)
        new_status = 'Paid' if new_outstanding == 0 else 'Partially Paid'
        
        # Update billing record
        c.execute('''
            UPDATE billing SET
            paid_amount = ?,
            outstanding_amount = ?,
            credit_amount = ?,
            status = ?,
            paid_date = CURRENT_TIMESTAMP,
            paid_by = ?
            WHERE id = ?
        ''', (new_paid, new_outstanding, new_credit, new_status, 'Test User', bill_id))
        
        # Get or generate invoice number
        c.execute('SELECT invoice_number FROM billing WHERE id = ?', (bill_id,))
        invoice_result = c.fetchone()
        invoice_number = invoice_result[0] if invoice_result and invoice_result[0] else None
        
        if not invoice_number:
            c.execute("SELECT MAX(CAST(SUBSTR(invoice_number, 5) AS INTEGER)) FROM billing WHERE invoice_number IS NOT NULL AND invoice_number LIKE 'INV-%'")
            max_inv_num = c.fetchone()[0] or 0
            invoice_number = f"INV-{str(max_inv_num + 1).zfill(4)}"
            c.execute('UPDATE billing SET invoice_number = ? WHERE id = ?', (invoice_number, bill_id))
        
        # Record in payment history
        c.execute('''
            INSERT INTO payment_history
            (bill_id, invoice_number, customer_id, payment_date, amount_paid,
             credit_amount, outstanding_amount, remaining_outstanding, applied_credit,
             payment_method, recorded_by, timestamp)
            VALUES (?, ?, ?, CURRENT_TIMESTAMP, ?, ?, ?, ?, ?, ?, ?, datetime('now'))
        ''', (bill_id, invoice_number, customer_id, payment_amount, new_credit, new_outstanding, new_outstanding, new_credit, 'Test', 'Test User'))
        
        print(f"Payment processed: {payment_amount} PKR")
        print(f"Invoice number: {invoice_number}")
        print(f"After payment - Outstanding: {new_outstanding}, Credit: {new_credit}, Status: {new_status}")
        
    # Scenario 2: Overpayment (more than remaining amount)
    print("\n--- Scenario 2: Overpayment ---")
    payment_amount = 800.0  # Pay 800 more (total will be 1800, creating 300 credit)
    
    with db.get_connection_context() as conn:
        c = conn.cursor()
        
        # Get current bill state
        c.execute('SELECT amount, paid_amount, outstanding_amount, credit_amount FROM billing WHERE id = ?', (bill_id,))
        bill_data = c.fetchone()
        print(f"Before payment - Amount: {bill_data[0]}, Paid: {bill_data[1]}, Outstanding: {bill_data[2]}, Credit: {bill_data[3]}")
        
        # Calculate new values
        new_paid = bill_data[1] + payment_amount
        new_outstanding = max(bill_data[0] - new_paid, 0)
        new_credit = max(new_paid - bill_data[0], 0)
        new_status = 'Paid' if new_outstanding == 0 else 'Partially Paid'
        
        # Update billing record
        c.execute('''
            UPDATE billing SET
            paid_amount = ?,
            outstanding_amount = ?,
            credit_amount = ?,
            status = ?,
            paid_date = CURRENT_TIMESTAMP,
            paid_by = ?
            WHERE id = ?
        ''', (new_paid, new_outstanding, new_credit, new_status, 'Test User', bill_id))
        
        # Record in payment history
        c.execute('''
            INSERT INTO payment_history
            (bill_id, invoice_number, customer_id, payment_date, amount_paid,
             credit_amount, outstanding_amount, remaining_outstanding, applied_credit,
             payment_method, recorded_by, timestamp)
            VALUES (?, ?, ?, CURRENT_TIMESTAMP, ?, ?, ?, ?, ?, ?, ?, datetime('now'))
        ''', (bill_id, invoice_number, customer_id, payment_amount, new_credit, new_outstanding, new_outstanding, new_credit, 'Test', 'Test User'))
        
        print(f"Payment processed: {payment_amount} PKR")
        print(f"After payment - Outstanding: {new_outstanding}, Credit: {new_credit}, Status: {new_status}")

def verify_payment_history(bill_id):
    """Verify that payment history is recorded correctly"""
    print(f"\n--- Verifying Payment History for Bill {bill_id} ---")
    
    db = Database()
    
    with db.get_connection_context() as conn:
        c = conn.cursor()
        
        # Get payment history
        c.execute('''
            SELECT payment_date, amount_paid, credit_amount, outstanding_amount, 
                   remaining_outstanding, applied_credit, recorded_by, invoice_number
            FROM payment_history 
            WHERE bill_id = ? 
            ORDER BY payment_date ASC
        ''', (bill_id,))
        
        payments = c.fetchall()
        
        if payments:
            print(f"Found {len(payments)} payment history records:")
            for i, payment in enumerate(payments, 1):
                print(f"  Payment {i}:")
                print(f"    Date: {payment[0]}")
                print(f"    Amount Paid: {payment[1]} PKR")
                print(f"    Credit Amount: {payment[2]} PKR")
                print(f"    Outstanding Amount: {payment[3]} PKR")
                print(f"    Remaining Outstanding: {payment[4]} PKR")
                print(f"    Applied Credit: {payment[5]} PKR")
                print(f"    Recorded By: {payment[6]}")
                print(f"    Invoice Number: {payment[7]}")
                print()
        else:
            print("No payment history found!")

def main():
    """Run payment scenario tests"""
    print("=== Testing Payment Scenarios ===\n")
    
    try:
        # Create a test bill
        result = create_test_bill()
        if not result:
            return
            
        bill_id, customer_id, bill_amount = result
        
        # Test payment scenarios
        test_payment_scenarios(bill_id, customer_id, bill_amount)
        
        # Verify payment history
        verify_payment_history(bill_id)
        
        print("✅ Payment scenario testing completed successfully!")
        print("\nKey features verified:")
        print("- Invoice numbers are generated automatically")
        print("- Credit and outstanding amounts are calculated correctly")
        print("- Payment history stores all required information")
        print("- Multiple payment scenarios work as expected")
        
    except Exception as e:
        print(f"\n❌ Error during payment testing: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
