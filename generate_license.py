import logging
import platform
import subprocess
import uuid
import hashlib
from datetime import datetime, timedelta
import json
import argparse
import sys

def get_hardware_id():
    """Generate hardware fingerprint (consistent with main.py)"""
    try:
        if platform.system() == 'Windows':
            try:
                disk_serial = subprocess.check_output(
                    "wmic diskdrive get serialnumber", 
                    shell=True, 
                    stderr=subprocess.DEVNULL
                ).decode().split('\n')[1].strip()
            except Exception as e:
                logging.warning(f"Could not get disk serial: {str(e)}")
                disk_serial = "unknown"
        else:
            disk_serial = "unknown"

        system_info = {
            'machine': platform.machine(),
            'processor': platform.processor(),
            'node': platform.node(),
            'disk_serial': disk_serial,
            'mac': ':'.join(['{:02x}'.format((uuid.getnode() >> elements) & 0xff) 
                            for elements in range(5, -1, -1)])
        }
        return hashlib.sha256(str(system_info).encode()).hexdigest()[:12]
    except Exception as e:
        logging.error(f"Hardware ID generation failed: {str(e)}")
        return "unknown"

def generate_license(customer_name, email, license_type="full", days_valid=365, activations=1, bind_to_hardware=False):
    """
    Enhanced license generator with:
    - License types (full/trial)
    - Activation limits
    - Hardware binding
    - Expiration dates
    """
    SECRET_SALT = "YourSecretSalt123!"  # MUST match license_manager.py
    
    # Validate inputs
    if not customer_name or not email:
        raise ValueError("Customer name and email are required")
    
    if days_valid < 0:
        raise ValueError("Days valid must be positive (0 for permanent)")
    
    # Create license data
    license_data = {
        'version': 2,  # Must match version in license_manager.py
        'customer': customer_name.strip(),
        'email': email.strip().lower(),
        'type': license_type.lower(),
        'created': datetime.now().strftime("%Y-%m-%d"),
        'activations_allowed': max(1, activations),
        'activations_used': 0,
        'hwid': None,
        'signature': None
    }
    
    # Add expiration if specified
    expiry_date = None
    if days_valid > 0:
        expiry_date = (datetime.now() + timedelta(days=days_valid)).strftime("%Y-%m-%d")
        license_data['expiry'] = expiry_date
    
    # Add hardware binding if requested
    if bind_to_hardware:
        license_data['hwid'] = get_hardware_id()
        if license_data['hwid'] == "unknown":
            logging.warning("Could not get reliable hardware ID")
    
    # Generate license key
    combined = f"{customer_name}{email}{license_type}{SECRET_SALT}".encode()
    license_key = hashlib.sha256(combined).hexdigest()[:16].upper()
    
    # Create anti-tamper signature
    signature_data = f"{license_key}{license_data['created']}{SECRET_SALT}"
    license_data['signature'] = hashlib.md5(signature_data.encode()).hexdigest()
    
    # Create complete license file
    license_file = {
        'key': license_key,
        'data': license_data
    }
    
    # Generate filename
    safe_name = "".join(c for c in customer_name[:20] if c.isalnum())
    filename = f"license_{safe_name}_{license_type}.key"
    
    # Save to file
    with open(filename, 'w') as f:
        json.dump(license_file, f, indent=2)
    
    # Print summary
    print(f"\n{'='*40}")
    print(f"LICENSE GENERATION SUCCESSFUL")
    print(f"{'='*40}")
    print(f"Customer: {license_data['customer']}")
    print(f"Email: {license_data['email']}")
    print(f"Type: {license_type.upper()}")
    print(f"Key: {license_key}")
    print(f"Expires: {expiry_date if days_valid else 'Never'}")
    print(f"Activations: {license_data['activations_allowed']}")
    print(f"Hardware Bound: {'Yes' if bind_to_hardware else 'No'}")
    print(f"Saved to: {filename}")
    print(f"{'='*40}\n")
    
    return license_key

if __name__ == "__main__":
    # Configure basic logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )
    
    parser = argparse.ArgumentParser(description="Generate enhanced license keys")
    parser.add_argument("customer", help="Customer name (required)")
    parser.add_argument("email", help="Customer email (required)")
    parser.add_argument("--type", choices=["full", "trial"], default="full", 
                        help="License type (default: full)")
    parser.add_argument("--days", type=int, default=365, 
                        help="Validity period in days (0=permanent, default: 365)")
    parser.add_argument("--activations", type=int, default=1, 
                        help="Number of allowed activations (default: 1)")
    parser.add_argument("--hardware", action="store_true", 
                        help="Bind license to current hardware")
    
    args = parser.parse_args()
    
    try:
        generate_license(
            args.customer,
            args.email,
            license_type=args.type,
            days_valid=args.days,
            activations=args.activations,
            bind_to_hardware=args.hardware
        )
    except Exception as e:
        logging.error(f"License generation failed: {str(e)}")
        sys.exit(1)
