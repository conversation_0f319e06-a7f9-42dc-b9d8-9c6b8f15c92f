#!/usr/bin/env python3
"""
License Setup Utility for CRM System
This script helps set up and validate the license file in the correct location.
"""

import os
import json
import sys
import logging
from pathlib import Path

def setup_logging():
    """Configure logging"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )

def get_license_dir():
    """Get the license directory path"""
    appdata = os.getenv('APPDATA', '')
    if not appdata:
        # Fallback for systems without APPDATA
        appdata = os.path.expanduser('~')
    
    license_dir = os.path.join(appdata, 'CRM_System')
    return license_dir

def create_license_file(license_key="76C4EDB3C004BA89", expiry_date="2025-12-31"):
    """Create a license file with the specified parameters"""
    license_dir = get_license_dir()
    license_path = os.path.join(license_dir, 'license.json')
    
    # Create directory if it doesn't exist
    os.makedirs(license_dir, exist_ok=True)
    
    # License data
    license_data = {
        "license_key": license_key,
        "expiry_date": expiry_date,
        "customer_name": "Sample Customer",
        "license_type": "standard",
        "features": ["basic", "advanced", "premium"],
        "activation_date": "2024-01-01",
        "max_users": 10,
        "version": "1.0"
    }
    
    try:
        # Write license file
        with open(license_path, 'w', encoding='utf-8') as f:
            json.dump(license_data, f, indent=4, ensure_ascii=False)
        
        print(f"✅ License file created successfully at: {license_path}")
        print(f"📄 License Key: {license_key}")
        print(f"📅 Expiry Date: {expiry_date}")
        
        return True
        
    except Exception as e:
        print(f"❌ Failed to create license file: {str(e)}")
        return False

def validate_license_file():
    """Validate the existing license file"""
    license_dir = get_license_dir()
    license_path = os.path.join(license_dir, 'license.json')
    
    if not os.path.exists(license_path):
        print(f"❌ License file not found at: {license_path}")
        return False
    
    try:
        with open(license_path, 'r', encoding='utf-8') as f:
            license_data = json.load(f)
        
        print(f"✅ License file is valid JSON")
        print(f"📄 License Key: {license_data.get('license_key', 'N/A')}")
        print(f"📅 Expiry Date: {license_data.get('expiry_date', 'N/A')}")
        print(f"👤 Customer: {license_data.get('customer_name', 'N/A')}")
        
        return True
        
    except json.JSONDecodeError as e:
        print(f"❌ Invalid JSON format: {str(e)}")
        return False
    except Exception as e:
        print(f"❌ Error reading license file: {str(e)}")
        return False

def show_license_info():
    """Show information about the license system"""
    license_dir = get_license_dir()
    license_path = os.path.join(license_dir, 'license.json')
    
    print("🔍 License System Information:")
    print(f"📁 License Directory: {license_dir}")
    print(f"📄 License File: {license_path}")
    print(f"📊 File Exists: {'Yes' if os.path.exists(license_path) else 'No'}")
    
    if os.path.exists(license_path):
        try:
            with open(license_path, 'r', encoding='utf-8') as f:
                content = f.read()
            print(f"📏 File Size: {len(content)} bytes")
        except Exception as e:
            print(f"❌ Error reading file: {str(e)}")

def main():
    """Main function"""
    setup_logging()
    
    print("🚀 CRM System License Setup Utility")
    print("=" * 50)
    
    if len(sys.argv) > 1:
        command = sys.argv[1].lower()
        
        if command == "create":
            license_key = sys.argv[2] if len(sys.argv) > 2 else "76C4EDB3C004BA89"
            expiry_date = sys.argv[3] if len(sys.argv) > 3 else "2025-12-31"
            create_license_file(license_key, expiry_date)
            
        elif command == "validate":
            validate_license_file()
            
        elif command == "info":
            show_license_info()
            
        else:
            print("❌ Unknown command. Use: create, validate, or info")
    else:
        # Interactive mode
        print("\nAvailable commands:")
        print("1. Create license file")
        print("2. Validate existing license")
        print("3. Show license info")
        print("4. Exit")
        
        try:
            choice = input("\nEnter your choice (1-4): ").strip()
            
            if choice == "1":
                license_key = input("Enter license key (or press Enter for default): ").strip()
                if not license_key:
                    license_key = "76C4EDB3C004BA89"
                
                expiry_date = input("Enter expiry date (YYYY-MM-DD) or press Enter for default: ").strip()
                if not expiry_date:
                    expiry_date = "2025-12-31"
                
                create_license_file(license_key, expiry_date)
                
            elif choice == "2":
                validate_license_file()
                
            elif choice == "3":
                show_license_info()
                
            elif choice == "4":
                print("👋 Goodbye!")
                
            else:
                print("❌ Invalid choice")
                
        except KeyboardInterrupt:
            print("\n👋 Goodbye!")

if __name__ == "__main__":
    main() 