#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to fix all remaining sqlite3.connect() calls in the CRM application.
This script will replace direct sqlite3.connect() calls with get_db_connection() calls.
"""

import os
import re
import sys

def fix_file_connections(file_path):
    """Fix sqlite3.connect calls in a single file"""
    if not os.path.exists(file_path):
        print(f"File not found: {file_path}")
        return False
    
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    original_content = content
    
    # Check if database_utils import already exists
    if 'from database_utils import get_db_connection' not in content:
        # Add the import after existing imports
        import_pattern = r'(from database import Database\n)'
        replacement = r'\1from database_utils import get_db_connection\n'
        content = re.sub(import_pattern, replacement, content)
        
        # If that didn't work, try adding after other imports
        if 'from database_utils import get_db_connection' not in content:
            import_pattern = r'(import sqlite3\n)'
            replacement = r'\1from database_utils import get_db_connection\n'
            content = re.sub(import_pattern, replacement, content)
    
    # Replace sqlite3.connect patterns
    patterns = [
        # Pattern 1: with sqlite3.connect(self.db_path, timeout=X) as conn:
        (r'with sqlite3\.connect\(self\.db_path,\s*timeout=\d+\)\s*as\s*conn:',
         'with get_db_connection() as conn:'),
        
        # Pattern 2: with sqlite3.connect(db_path, timeout=X) as conn:
        (r'with sqlite3\.connect\(db_path,?\s*timeout=\d+\)\s*as\s*conn:',
         'with get_db_connection() as conn:'),
        
        # Pattern 3: with sqlite3.connect(path) as conn:
        (r'with sqlite3\.connect\([^)]+\)\s*as\s*conn:',
         'with get_db_connection() as conn:'),
        
        # Pattern 4: conn = sqlite3.connect(...)
        (r'conn\s*=\s*sqlite3\.connect\([^)]+\)',
         'conn = get_db_connection().__enter__()'),
    ]
    
    changes_made = False
    for pattern, replacement in patterns:
        new_content = re.sub(pattern, replacement, content)
        if new_content != content:
            content = new_content
            changes_made = True
    
    # Write back if changes were made
    if content != original_content:
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content)
        print(f"Fixed connections in: {file_path}")
        return True
    else:
        print(f"No changes needed in: {file_path}")
        return False

def main():
    """Main function to fix all files"""
    files_to_fix = [
        'views/customers.py',
        'views/register.py', 
        'views/forgot_password.py',
        'login.py',
        'views/backup_restore.py',
        'views/billing.py',
        'views/products.py',
        'views/packages.py',
        'views/regions.py',
        'views/stock.py',
    ]
    
    print("Fixing database connections in CRM application...")
    print("=" * 50)
    
    fixed_count = 0
    for file_path in files_to_fix:
        if os.path.exists(file_path):
            if fix_file_connections(file_path):
                fixed_count += 1
        else:
            print(f"File not found: {file_path}")
    
    print("=" * 50)
    print(f"Fixed {fixed_count} files")
    print("\nManual fixes still needed:")
    print("1. Check for any remaining 'conn.close()' calls that should be removed")
    print("2. Look for Database() instances created inside methods")
    print("3. Test all functionality thoroughly")
    print("4. Monitor logs for any remaining lock errors")

if __name__ == "__main__":
    main()
