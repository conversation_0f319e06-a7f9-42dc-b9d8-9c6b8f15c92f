import tkinter as tk
from tkinter import ttk, messagebox
import sqlite3
from database_utils import get_db_connection
import os
import bcrypt
import re
from PIL import Image, ImageTk

class Register(tk.Frame):
    def __init__(self, parent, nav_commands):
        super().__init__(parent)
        self.nav_commands = nav_commands
        self.parent = parent

        # Color scheme matching the login page
        self.COLORS = {
            'background': '#F8F9FA',
            'card_bg': '#FFFFFF',
            'primary_accent': '#4A6FA5',
            'secondary_accent': '#6C8FC7',
            'text_primary': '#2D3748',
            'text_secondary': '#718096',
            'button_start': '#4A6FA5',
            'button_end': '#3A5A8C',
            'transparent': self.parent.cget('bg'),
            'warning': '#E53E3E',
            'input_bg': '#EDF2F7',
            'border': '#E2E8F0'
        }

        self.configure(bg=self.COLORS['background'])
        self.pack(fill="both", expand=True)

        # Create a canvas for the background image
        self.canvas = tk.Canvas(self, bg=self.COLORS['background'], highlightthickness=0)
        self.canvas.pack(fill="both", expand=True)

        # Load and display background image
        self.bg_image = None
        self.bg_photo = None
        self.load_background()

        # Main container frame
        self.main_frame = tk.Frame(self.canvas, bg=self.COLORS['background'])
        self.main_frame.place(relx=0.5, rely=0.5, anchor="center")

        # Create widgets
        self.create_widgets()
        
        # Add the footer
        self.create_footer()

    def load_background(self):
        try:
            base_path = os.path.dirname(os.path.abspath(__file__))
            img_path = os.path.join(base_path, '..', 'assets', 'dashboard', 'background1.png')
            img_path = os.path.normpath(img_path)
            if not os.path.exists(img_path):
                img_path = r"D:\CRM_System\assets\dashboard\background1.png"
            self.original_bg_image = Image.open(img_path)
            self.resize_background()
        except Exception as e:
            messagebox.showerror("Error", f"Error loading background image: {str(e)}")
            self.canvas.configure(bg=self.COLORS['background'])

    def resize_background(self, event=None):
        if not hasattr(self, 'original_bg_image') or not self.original_bg_image:
            return
            
        window_width = self.winfo_width()
        window_height = self.winfo_height()
        
        if window_width < 1 or window_height < 1:
            return

        try:
            resized_image = self.original_bg_image.resize((window_width, window_height), Image.LANCZOS)
            self.bg_photo = ImageTk.PhotoImage(resized_image)
            self.canvas.create_image(0, 0, image=self.bg_photo, anchor="nw")
        except Exception as e:
            messagebox.showerror("Error", f"Error resizing background: {str(e)}")

    def create_widgets(self):
        # Register card
        self.register_card = tk.Frame(
            self.main_frame,
            bg=self.COLORS['card_bg'],
            padx=40,
            pady=40,
            relief="flat",
            borderwidth=0
        )
        self.register_card.pack(pady=20)

        # Title section
        self.title_frame = tk.Frame(self.register_card, bg=self.COLORS['card_bg'])
        self.title_frame.pack(pady=(0, 30))
        
        self.title_label = tk.Label(
            self.title_frame,
            text="SANI BROADBAND",
            font=("Helvetica", 24, "bold"),
            fg=self.COLORS['primary_accent'],
            bg=self.COLORS['card_bg']
        )
        self.title_label.pack()
        
        self.subtitle_label = tk.Label(
            self.title_frame,
            text="Create your account",
            font=("Helvetica", 12),
            fg=self.COLORS['text_secondary'],
            bg=self.COLORS['card_bg']
        )
        self.subtitle_label.pack(pady=(5, 0))

        # Input fields frame
        self.input_frame = tk.Frame(self.register_card, bg=self.COLORS['card_bg'])
        self.input_frame.pack(fill="x")

        # Username field
        self.username_label = tk.Label(
            self.input_frame,
            text="Username",
            font=("Helvetica", 10),
            fg=self.COLORS['text_primary'],
            bg=self.COLORS['card_bg'],
            anchor='w'
        )
        self.username_label.pack(fill="x", pady=(10, 5))
        
        self.username_entry = ttk.Entry(
            self.input_frame,
            font=("Helvetica", 12),
            width=30,
            style='Modern.TEntry'
        )
        self.username_entry.pack(fill="x", ipady=8)

        # Email field
        self.email_label = tk.Label(
            self.input_frame,
            text="Email",
            font=("Helvetica", 10),
            fg=self.COLORS['text_primary'],
            bg=self.COLORS['card_bg'],
            anchor='w'
        )
        self.email_label.pack(fill="x", pady=(15, 5))
        
        self.email_entry = ttk.Entry(
            self.input_frame,
            font=("Helvetica", 12),
            width=30,
            style='Modern.TEntry'
        )
        self.email_entry.pack(fill="x", ipady=8)

        # Password field
        self.password_label = tk.Label(
            self.input_frame,
            text="Password",
            font=("Helvetica", 10),
            fg=self.COLORS['text_primary'],
            bg=self.COLORS['card_bg'],
            anchor='w'
        )
        self.password_label.pack(fill="x", pady=(15, 5))
        
        self.password_entry = ttk.Entry(
            self.input_frame,
            font=("Helvetica", 12),
            show="•",
            width=30,
            style='Modern.TEntry'
        )
        self.password_entry.pack(fill="x", ipady=8)

        # Confirm Password field
        self.confirm_password_label = tk.Label(
            self.input_frame,
            text="Confirm Password",
            font=("Helvetica", 10),
            fg=self.COLORS['text_primary'],
            bg=self.COLORS['card_bg'],
            anchor='w'
        )
        self.confirm_password_label.pack(fill="x", pady=(15, 5))
        
        self.confirm_password_entry = ttk.Entry(
            self.input_frame,
            font=("Helvetica", 12),
            show="•",
            width=30,
            style='Modern.TEntry'
        )
        self.confirm_password_entry.pack(fill="x", ipady=8)

        # Register button
        self.register_btn = tk.Button(
            self.input_frame,
            text="Sign Up",
            font=("Helvetica", 12, "bold"),
            bg=self.COLORS['primary_accent'],
            fg="#FFFFFF",
            activebackground=self.COLORS['secondary_accent'],
            activeforeground="#FFFFFF",
            width=30,
            height=1,
            bd=0,
            relief="flat",
            command=self.register
        )
        self.register_btn.pack(fill="x", pady=(20, 10), ipady=10)

        # Divider
        self.divider = tk.Frame(
            self.input_frame,
            bg=self.COLORS['border'],
            height=1
        )
        self.divider.pack(fill="x", pady=10)

        # Back to login link
        self.login_frame = tk.Frame(self.input_frame, bg=self.COLORS['card_bg'])
        self.login_frame.pack()
        
        self.login_label = tk.Label(
            self.login_frame,
            text="Already have an account?",
            font=("Helvetica", 10),
            fg=self.COLORS['text_secondary'],
            bg=self.COLORS['card_bg']
        )
        self.login_label.pack(side="left")
        
        self.login_link = tk.Label(
            self.login_frame,
            text="Login",
            font=("Helvetica", 10, "bold"),
            fg=self.COLORS['primary_accent'],
            bg=self.COLORS['card_bg'],
            cursor="hand2"
        )
        self.login_link.pack(side="left", padx=5)
        self.login_link.bind("<Button-1>", lambda e: self.nav_commands['show_login']())

        # Configure styles
        self.style = ttk.Style()
        self.style.configure('Modern.TEntry',
            foreground=self.COLORS['text_primary'],
            fieldbackground=self.COLORS['input_bg'],
            borderwidth=1,
            relief="flat",
            padding=10
        )
        self.style.map('Modern.TEntry',
            fieldbackground=[('active', self.COLORS['input_bg']), ('!disabled', self.COLORS['input_bg'])],
            bordercolor=[('focus', self.COLORS['primary_accent']), ('!focus', self.COLORS['border'])],
            lightcolor=[('focus', self.COLORS['primary_accent']), ('!focus', self.COLORS['border'])],
            darkcolor=[('focus', self.COLORS['primary_accent']), ('!focus', self.COLORS['border'])]
        )

    def create_footer(self):
        """Create an elegant footer with branding information"""
        # Footer frame
        self.footer_frame = tk.Frame(self.main_frame, bg=self.COLORS['background'], pady=20)
        self.footer_frame.pack(fill="x")
        
        # BISH TECHNOLOGIES (2025)
        self.tech_label = tk.Label(
            self.footer_frame,
            text="BISH TECHNOLOGIES (2025)",
            font=("Helvetica", 9, "bold"),
            fg=self.COLORS['primary_accent'],
            bg=self.COLORS['background']
        )
        self.tech_label.pack(pady=(2, 0))
        
        # Email contact
        self.email_label = tk.Label(
            self.footer_frame,
            text="<EMAIL>",
            font=("Helvetica", 8),
            fg=self.COLORS['text_secondary'],
            bg=self.COLORS['background'],
            cursor="hand2"
        )
        self.email_label.pack(pady=(5, 0))
        
        # Make email clickable (this will open default mail client)
        self.email_label.bind("<Button-1>", lambda e: os.system(f'start mailto:<EMAIL>'))

    def validate_username(self, username):
        """Validate username: max 20 chars, alphanumeric and underscore only"""
        if len(username) > 20:
            return False, "Username must be 20 characters or less."
        if not re.match(r'^[a-zA-Z0-9_]+$', username):
            return False, "Username can only contain letters, numbers, and underscores."
        return True, ""

    def validate_password(self, password):
        """Validate password: 8-15 chars"""
        if not (8 <= len(password) <= 15):
            return False, "Password must be between 8 and 15 characters."
        return True, ""

    def validate_email(self, email):
        """Validate email: non-empty and basic format check"""
        if not email:
            return False, "Email cannot be empty."
        if not re.match(r'^[^@]+@[^@]+\.[^@]+$', email):
            return False, "Invalid email format."
        return True, ""

    def sanitize_input(self, input_str):
        """Basic sanitization to prevent SQL injection"""
        # Remove common SQL injection patterns
        dangerous_patterns = [
            r'\b(ALTER|CREATE|DELETE|DROP|EXEC(UTE)?|INSERT|SELECT|UPDATE)\b',
            r'--',
            r';',
            r'/\*.*?\*/'
        ]
        sanitized = input_str
        for pattern in dangerous_patterns:
            sanitized = re.sub(pattern, '', sanitized, flags=re.IGNORECASE)
        return sanitized

    def register(self):
        username = self.sanitize_input(self.username_entry.get().strip())
        email = self.sanitize_input(self.email_entry.get().strip())
        password = self.sanitize_input(self.password_entry.get().strip())
        confirm_password = self.sanitize_input(self.confirm_password_entry.get().strip())

        # Validate inputs
        is_valid_username, username_error = self.validate_username(username)
        is_valid_email, email_error = self.validate_email(email)
        is_valid_password, password_error = self.validate_password(password)

        if not is_valid_username:
            messagebox.showerror("Invalid Input", username_error)
            return
        if not is_valid_email:
            messagebox.showerror("Invalid Input", email_error)
            return
        if not is_valid_password:
            messagebox.showerror("Invalid Input", password_error)
            return
        if password != confirm_password:
            messagebox.showerror("Invalid Input", "Passwords do not match")
            return

        try:
            db_path = os.path.join(os.getenv('APPDATA'), 'CRM_System', 'crm_database.db')
            with get_db_connection() as conn:
                c = conn.cursor()
                c.execute("SELECT id FROM users WHERE username = ?", (username,))
                if c.fetchone():
                    messagebox.showerror("Error", "Username already exists")
                    return

                hashed_password = bcrypt.hashpw(password.encode('utf-8'), bcrypt.gensalt())
                c.execute(
                    "INSERT INTO users (username, email, password) VALUES (?, ?, ?)",
                    (username, email, hashed_password.decode('utf-8'))
                )
                conn.commit()
                messagebox.showinfo("Success", "Registration successful! Please login.")
                self.nav_commands['show_login']()
        except Exception as e:
            messagebox.showerror("Error", f"Database error: {str(e)}")
