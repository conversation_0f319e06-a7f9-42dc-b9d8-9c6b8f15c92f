#!/usr/bin/env python3
"""
Check for existing license files that might be causing issues
"""
import os
import json
from datetime import datetime

def check_license_files():
    """Check for existing license and trial files"""
    print("🔍 Checking for existing license files...")
    
    # Paths to check
    app_data_dir = os.path.join(os.getenv('APPDATA', ''), 'CRM_System')
    current_dir = os.path.dirname(os.path.abspath(__file__))
    
    files_to_check = [
        # In APPDATA
        (os.path.join(app_data_dir, 'user_license.enc'), "User License (Encrypted)"),
        (os.path.join(app_data_dir, 'license_cache.dat'), "License Cache"),
        (os.path.join(app_data_dir, 'trial.key'), "Trial File"),
        (os.path.join(app_data_dir, 'trial_history.enc'), "Trial History"),
        (os.path.join(app_data_dir, 'attempts.log'), "Attempts Log"),
        (os.path.join(app_data_dir, 'key.key'), "Encryption Key"),
        (os.path.join(app_data_dir, 'registry_backup.enc'), "Registry Backup"),
        
        # In current directory
        (os.path.join(current_dir, 'license.key'), "License Key File"),
        
        # Log files
        (os.path.join(app_data_dir, 'crm_license.log'), "License Log"),
        (os.path.join(app_data_dir, 'crm_test.log'), "Test Log"),
        (os.path.join(app_data_dir, 'app_crash_diagnosis.log'), "Crash Diagnosis Log"),
    ]
    
    print(f"📁 Checking directory: {app_data_dir}")
    print(f"📁 Checking directory: {current_dir}")
    print("-" * 60)
    
    found_files = []
    
    for file_path, description in files_to_check:
        if os.path.exists(file_path):
            try:
                stat = os.stat(file_path)
                size = stat.st_size
                modified = datetime.fromtimestamp(stat.st_mtime).strftime('%Y-%m-%d %H:%M:%S')
                print(f"✅ {description}")
                print(f"   Path: {file_path}")
                print(f"   Size: {size} bytes")
                print(f"   Modified: {modified}")
                
                # Try to read trial file if it exists
                if file_path.endswith('trial.key'):
                    try:
                        with open(file_path, 'r') as f:
                            trial_data = json.load(f)
                        install_date = datetime.strptime(trial_data['install_date'], "%Y-%m-%d")
                        trial_days = trial_data['trial_days']
                        days_used = (datetime.now() - install_date).days
                        days_remaining = trial_days - days_used
                        
                        print(f"   Trial Info:")
                        print(f"     Install Date: {trial_data['install_date']}")
                        print(f"     Trial Days: {trial_days}")
                        print(f"     Days Used: {days_used}")
                        print(f"     Days Remaining: {days_remaining}")
                        print(f"     Status: {'ACTIVE' if days_remaining > 0 else 'EXPIRED'}")
                    except Exception as e:
                        print(f"   Error reading trial file: {e}")
                
                print()
                found_files.append((file_path, description))
            except Exception as e:
                print(f"❌ Error checking {description}: {e}")
        else:
            print(f"❌ {description}: Not found")
    
    print("-" * 60)
    print(f"📊 Summary: Found {len(found_files)} license-related files")
    
    if found_files:
        print("\n🧹 To reset license state, you can delete these files:")
        for file_path, description in found_files:
            print(f"   {file_path}")
        
        print("\n⚠️  WARNING: Deleting these files will reset your license state!")
        print("   You may need to re-enter your license key or restart trial.")
    
    return found_files

def clean_license_files():
    """Clean all license files (use with caution)"""
    print("\n🧹 CLEANING LICENSE FILES...")
    print("⚠️  This will reset your license state!")
    
    response = input("Are you sure you want to delete all license files? (yes/no): ")
    if response.lower() != 'yes':
        print("❌ Cancelled.")
        return
    
    files = check_license_files()
    deleted_count = 0
    
    for file_path, description in files:
        try:
            os.remove(file_path)
            print(f"✅ Deleted: {description}")
            deleted_count += 1
        except Exception as e:
            print(f"❌ Failed to delete {description}: {e}")
    
    print(f"\n📊 Deleted {deleted_count} files")
    print("🔄 License state has been reset. You can now try starting the application again.")

def main():
    print("🔍 CRM System License File Checker")
    print("=" * 60)
    
    files = check_license_files()
    
    if files:
        print("\n🛠️  Options:")
        print("1. Just check files (done)")
        print("2. Clean all license files (reset license state)")
        
        choice = input("\nEnter your choice (1 or 2): ")
        if choice == '2':
            clean_license_files()
    
    print("\n✅ Done!")

if __name__ == "__main__":
    try:
        main()
    except Exception as e:
        print(f"Error: {e}")
    
    input("\nPress Enter to exit...")
