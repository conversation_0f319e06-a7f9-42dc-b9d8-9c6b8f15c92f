import tkinter as tk
from tkinter import ttk, messagebox
import sqlite3
from database_utils import get_db_connection
import os
import smtplib
import secrets
import string
from email.mime.text import MIMEText
from datetime import datetime, timedelta
import re
import bcrypt
import logging

# Configure logging to file
log_dir = os.path.join(os.getenv('APPDATA'), 'CRM_System', 'logs')
os.makedirs(log_dir, exist_ok=True)
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(os.path.join(log_dir, 'crm_security.log')),
        logging.StreamHandler()
    ]
)

class ForgotPassword(tk.Frame):
    def __init__(self, parent, nav_commands):
        super().__init__(parent)
        self.nav_commands = nav_commands
        self.parent = parent

        # Color scheme matching the login page
        self.COLORS = {
            'background': '#F8F9FA',
            'card_bg': '#FFFFFF',
            'primary_accent': '#4A6FA5',
            'secondary_accent': '#6C8FC7',
            'text_primary': '#2D3748',
            'text_secondary': '#718096',
            'button_start': '#4A6FA5',
            'button_end': '#3A5A8C',
            'transparent': self.parent.cget('bg'),
            'warning': '#E53E3E',
            'input_bg': '#EDF2F7',
            'border': '#E2E8F0'
        }

        # For password reset
        self.reset_token = None
        self.current_user_id = None
        self.current_new_password = None
        self.locked_accounts = {}
        self.submission_timestamps = {}  # For rate limiting
        self.session_start_time = None

        self.db_path = os.path.join(os.getenv('APPDATA'), 'CRM_System', 'crm_database.db')
        
        self.configure(bg=self.COLORS['card_bg'])
        self.pack(fill="both", expand=True)

        # Create a canvas for the solid background
        self.canvas = tk.Canvas(self, bg=self.COLORS['card_bg'], highlightthickness=0)
        self.canvas.pack(fill="both", expand=True)

        # Main container frame
        self.main_frame = tk.Frame(self.canvas, bg=self.COLORS['card_bg'])
        self.main_frame.place(relx=0.5, rely=0.5, anchor="center")

        # Create widgets
        self.create_widgets()
        
        # Add the footer
        self.create_footer()

        self.after(1000, self.check_session_timeout)

    def create_widgets(self):
        # Reset request card
        self.reset_request_card = tk.Frame(
            self.main_frame,
            bg=self.COLORS['card_bg'],
            padx=40,
            pady=40,
            relief="flat",
            borderwidth=0
        )
        self.reset_request_card.pack(pady=20)

        # Title section
        self.title_frame = tk.Frame(self.reset_request_card, bg=self.COLORS['card_bg'])
        self.title_frame.pack(pady=(0, 30))
        
        self.title_label = tk.Label(
            self.title_frame,
            text="SANI BROADBAND",
            font=("Helvetica", 24, "bold"),
            fg=self.COLORS['primary_accent'],
            bg=self.COLORS['card_bg']
        )
        self.title_label.pack()
        
        self.subtitle_label = tk.Label(
            self.title_frame,
            text="Reset your password",
            font=("Helvetica", 12),
            fg=self.COLORS['text_secondary'],
            bg=self.COLORS['card_bg']
        )
        self.subtitle_label.pack(pady=(5, 0))

        # Input fields frame
        self.input_frame = tk.Frame(self.reset_request_card, bg=self.COLORS['card_bg'])
        self.input_frame.pack(fill="x")

        # Username field
        self.username_label = tk.Label(
            self.input_frame,
            text="Username",
            font=("Helvetica", 10),
            fg=self.COLORS['text_primary'],
            bg=self.COLORS['card_bg'],
            anchor='w'
        )
        self.username_label.pack(fill="x", pady=(10, 5))
        
        self.reset_username_entry = ttk.Entry(
            self.input_frame,
            font=("Helvetica", 12),
            width=30,
            style='Modern.TEntry'
        )
        self.reset_username_entry.pack(fill="x", ipady=8)

        # Email field
        self.email_label = tk.Label(
            self.input_frame,
            text="Email",
            font=("Helvetica", 10),
            fg=self.COLORS['text_primary'],
            bg=self.COLORS['card_bg'],
            anchor='w'
        )
        self.email_label.pack(fill="x", pady=(15, 5))
        
        self.reset_email_entry = ttk.Entry(
            self.input_frame,
            font=("Helvetica", 12),
            width=30,
            style='Modern.TEntry'
        )
        self.reset_email_entry.pack(fill="x", ipady=8)

        # New Password field
        self.new_password_label = tk.Label(
            self.input_frame,
            text="New Password",
            font=("Helvetica", 10),
            fg=self.COLORS['text_primary'],
            bg=self.COLORS['card_bg'],
            anchor='w'
        )
        self.new_password_label.pack(fill="x", pady=(15, 5))
        
        self.new_password_entry = ttk.Entry(
            self.input_frame,
            font=("Helvetica", 12),
            show="•",
            width=30,
            style='Modern.TEntry'
        )
        self.new_password_entry.pack(fill="x", ipady=8)

        # Confirm Password field
        self.confirm_password_label = tk.Label(
            self.input_frame,
            text="Confirm Password",
            font=("Helvetica", 10),
            fg=self.COLORS['text_primary'],
            bg=self.COLORS['card_bg'],
            anchor='w'
        )
        self.confirm_password_label.pack(fill="x", pady=(15, 5))
        
        self.confirm_password_entry = ttk.Entry(
            self.input_frame,
            font=("Helvetica", 12),
            show="•",
            width=30,
            style='Modern.TEntry'
        )
        self.confirm_password_entry.pack(fill="x", ipady=8)

        # Send Token button
        self.send_token_btn = tk.Button(
            self.input_frame,
            text="Send Token",
            font=("Helvetica", 12, "bold"),
            bg=self.COLORS['primary_accent'],
            fg="#FFFFFF",
            activebackground=self.COLORS['secondary_accent'],
            activeforeground="#FFFFFF",
            width=30,
            height=1,
            bd=0,
            relief="flat",
            command=self.send_reset_token
        )
        self.send_token_btn.pack(fill="x", pady=(20, 10), ipady=10)

        # Divider
        self.divider = tk.Frame(
            self.input_frame,
            bg=self.COLORS['border'],
            height=1
        )
        self.divider.pack(fill="x", pady=10)

        # Back to login link
        self.login_frame = tk.Frame(self.input_frame, bg=self.COLORS['card_bg'])
        self.login_frame.pack()
        
        self.login_label = tk.Label(
            self.login_frame,
            text="Remember your password?",
            font=("Helvetica", 10),
            fg=self.COLORS['text_secondary'],
            bg=self.COLORS['card_bg']
        )
        self.login_label.pack(side="left")
        
        self.login_link = tk.Label(
            self.login_frame,
            text="Login",
            font=("Helvetica", 10, "bold"),
            fg=self.COLORS['primary_accent'],
            bg=self.COLORS['card_bg'],
            cursor="hand2"
        )
        self.login_link.pack(side="left", padx=5)
        self.login_link.bind("<Button-1>", lambda e: self.nav_commands['show_login']())

        # Token verification card (initially hidden)
        self.token_verify_card = tk.Frame(
            self.main_frame,
            bg=self.COLORS['card_bg'],
            padx=40,
            pady=40,
            relief="flat",
            borderwidth=0
        )

        # Title section for token verification
        self.token_title_frame = tk.Frame(self.token_verify_card, bg=self.COLORS['card_bg'])
        self.token_title_frame.pack(pady=(0, 30))
        
        self.token_title_label = tk.Label(
            self.token_title_frame,
            text="SANI BROADBAND",
            font=("Helvetica", 24, "bold"),
            fg=self.COLORS['primary_accent'],
            bg=self.COLORS['card_bg']
        )
        self.token_title_label.pack()
        
        self.token_subtitle_label = tk.Label(
            self.token_title_frame,
            text="Verify your reset token",
            font=("Helvetica", 12),
            fg=self.COLORS['text_secondary'],
            bg=self.COLORS['card_bg']
        )
        self.token_subtitle_label.pack(pady=(5, 0))

        # Token input frame
        self.token_input_frame = tk.Frame(self.token_verify_card, bg=self.COLORS['card_bg'])
        self.token_input_frame.pack(fill="x")

        # Token field
        self.token_label = tk.Label(
            self.token_input_frame,
            text="Reset Token",
            font=("Helvetica", 10),
            fg=self.COLORS['text_primary'],
            bg=self.COLORS['card_bg'],
            anchor='w'
        )
        self.token_label.pack(fill="x", pady=(10, 5))
        
        self.token_entry = ttk.Entry(
            self.token_input_frame,
            font=("Helvetica", 12),
            width=30,
            style='Modern.TEntry'
        )
        self.token_entry.pack(fill="x", ipady=8)

        # Verify Token button
        self.verify_token_btn = tk.Button(
            self.token_input_frame,
            text="Verify Token",
            font=("Helvetica", 12, "bold"),
            bg=self.COLORS['primary_accent'],
            fg="#FFFFFF",
            activebackground=self.COLORS['secondary_accent'],
            activeforeground="#FFFFFF",
            width=30,
            height=1,
            bd=0,
            relief="flat",
            command=self.verify_token
        )
        self.verify_token_btn.pack(fill="x", pady=(20, 10), ipady=10)

        # Divider
        self.token_divider = tk.Frame(
            self.token_input_frame,
            bg=self.COLORS['border'],
            height=1
        )
        self.token_divider.pack(fill="x", pady=10)

        # Back to reset request link
        self.back_frame = tk.Frame(self.token_input_frame, bg=self.COLORS['card_bg'])
        self.back_frame.pack()
        
        self.back_label = tk.Label(
            self.back_frame,
            text="Need to request a new token?",
            font=("Helvetica", 10),
            fg=self.COLORS['text_secondary'],
            bg=self.COLORS['card_bg']
        )
        self.back_label.pack(side="left")
        
        self.back_link = tk.Label(
            self.back_frame,
            text="Back",
            font=("Helvetica", 10, "bold"),
            fg=self.COLORS['primary_accent'],
            bg=self.COLORS['card_bg'],
            cursor="hand2"
        )
        self.back_link.pack(side="left", padx=5)
        self.back_link.bind("<Button-1>", lambda e: self.show_reset_request())

        # Configure styles
        self.style = ttk.Style()
        self.style.configure('Modern.TEntry',
            foreground=self.COLORS['text_primary'],
            fieldbackground=self.COLORS['input_bg'],
            borderwidth=1,
            relief="flat",
            padding=10
        )
        self.style.map('Modern.TEntry',
            fieldbackground=[('active', self.COLORS['input_bg']), ('!disabled', self.COLORS['input_bg'])],
            bordercolor=[('focus', self.COLORS['primary_accent']), ('!focus', self.COLORS['border'])],
            lightcolor=[('focus', self.COLORS['primary_accent']), ('!focus', self.COLORS['border'])],
            darkcolor=[('focus', self.COLORS['primary_accent']), ('!focus', self.COLORS['border'])]
        )

    def create_footer(self):
        """Create an elegant footer with branding information"""
        # Footer frame
        self.footer_frame = tk.Frame(self.main_frame, bg=self.COLORS['card_bg'], pady=20)
        self.footer_frame.pack(fill="x")
        
        # BISH TECHNOLOGIES (2025)
        self.tech_label = tk.Label(
            self.footer_frame,
            text="BISH TECHNOLOGIES (2025)",
            font=("Helvetica", 9, "bold"),
            fg=self.COLORS['primary_accent'],
            bg=self.COLORS['card_bg']
        )
        self.tech_label.pack(pady=(2, 0))
        
        # Email contact
        self.email_label = tk.Label(
            self.footer_frame,
            text="<EMAIL>",
            font=("Helvetica", 8),
            fg=self.COLORS['text_secondary'],
            bg=self.COLORS['card_bg'],
            cursor="hand2"
        )
        self.email_label.pack(pady=(5, 0))
        
        # Make email clickable (this will open default mail client)
        self.email_label.bind("<Button-1>", lambda e: os.system(f'start mailto:<EMAIL>'))

    def show_reset_request(self):
        self.token_verify_card.place_forget()
        self.reset_request_card.place(relx=0.5, rely=0.5, anchor="center")
        self.reset_username_entry.delete(0, tk.END)
        self.reset_email_entry.delete(0, tk.END)
        self.new_password_entry.delete(0, tk.END)
        self.confirm_password_entry.delete(0, tk.END)

    def show_token_verify(self):
        self.reset_request_card.place_forget()
        self.token_verify_card.place(relx=0.5, rely=0.5, anchor="center")
        self.token_entry.delete(0, tk.END)

    def validate_username(self, username):
        if len(username) > 20:
            return False, "Username must be 20 characters or less."
        if not re.match(r'^[a-zA-Z0-9_]+$', username):
            return False, "Username can only contain letters, numbers, and underscores."
        return True, ""

    def validate_password(self, password, strict=True):
        if not (8 <= len(password) <= 15):
            return False, "Password must be between 8 and 15 characters."
        if strict:
            if not re.search(r'[A-Z]', password):
                return False, "Password must contain at least one uppercase letter."
            if not re.search(r'[a-z]', password):
                return False, "Password must contain at least one lowercase letter."
            if not re.search(r'\d', password):
                return False, "Password must contain at least one digit."
            if not re.search(r'[!@#$%^&*(),.?":{}|<>]', password):
                return False, "Password must contain at least one special character."
        return True, ""

    def validate_email(self, email):
        if not email:
            return False, "Email cannot be empty."
        if not re.match(r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$', email):
            return False, "Invalid email format."
        return True, ""

    def sanitize_input(self, input_str, input_type='text'):
        if not input_str:
            return input_str

        # Log suspicious patterns
        suspicious_patterns = [
            r'\b(ALTER|CREATE|DELETE|DROP|EXEC(UTE)?|INSERT|SELECT|UPDATE|UNION)\b',
            r'--',
            r';',
            r'/\*.*?\*/',
            r'[\'"]\s*OR\s*[\'"]',
            r'[\'"]\s*=\s*[\'"]',
            r'UNION\s+SELECT',
            r'xp_'
        ]
        for pattern in suspicious_patterns:
            if re.search(pattern, input_str, re.IGNORECASE):
                logging.warning(f"Potential SQL injection attempt in {input_type}: {input_str}")

        # Validate input based on type
        sanitized = input_str
        if input_type == 'username':
            if not re.match(r'^[a-zA-Z0-9_]+$', sanitized):
                logging.debug(f"Invalid username input: {sanitized}")
                raise ValueError("Invalid username characters")
        elif input_type == 'email':
            if not re.match(r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$', sanitized):
                logging.debug(f"Invalid email input: {sanitized}")
                raise ValueError("Invalid email characters")
        elif input_type == 'token':
            if not re.match(r'^\d{6}$', sanitized):
                logging.debug(f"Invalid token input: {sanitized}")
                raise ValueError("Invalid token characters")
        else:  # Password or other text
            dangerous_patterns = [
                r'\b(ALTER|CREATE|DELETE|DROP|EXEC(UTE)?|INSERT|SELECT|UPDATE|UNION)\b',
                r'--',
                r';',
                r'/\*.*?\*/',
                r'[\'";]'
            ]
            for pattern in dangerous_patterns:
                sanitized = re.sub(pattern, '', sanitized, flags=re.IGNORECASE)

        sanitized = sanitized[:100]
        return sanitized.strip()

    def send_confirmation_email(self, email, token, username):
        try:
            with get_db_connection() as conn:
                c = conn.cursor()
                c.execute("SELECT email, password, smtp_server, smtp_port FROM email_config LIMIT 1")
                config = c.fetchone()
                
                if not config:
                    messagebox.showerror("Error", "Email configuration not set. Please configure email settings.")
                    logging.error("Email configuration missing")
                    return False

                sender_email, sender_password, smtp_server, smtp_port = config

            subject = f"SANI Broadband CRM - Password Reset Token for {username}"
            timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            body = f"""
            Dear {username},

            Your password reset token is: {token}

            This token will expire in 5 minutes.
            Please enter this token in the application to reset your password.

            Request Timestamp: {timestamp}
            If you didn't request this, please ignore this email or contact support.

            Best regards,
            SANI Broadband Team
            """
            msg = MIMEText(body)
            msg['Subject'] = subject
            msg['From'] = sender_email
            msg['To'] = email

            with smtplib.SMTP_SSL(smtp_server, smtp_port) as server:
                server.login(sender_email, sender_password)
                server.sendmail(sender_email, email, msg.as_string())
            logging.info(f"Reset token email sent to {email}")
            return True
        except Exception as e:
            messagebox.showerror("Error", f"Failed to send email: {str(e)}")
            logging.error(f"Email sending failed: {str(e)}")
            return False

    def generate_token(self, length=6):
        return ''.join(secrets.choice(string.digits) for _ in range(length))

    def check_session_timeout(self):
        if self.session_start_time and (datetime.now() - self.session_start_time) > timedelta(minutes=10):
            self.reset_token = None
            self.current_user_id = None
            self.current_new_password = None
            self.session_start_time = None
            logging.info("Password reset session timed out")
            messagebox.showinfo("Session Timeout", "Your session has timed out. Please start the reset process again.")
            self.show_reset_request()
        self.after(1000, self.check_session_timeout)

    def send_reset_token(self):
        logging.debug("send_reset_token called")
        try:
            username = self.sanitize_input(self.reset_username_entry.get().strip().lower(), input_type='username')
            email = self.sanitize_input(self.reset_email_entry.get().strip().lower(), input_type='email')
            new_password = self.sanitize_input(self.new_password_entry.get().strip(), input_type='password')
            confirm_password = self.sanitize_input(self.confirm_password_entry.get().strip(), input_type='password')
            logging.debug(f"Inputs: username={username}, email={email}, password_len={len(new_password)}")

            is_valid_username, username_error = self.validate_username(username)
            is_valid_email, email_error = self.validate_email(email)
            is_valid_password, password_error = self.validate_password(new_password, strict=True)

            if not username:
                messagebox.showerror("Invalid Input", "Username cannot be empty")
                return
            if not email:
                messagebox.showerror("Invalid Input", "Email cannot be empty")
                return
            if not is_valid_username:
                messagebox.showerror("Invalid Input", username_error)
                self.new_password_entry.delete(0, tk.END)
                self.confirm_password_entry.delete(0, tk.END)
                return
            if not is_valid_email:
                messagebox.showerror("Invalid Input", email_error)
                self.new_password_entry.delete(0, tk.END)
                self.confirm_password_entry.delete(0, tk.END)
                return
            if not is_valid_password:
                messagebox.showerror("Invalid Input", password_error)
                self.new_password_entry.delete(0, tk.END)
                self.confirm_password_entry.delete(0, tk.END)
                return
            if new_password != confirm_password:
                messagebox.showerror("Invalid Input", "Passwords do not match")
                self.new_password_entry.delete(0, tk.END)
                self.confirm_password_entry.delete(0, tk.END)
                return

            # Rate limiting
            current_time = datetime.now()
            if username in self.submission_timestamps:
                timestamps = [t for t in self.submission_timestamps[username] if (current_time - t).total_seconds() < 60]
                self.submission_timestamps[username] = timestamps
                if len(timestamps) >= 5:
                    messagebox.showerror("Error", "Too many attempts. Please wait a minute before trying again.")
                    logging.warning(f"Rate limit exceeded for username: {username}")
                    return
            else:
                self.submission_timestamps[username] = []
            self.submission_timestamps[username].append(current_time)
            logging.debug(f"Rate limit check passed for {username}")

            if username in self.locked_accounts:
                lock_time = self.locked_accounts[username]
                if datetime.now() < lock_time:
                    remaining = (lock_time - datetime.now()).seconds // 60
                    messagebox.showerror("Error", f"Account locked. Try again in {remaining} minutes.")
                    return
                else:
                    del self.locked_accounts[username]

            with get_db_connection() as conn:
                c = conn.cursor()
                c.execute("SELECT id, email, password FROM users WHERE username = ?", (username,))
                result = c.fetchone()
                logging.debug(f"User query result: {result}")

                if not result:
                    messagebox.showerror("Error", "Username not found")
                    self.new_password_entry.delete(0, tk.END)
                    self.confirm_password_entry.delete(0, tk.END)
                    return

                user_id, registered_email, current_hashed_password = result
                if registered_email != email:
                    messagebox.showerror("Error", "Email does not match the email used during registration")
                    self.new_password_entry.delete(0, tk.END)
                    self.confirm_password_entry.delete(0, tk.END)
                    return

                if bcrypt.checkpw(new_password.encode('utf-8'), current_hashed_password.encode('utf-8')):
                    messagebox.showerror("Error", "New password cannot be the same as the current password")
                    self.new_password_entry.delete(0, tk.END)
                    self.confirm_password_entry.delete(0, tk.END)
                    return

                # Check password history if table exists
                try:
                    c.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='password_history'")
                    if c.fetchone():
                        c.execute("SELECT hashed_password FROM password_history WHERE user_id = ?", (user_id,))
                        previous_passwords = c.fetchall()
                        for (prev_hashed_password,) in previous_passwords:
                            if bcrypt.checkpw(new_password.encode('utf-8'), prev_hashed_password.encode('utf-8')):
                                messagebox.showerror("Error", "New password cannot be the same as a previously used password")
                                self.new_password_entry.delete(0, tk.END)
                                self.confirm_password_entry.delete(0, tk.END)
                                return
                except Exception as e:
                    logging.warning(f"Password history check failed: {str(e)}")

                c.execute("SELECT COUNT(*) FROM reset_tokens WHERE user_id = ? AND created_at > ?",
                         (user_id, (datetime.now() - timedelta(hours=1)).isoformat()))
                attempt_count = c.fetchone()[0]
                logging.debug(f"Reset attempt count: {attempt_count}")

                if attempt_count >= 3:
                    self.locked_accounts[username] = datetime.now() + timedelta(hours=1)
                    messagebox.showerror("Error", "Too many attempts. Account locked for 60 minutes.")
                    self.new_password_entry.delete(0, tk.END)
                    self.confirm_password_entry.delete(0, tk.END)
                    return

                self.current_user_id = user_id
                self.current_new_password = new_password
                self.reset_token = self.generate_token()
                self.session_start_time = datetime.now()

                created_at = datetime.now().isoformat()
                c.execute("DELETE FROM reset_tokens WHERE user_id = ?", (user_id,))
                c.execute("INSERT INTO reset_tokens (user_id, token, created_at, attempts) VALUES (?, ?, ?, ?)",
                         (user_id, self.reset_token, created_at, 0))
                conn.commit()
                logging.debug(f"Token generated and stored: {self.reset_token}")

                if self.send_confirmation_email(email, self.reset_token, username):
                    messagebox.showinfo("Success", "Reset token sent to your email!")
                    self.show_token_verify()
                else:
                    self.reset_token = None
                    c.execute("DELETE FROM reset_tokens WHERE user_id = ?", (user_id,))
                    conn.commit()
                    self.new_password_entry.delete(0, tk.END)
                    self.confirm_password_entry.delete(0, tk.END)
        except ValueError as ve:
            messagebox.showerror("Invalid Input", str(ve))
            self.new_password_entry.delete(0, tk.END)
            self.confirm_password_entry.delete(0, tk.END)
        except Exception as e:
            messagebox.showerror("Error", f"Failed to process reset request: {str(e)}")
            logging.error(f"Error in send_reset_token: {str(e)}")
            self.new_password_entry.delete(0, tk.END)
            self.confirm_password_entry.delete(0, tk.END)

    def verify_token(self):
        try:
            entered_token = self.sanitize_input(self.token_entry.get().strip(), input_type='token')
            logging.debug(f"Verifying token: {entered_token}")

            if not entered_token:
                messagebox.showerror("Error", "Please enter the reset token")
                return

            with get_db_connection() as conn:
                c = conn.cursor()
                c.execute("SELECT id, token, created_at, attempts FROM reset_tokens WHERE user_id = ? ORDER BY created_at DESC LIMIT 1",
                         (self.current_user_id,))
                result = c.fetchone()
                logging.debug(f"Token query result: {result}")

                if not result:
                    messagebox.showerror("Error", "No valid reset token found")
                    self.show_reset_request()
                    return

                token_id, stored_token, created_at, attempts = result
                created_time = datetime.fromisoformat(created_at)
                expiry_time = created_time + timedelta(minutes=5)

                if datetime.now() > expiry_time:
                    messagebox.showerror("Error", "Reset token has expired")
                    c.execute("DELETE FROM reset_tokens WHERE user_id = ?", (self.current_user_id,))
                    conn.commit()
                    self.show_reset_request()
                    return

                if attempts >= 3:
                    self.locked_accounts[self.current_user_id] = datetime.now() + timedelta(hours=1)
                    messagebox.showerror("Error", "Too many invalid attempts. Account locked for 60 minutes.")
                    c.execute("DELETE FROM reset_tokens WHERE user_id = ?", (self.current_user_id,))
                    conn.commit()
                    self.nav_commands['show_login']()
                    return

                if entered_token != stored_token:
                    c.execute("UPDATE reset_tokens SET attempts = attempts + 1 WHERE id = ?", (token_id,))
                    conn.commit()
                    messagebox.showerror("Error", "Invalid reset token")
                    return

                hashed_password = bcrypt.hashpw(self.current_new_password.encode('utf-8'), bcrypt.gensalt())
                c.execute("UPDATE users SET password = ? WHERE id = ?", 
                         (hashed_password.decode('utf-8'), self.current_user_id))
                
                # Store in password history if table exists
                try:
                    c.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='password_history'")
                    if c.fetchone():
                        c.execute("INSERT INTO password_history (user_id, hashed_password, created_at) VALUES (?, ?, ?)",
                                 (self.current_user_id, hashed_password.decode('utf-8'), datetime.now().isoformat()))
                        # Keep only last 5 passwords
                        c.execute("""
                            DELETE FROM password_history
                            WHERE user_id = ? AND id NOT IN (
                                SELECT id FROM password_history WHERE user_id = ? 
                                ORDER BY created_at DESC LIMIT 5
                            )""", (self.current_user_id, self.current_user_id))
                except Exception as e:
                    logging.warning(f"Failed to store password history: {str(e)}")

                c.execute("DELETE FROM reset_tokens WHERE user_id = ?", (self.current_user_id,))
                conn.commit()

                messagebox.showinfo("Success", "Password reset successful! Please login.")
                self.reset_token = None
                self.current_user_id = None
                self.current_new_password = None
                self.session_start_time = None
                self.nav_commands['show_login']()

        except ValueError as ve:
            messagebox.showerror("Invalid Input", str(ve))
        except Exception as e:
            messagebox.showerror("Error", f"Database error: {str(e)}")
            logging.error(f"Error in verify_token: {str(e)}")
