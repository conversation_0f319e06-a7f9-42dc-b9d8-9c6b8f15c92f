#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
License Key Update Manager
This script helps manage license key updates for existing installations
"""

import os
import json
import shutil
from datetime import datetime, timedelta
from license_manager import LicenseManager

class LicenseUpdateManager:
    def __init__(self):
        self.app_data_dir = os.path.join(os.getenv('APPDATA', ''), 'CRM_System')
        self.backup_dir = os.path.join(self.app_data_dir, 'backups')
        self.update_log_path = os.path.join(self.app_data_dir, 'update_history.json')
        
        # Ensure directories exist
        os.makedirs(self.backup_dir, exist_ok=True)
    
    def backup_current_license_data(self):
        """Backup current license data before update"""
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_path = os.path.join(self.backup_dir, f'license_backup_{timestamp}')
            os.makedirs(backup_path, exist_ok=True)
            
            # Files to backup
            files_to_backup = [
                'user_license.enc',
                'license_cache.dat',
                'client_tracking.enc',
                'trial_history.enc',
                'registry_backup.enc'
            ]
            
            backed_up_files = []
            for file_name in files_to_backup:
                file_path = os.path.join(self.app_data_dir, file_name)
                if os.path.exists(file_path):
                    backup_file = os.path.join(backup_path, file_name)
                    shutil.copy2(file_path, backup_file)
                    backed_up_files.append(file_name)
            
            # Log the backup
            self._log_update_action("backup", {
                "timestamp": timestamp,
                "backup_path": backup_path,
                "files_backed_up": backed_up_files
            })
            
            print(f"✓ Backup created: {backup_path}")
            print(f"  Files backed up: {', '.join(backed_up_files)}")
            return backup_path
            
        except Exception as e:
            print(f"✗ Backup failed: {str(e)}")
            return None
    
    def check_for_updates(self):
        """Check if there are any pending license key updates"""
        try:
            # Check if there's an update file
            update_file = os.path.join(self.app_data_dir, 'license_update.json')
            if os.path.exists(update_file):
                with open(update_file, 'r') as f:
                    update_data = json.load(f)
                
                print("📦 License update found:")
                print(f"  Version: {update_data.get('version', 'Unknown')}")
                print(f"  Date: {update_data.get('date', 'Unknown')}")
                print(f"  New keys: {len(update_data.get('new_keys', []))}")
                print(f"  Description: {update_data.get('description', 'No description')}")
                return update_data
            
            return None
            
        except Exception as e:
            print(f"✗ Error checking for updates: {str(e)}")
            return None
    
    def apply_license_update(self, update_data):
        """Apply license key updates"""
        try:
            # Backup current data first
            backup_path = self.backup_current_license_data()
            if not backup_path:
                return False
            
            # Apply new keys to license manager
            lm = LicenseManager()
            
            # Add new keys to the default_keys dictionary
            new_keys_added = []
            for key_info in update_data.get('new_keys', []):
                key = key_info['key']
                lm.default_keys[key] = {
                    'name': key_info['name'],
                    'valid_until': key_info['valid_until'],
                    'max_activations': key_info.get('max_activations', 1000),
                    'current_activations': 0,
                    'description': key_info.get('description', '')
                }
                new_keys_added.append(key)
            
            # Save updated license manager state
            self._save_updated_license_manager(lm)
            
            # Log the update
            self._log_update_action("update_applied", {
                "update_version": update_data.get('version'),
                "new_keys_added": new_keys_added,
                "backup_path": backup_path
            })
            
            # Remove the update file
            os.remove(os.path.join(self.app_data_dir, 'license_update.json'))
            
            print(f"✓ License update applied successfully")
            print(f"  New keys added: {', '.join(new_keys_added)}")
            print(f"  Backup available at: {backup_path}")
            return True
            
        except Exception as e:
            print(f"✗ Update failed: {str(e)}")
            return False
    
    def _save_updated_license_manager(self, lm):
        """Save the updated license manager state"""
        try:
            # This would typically involve updating the license_manager.py file
            # For now, we'll just save the current state
            state_file = os.path.join(self.app_data_dir, 'license_manager_state.json')
            state = {
                'default_keys': lm.default_keys,
                'last_update': datetime.now().isoformat(),
                'version': '1.0'
            }
            
            with open(state_file, 'w') as f:
                json.dump(state, f, indent=2)
                
        except Exception as e:
            print(f"✗ Error saving license manager state: {str(e)}")
    
    def _log_update_action(self, action, details):
        """Log update actions"""
        try:
            log_data = {
                'timestamp': datetime.now().isoformat(),
                'action': action,
                'details': details
            }
            
            # Load existing log
            if os.path.exists(self.update_log_path):
                with open(self.update_log_path, 'r') as f:
                    log = json.load(f)
            else:
                log = {'updates': []}
            
            log['updates'].append(log_data)
            
            # Keep only last 50 entries
            if len(log['updates']) > 50:
                log['updates'] = log['updates'][-50:]
            
            with open(self.update_log_path, 'w') as f:
                json.dump(log, f, indent=2)
                
        except Exception as e:
            print(f"✗ Error logging update action: {str(e)}")
    
    def create_update_package(self, new_keys, version, description=""):
        """Create an update package for distribution"""
        try:
            update_data = {
                'version': version,
                'date': datetime.now().isoformat(),
                'description': description,
                'new_keys': new_keys,
                'requires_restart': True
            }
            
            update_file = os.path.join(self.app_data_dir, 'license_update.json')
            with open(update_file, 'w') as f:
                json.dump(update_data, f, indent=2)
            
            print(f"✓ Update package created: {update_file}")
            print(f"  Version: {version}")
            print(f"  New keys: {len(new_keys)}")
            return update_file
            
        except Exception as e:
            print(f"✗ Error creating update package: {str(e)}")
            return None
    
    def get_update_history(self):
        """Get the update history"""
        try:
            if os.path.exists(self.update_log_path):
                with open(self.update_log_path, 'r') as f:
                    log = json.load(f)
                return log.get('updates', [])
            return []
        except Exception as e:
            print(f"✗ Error reading update history: {str(e)}")
            return []

def main():
    """Main function to demonstrate license update process"""
    print("=" * 60)
    print("LICENSE UPDATE MANAGER")
    print("=" * 60)
    
    manager = LicenseUpdateManager()
    
    # Check for existing updates
    print("\n1. Checking for updates...")
    update_data = manager.check_for_updates()
    
    if update_data:
        print("\n2. Applying update...")
        success = manager.apply_license_update(update_data)
        if success:
            print("✓ Update completed successfully!")
        else:
            print("✗ Update failed!")
    else:
        print("✓ No updates found")
    
    # Show update history
    print("\n3. Update History:")
    history = manager.get_update_history()
    if history:
        for update in history[-5:]:  # Show last 5 updates
            print(f"  {update['timestamp']}: {update['action']}")
    else:
        print("  No update history found")
    
    print("\n" + "=" * 60)

if __name__ == "__main__":
    main() 