@echo off
set PYTHON="C:\Program Files\Python313\python.exe"
set PROJECT="D:\CRM_System"
set ICON="%PROJECT%\assets\app_icon.ico"

echo Cleaning previous builds...
cd /d %PROJECT%
rmdir /s /q build 2>nul
rmdir /s /q dist 2>nul
del CRM_System.spec 2>nul

echo Verifying icon exists...
if not exist %ICON% (
    echo Error: Icon file not found at %ICON%
    pause
    exit /b 1
)

echo Building application...
%PYTHON% -m PyInstaller ^
  --onefile ^
  --windowed ^
  --name CRM_System ^
  --icon=%ICON% ^
  --add-data "%PROJECT%\assets;assets" ^
  --add-data "%PROJECT%\views;views" ^
  --hidden-import=PIL ^
  --hidden-import=PIL.Image ^
  --hidden-import=PIL.ImageTk ^
  --clean ^
  main.py

echo Build complete. Check %PROJECT%\dist\CRM_System.exe
pause