#!/usr/bin/env python3
"""
Simple verification script to check if the payment fixes are working
"""

import sqlite3
import os

DB_PATH = "crm_system.db"

def check_database():
    """Check database structure and data"""
    if not os.path.exists(DB_PATH):
        print("Database file not found!")
        return
    
    conn = sqlite3.connect(DB_PATH)
    c = conn.cursor()
    
    try:
        # Check customers
        c.execute("SELECT COUNT(*) FROM customers")
        customer_count = c.fetchone()[0]
        print(f"Customers in database: {customer_count}")
        
        # Check billing
        c.execute("SELECT COUNT(*) FROM billing")
        bill_count = c.fetchone()[0]
        print(f"Bills in database: {bill_count}")
        
        # Check invoice numbers
        c.execute("SELECT COUNT(*) FROM billing WHERE invoice_number IS NOT NULL AND invoice_number != ''")
        bills_with_invoices = c.fetchone()[0]
        print(f"Bills with invoice numbers: {bills_with_invoices}")
        
        # Check payment history structure
        c.execute("PRAGMA table_info(payment_history)")
        columns = [col[1] for col in c.fetchall()]
        required_cols = ['credit_amount', 'outstanding_amount', 'remaining_outstanding', 'applied_credit']
        has_all_cols = all(col in columns for col in required_cols)
        print(f"Payment history has all required columns: {has_all_cols}")
        
        # Check payment history records
        c.execute("SELECT COUNT(*) FROM payment_history")
        payment_count = c.fetchone()[0]
        print(f"Payment history records: {payment_count}")
        
        # Sample data
        if bill_count > 0:
            c.execute("SELECT id, invoice_number, outstanding_amount, credit_amount FROM billing LIMIT 3")
            bills = c.fetchall()
            print("\nSample billing data:")
            for bill in bills:
                print(f"  Bill {bill[0]}: Invoice={bill[1]}, Outstanding={bill[2]}, Credit={bill[3]}")
        
        if payment_count > 0:
            c.execute("SELECT invoice_number, amount_paid, credit_amount, outstanding_amount FROM payment_history LIMIT 3")
            payments = c.fetchall()
            print("\nSample payment history:")
            for payment in payments:
                print(f"  Invoice={payment[0]}, Paid={payment[1]}, Credit={payment[2]}, Outstanding={payment[3]}")
        
    except Exception as e:
        print(f"Error: {e}")
    finally:
        conn.close()

if __name__ == "__main__":
    print("=== Verifying Payment Fixes ===")
    check_database()
