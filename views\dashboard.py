import tkinter as tk
from tkinter import ttk, messagebox
from PIL import Image, ImageTk
import os
import sys
from datetime import datetime
import sqlite3
from database_utils import get_db_connection
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
from matplotlib.figure import Figure
import matplotlib.dates as mdates
import numpy as np
import psutil
import time
import logging
from resources import resource_path
from .email_config import EmailConfig

# Set up logging to help diagnose issues
logging.basicConfig(level=logging.DEBUG, format='%(asctime)s - %(levelname)s - %(message)s')

class Tooltip:
    def __init__(self, widget, text):
        self.widget = widget
        self.text = text
        self.tooltip_window = None
        self.widget.bind("<Enter>", self.show_tooltip)
        self.widget.bind("<Leave>", self.hide_tooltip)

    def show_tooltip(self, event=None):
        x, y, _, _ = self.widget.bbox("insert")
        x += self.widget.winfo_rootx() + 25
        y += self.widget.winfo_rooty() + 25

        self.tooltip_window = tk.Toplevel(self.widget)
        self.tooltip_window.wm_overrideredirect(True)
        self.tooltip_window.wm_geometry(f"+{x}+{y}")

        label = tk.Label(
            self.tooltip_window,
            text=self.text,
            background="#4A6FA5",
            relief="solid",
            borderwidth=1,
            font=("Helvetica", 10),
            fg="#FFFFFF"
        )
        label.pack()

    def hide_tooltip(self, event=None):
        if self.tooltip_window:
            self.tooltip_window.destroy()
            self.tooltip_window = None

class Dashboard(tk.Frame):
    def __init__(self, parent, nav_commands):
        super().__init__(parent)
        self.nav_commands = nav_commands
        self.parent = parent
        self.image_references = []  # Keep track of all images

        # Initialize network monitoring baseline
        psutil.net_io_counters()  # This will establish the baseline

        # Color Scheme matching register.py
        self.COLORS = {
            'background': '#F8F9FA',
            'card_bg': '#FFFFFF',
            'primary_accent': '#4A6FA5',
            'secondary_accent': '#6C8FC7',
            'text_primary': '#2D3748',
            'text_secondary': '#718096',
            'button_start': '#4A6FA5',
            'button_end': '#3A5A8C',
            'border': '#E2E8F0',
            'warning': '#E53E3E',
            'input_bg': '#EDF2F7',
            'bar_colors': [
                '#4A6FA5',  # Primary accent
                '#6C8FC7',  # Secondary accent
                '#FFC107',  # Warning yellow
                '#E53E3E',  # Danger red
                '#2196F3',  # Info blue
                '#9E9E9E'   # Gray
            ],
            'pie_color1': '#4A6FA5',  # Primary accent for Paid
            'pie_color2': '#E53E3E',  # Danger red for Unpaid
            'pie_divider': '#FFFFFF',  # White
            'line_chart': '#4A6FA5',  # Primary accent
            'div_background': '#FFFFFF',  # White
            'button_border': '#E2E8F0'  # Muted gray
        }

        # Configure frame
        self.config(bg=self.COLORS['background'])
        self.pack(fill="both", expand=True)

        # Initialize images
        self.icons = {}
        self.icons_small = {}
        self.icons_large = {}
        self.is_animating = {}

        try:
            self._load_images()
            self._create_ui()
            # Start periodic updates
            self._update_monthly_revenue_comparison_chart()
            self._update_product_sales_chart()
            self._update_monthly_revenue_by_region_chart()
            self._refresh_stock_chart()  # Start stock chart updates
        except Exception as e:
            logging.error(f"Initialization error in Dashboard: {str(e)}")
            messagebox.showerror("Initialization Error", f"Could not load dashboard: {str(e)}")
            self._create_fallback_ui()

    def _load_images(self):
        """Load navigation button images and convert them to white using resource_path"""
        image_files = {
            'customers': 'assets/dashboard/customers.png',
            'billing': 'assets/dashboard/billing.png',
            'packages': 'assets/dashboard/packages.png',
            'products': 'assets/dashboard/product.png',
            'regions': 'assets/dashboard/regions.png',
            'stock': 'assets/dashboard/stock.png',
            'backup': 'assets/dashboard/backup.png',
            'logout': 'assets/dashboard/dashboard_logout.png',
            'dashboard': 'assets/dashboard/dashboard.png',
            'settings': 'assets/dashboard/settings.png'
        }

        def load_and_process_image(relative_path):
            try:
                full_path = resource_path(relative_path)
                if os.path.exists(full_path):
                    img = Image.open(full_path).convert("RGBA")
                    data = img.getdata()
                    new_data = []
                    for item in data:
                        if item[3] > 0:  # If pixel is not transparent
                            new_data.append((255, 255, 255, item[3]))  # Set to white
                        else:
                            new_data.append(item)
                    img.putdata(new_data)
                    return img
                else:
                    logging.warning(f"Icon not found: {full_path}")
                    return Image.new('RGBA', (50, 50), (255, 255, 255, 0))
            except Exception as e:
                logging.error(f"Error loading image {relative_path}: {str(e)}")
                return Image.new('RGBA', (50, 50), (255, 255, 255, 0))

        for key, relative_path in image_files.items():
            try:
                img = load_and_process_image(relative_path)
                
                # For navigation icons, store multiple sizes
                if key not in ['logout', 'dashboard']:
                    photo = ImageTk.PhotoImage(img.resize((50, 50), Image.LANCZOS))
                    self.icons[key] = photo
                    self.image_references.append(photo)
                    
                    photo_small = ImageTk.PhotoImage(img.resize((44, 44), Image.LANCZOS))
                    self.icons_small[key] = photo_small
                    self.image_references.append(photo_small)
                    
                    photo_large = ImageTk.PhotoImage(img.resize((56, 56), Image.LANCZOS))
                    self.icons_large[key] = photo_large
                    self.image_references.append(photo_large)
                else:
                    photo = ImageTk.PhotoImage(img.resize((20, 20), Image.LANCZOS))
                    self.icons[key] = photo
                    self.image_references.append(photo)
                    self.logout_icon_size = (20, 20)

            except Exception as e:
                logging.error(f"Error processing image {relative_path}: {str(e)}")
                img = Image.new('RGBA', (50, 50), (255, 255, 255, 0))
                if key not in ['logout', 'dashboard']:
                    photo = ImageTk.PhotoImage(img.resize((50, 50), Image.LANCZOS))
                    self.icons[key] = photo
                    self.image_references.append(photo)
                    
                    photo_small = ImageTk.PhotoImage(img.resize((44, 44), Image.LANCZOS))
                    self.icons_small[key] = photo_small
                    self.image_references.append(photo_small)
                    
                    photo_large = ImageTk.PhotoImage(img.resize((56, 56), Image.LANCZOS))
                    self.icons_large[key] = photo_large
                    self.image_references.append(photo_large)
                else:
                    photo = ImageTk.PhotoImage(img.resize((20, 20), Image.LANCZOS))
                    self.icons[key] = photo
                    self.image_references.append(photo)
                    self.logout_icon_size = (20, 20)

    def _get_network_speed(self):
        """Calculate current network upload and download speeds in KB/s"""
        try:
            old_io = psutil.net_io_counters()
            old_bytes_sent = old_io.bytes_sent
            old_bytes_recv = old_io.bytes_recv
            time.sleep(1)
            new_io = psutil.net_io_counters()
            new_bytes_sent = new_io.bytes_sent
            new_bytes_recv = new_io.bytes_recv
            # Calculate speeds in KB/s (1 KB = 1024 bytes)
            upload_speed_kbs = (new_bytes_sent - old_bytes_sent) / 1024
            download_speed_kbs = (new_bytes_recv - old_bytes_recv) / 1024
            return f"{upload_speed_kbs:.2f} KB/s", f"{download_speed_kbs:.2f} KB/s"
        except Exception as e:
            logging.error(f"Error calculating network speed: {e}")
            return "0.00 KB/s", "0.00 KB/s"

    def _get_cpu_usage(self):
        """Get current CPU usage percentage"""
        try:
            return f"{psutil.cpu_percent(interval=1):.1f}%"
        except Exception as e:
            logging.error(f"Error getting CPU usage: {e}")
            return "0.0%"

    def _get_disk_usage(self):
        """Get current disk usage percentage for the root directory"""
        try:
            disk = psutil.disk_usage('/')
            return f"{disk.percent:.1f}%"
        except Exception as e:
            logging.error(f"Error getting disk usage: {e}")
            return "0.0%"

    def _update_system_stats(self):
        """Update network speeds, CPU, and disk usage periodically"""
        try:
            # Update network speeds
            upload_speed, download_speed = self._get_network_speed()
            self.upload_label.config(text=f"Upload: {upload_speed}")
            self.download_label.config(text=f"Download: {download_speed}")
            # Update CPU usage
            cpu = self._get_cpu_usage()
            self.cpu_label.config(text=f"CPU: {cpu}")
            # Update disk usage
            disk = self._get_disk_usage()
            self.disk_label.config(text=f"Disk: {disk}")
        except Exception as e:
            logging.error(f"Error updating system stats: {e}")
        # Schedule the next update
        self.after(3000, self._update_system_stats)

    def _get_base_path(self):
        if getattr(sys, 'frozen', False):
            return getattr(sys, '_MEIPASS', os.path.dirname(sys.executable))
        return os.path.dirname(os.path.dirname(os.path.abspath(__file__)))

    def _get_billing_data(self):
        try:
            with get_db_connection() as conn:
                c = conn.cursor()
                current_month = datetime.now().month
                current_year = datetime.now().year
                
                c.execute('''
                    SELECT COUNT(*), SUM(p.price) 
                    FROM billing b
                    JOIN customers c ON b.customer_id = c.id
                    JOIN packages p ON c.package_id = p.id
                    WHERE b.is_paid=1 AND b.month=? AND b.year=?
                ''', (current_month, current_year))
                paid_count, paid_amount = c.fetchone()
                paid_amount = paid_amount or 0
                
                c.execute('''
                    SELECT COUNT(*), SUM(p.price) 
                    FROM billing b
                    JOIN customers c ON b.customer_id = c.id
                    JOIN packages p ON c.package_id = p.id
                    WHERE b.is_paid=0 AND b.month=? AND b.year=?
                ''', (current_month, current_year))
                unpaid_count, unpaid_amount = c.fetchone()
                unpaid_amount = unpaid_amount or 0
                
                c.execute('''
                    SELECT 
                        SUM(CASE WHEN is_paid=1 THEN 1 ELSE 0 END),
                        SUM(CASE WHEN is_paid=0 THEN 1 ELSE 0 END)
                    FROM billing WHERE year=?
                ''', (current_year,))
                annual_paid, annual_unpaid = c.fetchone()
                
                return {
                    'monthly_paid': paid_count,
                    'monthly_paid_amount': paid_amount,
                    'monthly_unpaid': unpaid_count,
                    'monthly_unpaid_amount': unpaid_amount,
                    'annual_paid': annual_paid or 0,
                    'annual_unpaid': annual_unpaid or 0
                }
        except Exception as e:
            logging.error(f"Failed to fetch billing data: {str(e)}")
            messagebox.showerror("Database Error", f"Failed to fetch billing data: {str(e)}")
            return {
                'monthly_paid': 0,
                'monthly_paid_amount': 0,
                'monthly_unpaid': 0,
                'monthly_unpaid_amount': 0,
                'annual_paid': 0,
                'annual_unpaid': 0
            }

    def _get_monthly_revenue_data(self):
        try:
            with get_db_connection() as conn:
                c = conn.cursor()
                current_month = datetime.now().month
                current_year = datetime.now().year
                
                c.execute('''
                    SELECT SUM(p.price)
                    FROM billing b
                    JOIN customers c ON b.customer_id = c.id
                    JOIN packages p ON c.package_id = p.id
                    WHERE b.month=? AND b.year=?
                ''', (current_month, current_year))
                total_revenue = c.fetchone()[0] or 0
                
                c.execute('''
                    SELECT SUM(p.price)
                    FROM billing b
                    JOIN customers c ON b.customer_id = c.id
                    JOIN packages p ON c.package_id = p.id
                    WHERE b.is_paid=1 AND b.month=? AND b.year=?
                ''', (current_month, current_year))
                paid_amount = c.fetchone()[0] or 0
                
                c.execute('''
                    SELECT SUM(p.price)
                    FROM billing b
                    JOIN customers c ON b.customer_id = c.id
                    JOIN packages p ON c.package_id = p.id
                    WHERE b.is_paid=0 AND b.month=? AND b.year=?
                ''', (current_month, current_year))
                unpaid_amount = c.fetchone()[0] or 0
                
                return total_revenue, paid_amount, unpaid_amount
        except Exception as e:
            logging.error(f"Failed to fetch revenue data: {str(e)}")
            messagebox.showerror("Database Error", f"Failed to fetch revenue data: {str(e)}")
            return 0, 0, 0

    def _get_last_six_months_bills(self):
        try:
            with get_db_connection() as conn:
                c = conn.cursor()
                current_month = datetime.now().month
                current_year = datetime.now().year
                
                months = []
                years = []
                for i in range(5, -1, -1):
                    month = current_month - i
                    year = current_year
                    if month <= 0:
                        month += 12
                        year -= 1
                    months.append(month)
                    years.append(year)
                
                paid_bills = []
                unpaid_bills = []
                for month, year in zip(months, years):
                    c.execute('''
                        SELECT SUM(p.price) 
                        FROM billing b
                        JOIN customers c ON b.customer_id = c.id
                        JOIN packages p ON c.package_id = p.id
                        WHERE b.is_paid=1 AND b.month=? AND b.year=?
                    ''', (month, year))
                    paid_amount = c.fetchone()[0] or 0
                    
                    c.execute('''
                        SELECT SUM(p.price) 
                        FROM billing b
                        JOIN customers c ON b.customer_id = c.id
                        JOIN packages p ON c.package_id = p.id
                        WHERE b.is_paid=0 AND b.month=? AND b.year=?
                    ''', (month, year))
                    unpaid_amount = c.fetchone()[0] or 0
                    
                    paid_bills.append((month, year, paid_amount))
                    unpaid_bills.append((month, year, unpaid_amount))
                
                return paid_bills, unpaid_bills
        except Exception as e:
            logging.error(f"Failed to fetch last six months bills: {str(e)}")
            messagebox.showerror("Database Error", f"Failed to fetch last six months bills: {str(e)}")
            return [(1, 2025, 0)] * 6, [(1, 2025, 0)] * 6

    def _get_package_plans(self):
        try:
            with get_db_connection() as conn:
                c = conn.cursor()
                c.execute("SELECT name, price FROM packages")
                return c.fetchall()
        except Exception as e:
            logging.error(f"Failed to fetch packages: {str(e)}")
            messagebox.showerror("Database Error", f"Failed to fetch packages: {str(e)}")
            return []

    def _get_top_package(self):
        try:
            db_path = os.path.join(os.getenv('APPDATA'), 'CRM_System', 'crm_database.db')
            with sqlite3.connect(db_path) as conn:
                c = conn.cursor()
                c.execute('''
                    SELECT p.name, COUNT(b.id) as sales
                    FROM billing b
                    JOIN customers c ON b.customer_id = c.id
                    JOIN packages p ON c.package_id = p.id
                    GROUP BY p.name
                    ORDER BY sales DESC
                    LIMIT 1
                ''')
                return c.fetchone()
        except Exception as e:
            logging.error(f"Failed to fetch top package: {str(e)}")
            messagebox.showerror("Database Error", f"Failed to fetch top package: {str(e)}")
            return None

    def _get_package_sales(self):
        try:
            db_path = os.path.join(os.getenv('APPDATA'), 'CRM_System', 'crm_database.db')
            with sqlite3.connect(db_path) as conn:
                c = conn.cursor()
                c.execute('''
                    SELECT p.name, COUNT(b.id) as sales
                    FROM billing b
                    JOIN customers c ON b.customer_id = c.id
                    JOIN packages p ON c.package_id = p.id
                    GROUP BY p.name
                    ORDER BY sales DESC
                ''')
                return c.fetchall()
        except Exception as e:
            logging.error(f"Failed to fetch package sales: {str(e)}")
            messagebox.showerror("Database Error", f"Failed to fetch package sales: {str(e)}")
            return []

    def _get_product_sales(self):
        try:
            db_path = os.path.join(os.getenv('APPDATA'), 'CRM_System', 'crm_database.db')
            with sqlite3.connect(db_path) as conn:
                c = conn.cursor()
                current_month = datetime.now().month
                current_year = datetime.now().year
                c.execute('''
                    SELECT p.name, COUNT(cp.id) as sales
                    FROM customer_purchases cp
                    JOIN products p ON cp.product_id = p.id
                    JOIN billing b ON cp.billing_id = b.id
                    WHERE b.month = ? AND b.year = ?
                    GROUP BY p.name
                    ORDER BY sales DESC
                ''', (current_month, current_year))
                return c.fetchall()
        except Exception as e:
            logging.error(f"Failed to fetch product sales: {str(e)}")
            messagebox.showerror("Database Error", f"Failed to fetch product sales: {str(e)}")
            return []

    def _get_new_customers_data(self):
        try:
            db_path = os.path.join(os.getenv('APPDATA'), 'CRM_System', 'crm_database.db')
            with sqlite3.connect(db_path) as conn:
                c = conn.cursor()
                current_month = datetime.now().month
                current_year = datetime.now().year
                
                c.execute('''
                    SELECT 
                        date(create_date) as day,
                        COUNT(*) as count
                    FROM customers
                    WHERE strftime('%m', create_date) = ? 
                    AND strftime('%Y', create_date) = ?
                    GROUP BY day
                    ORDER BY day
                ''', (f"{current_month:02d}", str(current_year)))
                
                results = c.fetchall()
                
                dates = []
                counts = []
                today = datetime.now().date()
                
                for day in range(1, today.day + 1):
                    date_str = f"{current_year}-{current_month:02d}-{day:02d}"
                    dates.append(datetime.strptime(date_str, "%Y-%m-%d"))
                    count = 0
                    
                    for record in results:
                        if record[0] == date_str:
                            count = record[1]
                            break
                    
                    counts.append(count)
                
                return dates, counts
                
        except Exception as e:
            logging.error(f"Failed to fetch new customers: {str(e)}")
            messagebox.showerror("Database Error", f"Failed to fetch new customers: {str(e)}")
            current_month = datetime.now().month
            current_year = datetime.now().year
            today = datetime.now().date()
            dates = []
            counts = []
            
            for day in range(1, today.day + 1):
                dates.append(datetime(current_year, current_month, day))
                counts.append(0)
            
            return dates, counts

    def _get_monthly_revenue_by_region(self):
        try:
            db_path = os.path.join(os.getenv('APPDATA'), 'CRM_System', 'crm_database.db')
            with sqlite3.connect(db_path) as conn:
                c = conn.cursor()
                current_month = datetime.now().month
                current_year = datetime.now().year
                
                c.execute('''
                    SELECT c.region, 
                           SUM(CASE WHEN b.is_paid=1 THEN p.price ELSE 0 END) as paid_amount,
                           SUM(CASE WHEN b.is_paid=0 THEN p.price ELSE 0 END) as unpaid_amount
                    FROM billing b
                    JOIN customers c ON b.customer_id = c.id
                    JOIN packages p ON c.package_id = p.id
                    WHERE b.month=? AND b.year=?
                    GROUP BY c.region
                    ORDER BY c.region
                ''', (current_month, current_year))
                
                return c.fetchall()
        except Exception as e:
            logging.error(f"Failed to fetch region revenue data: {str(e)}")
            messagebox.showerror("Database Error", f"Failed to fetch region revenue data: {str(e)}")
            return []

    def _get_stock_data(self):
        """Fetch stock data (product name and remaining stock)"""
        try:
            db_path = os.path.join(os.getenv('APPDATA'), 'CRM_System', 'crm_database.db')
            with sqlite3.connect(db_path) as conn:
                c = conn.cursor()
                c.execute('''
                    SELECT p.name, s.quantity - s.sold as remaining
                    FROM stock s
                    JOIN products p ON s.product_id = p.id
                    ORDER BY p.name
                ''')
                return c.fetchall()
        except Exception as e:
            logging.error(f"Failed to fetch stock data: {str(e)}")
            messagebox.showerror("Database Error", f"Failed to fetch stock data: {str(e)}")
            return []

    def _create_fallback_ui(self):
        self.config(bg=self.COLORS['background'])
        header = tk.Frame(self, bg=self.COLORS['primary_accent'], height=80)
        header.pack(fill="x", pady=(0, 20))
        
        tk.Label(
            header,
            text="SANI BROADBAND(Basic Mode)",
            font=("Helvetica", 16, "bold"),
            fg="#FFFFFF",
            bg=self.COLORS['primary_accent']
        ).pack(side="left", padx=10)

    def _create_header(self):
        header = tk.Frame(self, bg=self.COLORS['primary_accent'], height=65)
        header.pack(fill="x", pady=(0, 20))

        # Company name label
        tk.Label(
            header,
            text="SANI BROADBAND",
            font=("Helvetica", 24, "bold"),
            fg="#FFFFFF",
            bg=self.COLORS['primary_accent']
        ).pack(side="left", padx=20)

        # Right side container for date, stats, and logout button
        right_frame = tk.Frame(header, bg=self.COLORS['primary_accent'])
        right_frame.pack(side="right", padx=20)

        # Logout button (packed first with side="right" to ensure it's on the far right)
        logout_button = tk.Button(
            right_frame,
            image=self.icons['logout'],
            bg='#E53E3E',
            activebackground=self.COLORS['button_end'],
            relief="flat",
            bd=0,
            command=self._logout,
            width=20,
            height=20
        )
        logout_button.pack(side="right", padx=(10, 0))

        # Bind hover effect to match Customers button (turns secondary color on hover)
        logout_button.bind("<Enter>", lambda e: logout_button.config(bg=self.COLORS['secondary_accent']))
        logout_button.bind("<Leave>", lambda e: logout_button.config(bg='#E53E3E'))

        # Add tooltip for the logout button
        Tooltip(logout_button, "Logout")

        # Horizontal frame for system stats (packed after logout button)
        stats_frame = tk.Frame(right_frame, bg=self.COLORS['primary_accent'])
        stats_frame.pack(side="right", padx=(0, 10))

        # Date label
        self.date_label = tk.Label(
            stats_frame,
            text=datetime.now().strftime("%A, %B %d, %Y"),
            font=("Helvetica", 10),
            fg="#E0E0E0",
            bg=self.COLORS['primary_accent']
        )
        self.date_label.pack(side="left", padx=(0, 15))

        # Upload speed label
        self.upload_label = tk.Label(
            stats_frame,
            text="Upload: 0.00 KB/s",
            font=("Helvetica", 10),
            fg="#E0E0E0",
            bg=self.COLORS['primary_accent']
        )
        self.upload_label.pack(side="left", padx=(0, 15))

        # Download speed label
        self.download_label = tk.Label(
            stats_frame,
            text="Download: 0.00 KB/s",
            font=("Helvetica", 10),
            fg="#E0E0E0",
            bg=self.COLORS['primary_accent']
        )
        self.download_label.pack(side="left", padx=(0, 15))

        # CPU usage label
        self.cpu_label = tk.Label(
            stats_frame,
            text="CPU: 0.0%",
            font=("Helvetica", 10),
            fg="#E0E0E0",
            bg=self.COLORS['primary_accent']
        )
        self.cpu_label.pack(side="left", padx=(0, 15))

        # Disk usage label
        self.disk_label = tk.Label(
            stats_frame,
            text="Disk: 0.0%",
            font=("Helvetica", 10),
            fg="#E0E0E0",
            bg=self.COLORS['primary_accent']
        )
        self.disk_label.pack(side="left", padx=(0, 15))

        # Start system stats update
        self._update_system_stats()

    def _logout(self):
        """Handle logout action by redirecting to login page"""
        logging.info("Logging out from Dashboard")
        self.nav_commands['show_login']()

    def _create_navigation_panel(self):
        nav_frame = tk.Frame(self, bg=self.COLORS['background'], padx=20, pady=5)
        nav_frame.pack(fill="x", pady=(0, 15))

        center_frame = tk.Frame(nav_frame, bg=self.COLORS['background'])
        center_frame.pack(expand=True)

        self.nav_buttons = []
        
        for text, nav_key, icon_key in [
            ("Customers", 'customers', 'customers'),
            ("Billing", 'billing', 'billing'),
            ("Packages", 'packages', 'packages'),
            ("Products", 'products', 'products'),
            ("Regions", 'regions', 'regions'),
            ("Stock", 'stock', 'stock'),
            ("Backup", 'backup_restore', 'backup'),
            ("Settings", 'email_config', 'settings')
        ]:
            # Create a canvas to hold the button, reduced size: 80-14=66
            canvas = tk.Canvas(
                center_frame,
                bg=self.COLORS['background'],
                highlightthickness=0,
                width=66,
                height=66,
            )
            canvas.pack(side="left", padx=10)

            # Create a circular button with reduced size
            btn = tk.Button(
                canvas,
                command=lambda k=nav_key: self._navigate_to_page(k),
                bg=self.COLORS['button_start'],
                relief="flat",
                bd=0,
                activebackground=self.COLORS['button_end'],
                borderwidth=0,
                highlightthickness=0,
                width=66,
                height=66
            )
            
            if icon_key in self.icons:
                btn.config(image=self.icons[icon_key])
            
            # Add tooltip to show text on hover
            Tooltip(btn, text)

            # Center the button in the canvas: 66/2=33
            btn_id = canvas.create_window(
                33, 33,
                window=btn, anchor="center")
            
            self.nav_buttons.append((canvas, btn, btn_id))
            self.is_animating[icon_key] = False
            
            # Bind events for hover and click effects
            canvas.bind("<Enter>", lambda e, c=canvas, b=btn, k=icon_key: self._on_enter(e, c, b, k))
            btn.bind("<Button-1>", lambda e, b=btn: self._on_press(e, b))
            btn.bind("<ButtonRelease-1>", lambda e, b=btn: self._on_release(e, b))

    def _navigate_to_page(self, nav_key):
        """Handle navigation to different pages with logging"""
        try:
            logging.info(f"Navigating to page: {nav_key}")
            nav_func = self.nav_commands.get(f'show_{nav_key}', None)
            if nav_func:
                nav_func()
            else:
                logging.error(f"Navigation function for {nav_key} not found")
                messagebox.showerror("Navigation Error", f"Failed to load {nav_key} page")
        except Exception as e:
            logging.error(f"Error during navigation to {nav_key}: {str(e)}")
            messagebox.showerror("Navigation Error", f"Failed to load {nav_key} page: {str(e)}")

    def _on_enter(self, event, canvas, button, icon_key):
        if self.is_animating.get(icon_key, False):
            return

        self.is_animating[icon_key] = True
        button.config(bg=self.COLORS['secondary_accent'], image=self.icons_large[icon_key])

        for i in range(5):
            offset = i * 2
            canvas.after(i * 50, lambda o=offset: canvas.coords(
                canvas.find_withtag("current")[0], 33 - o, 33 - o
            ))

        canvas.after(250, lambda: self._return_to_initial(canvas, button, icon_key))

    def _return_to_initial(self, canvas, button, icon_key):
        button.config(bg=self.COLORS['button_start'], image=self.icons[icon_key])

        for i in range(5, -1, -1):
            offset = i * 2
            canvas.after((5 - i) * 50, lambda o=offset: canvas.coords(
                canvas.find_withtag("current")[0], 33 - o, 33 - o
            ))

        canvas.after(250, lambda: self._reset_animation_state(icon_key))

    def _on_press(self, event, button):
        button.config(bg=self.COLORS['button_end'])

    def _on_release(self, event, button):
        button.config(bg=self.COLORS['button_start'])

    def _reset_animation_state(self, icon_key):
        self.is_animating[icon_key] = False

    def _create_stats_panel(self):
        stats_frame = tk.Frame(self, bg=self.COLORS['background'], padx=5, pady=5)
        stats_frame.pack(fill="x", pady=(0, 2))  # Reduced pady from 5 to 2 to decrease space between charts

        billing_data = self._get_billing_data()
        stats = [
            {'title': 'Monthly Product Sales', 'value': 'chart'},
            {'title': 'Last 6 Month Bill Details', 'value': 'chart'},
            {'title': 'Monthly Revenue Comparison', 'value': 'chart'},
            {'title': 'Monthly Revenue', 'value': 'chart'}
        ]

        for stat in stats:
            card = tk.Frame(
                stats_frame,
                bg=self.COLORS['card_bg'],
                padx=10,
                pady=10,  # Reduced padding to give more space for chart
                highlightbackground=self.COLORS['border'],
                highlightthickness=1,
                relief="solid"
            )
            card.pack(side="left", padx=10, fill="x", expand=True)

            # Move title upward with padding
            tk.Label(
                card,
                text=stat['title'],
                font=("Helvetica", 10),
                fg=self.COLORS['text_secondary'],
                bg=self.COLORS['card_bg']
            ).pack(anchor="nw", pady=(5, 0))  # Adjusted padding to move title up

            if stat['title'] == 'Monthly Product Sales':
                self.product_sales_card = card
                self._create_product_sales_bar_chart(card)
            elif stat['title'] == 'Last 6 Month Bill Details':
                self._create_last_six_months_bills_chart(card)
            elif stat['title'] == 'Monthly Revenue Comparison':
                self.monthly_revenue_card = card
                self._create_monthly_revenue_comparison_chart(card)
            elif stat['title'] == 'Monthly Revenue':
                self.monthly_revenue_by_region_card = card
                self._create_monthly_revenue_by_region_chart(card)

    def _create_product_sales_bar_chart(self, parent):
        product_sales = self._get_product_sales()
        
        # Increased vertical size from 0.92 to 1.2
        self.product_sales_fig = Figure(figsize=(1.2, 1.2), dpi=100, facecolor=self.COLORS['card_bg'])
        self.product_sales_ax = self.product_sales_fig.add_subplot(111, facecolor=self.COLORS['card_bg'])
        
        self._update_product_sales_plot(product_sales)
        
        self.product_sales_canvas = FigureCanvasTkAgg(self.product_sales_fig, master=parent)
        self.product_sales_canvas.draw()
        self.product_sales_canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True, padx=0, pady=0)

    def _update_product_sales_plot(self, product_sales):
        self.product_sales_ax.clear()
        
        if not product_sales:
            self.product_sales_ax.text(0.5, 0.5, 'No product sales data',
                   ha='center', va='center', fontsize=8, color=self.COLORS['text_secondary'])
            self.product_sales_ax.axis('off')
        else:
            products = [prod[0] for prod in product_sales]
            sales = [prod[1] for prod in product_sales]
            
            colors = [self.COLORS['bar_colors'][i % len(self.COLORS['bar_colors'])] for i in range(len(products))]
            
            bars = self.product_sales_ax.bar(products, sales, color=colors, edgecolor=self.COLORS['border'])
            self.product_sales_ax.set_ylim(0, None)
            
            for bar in bars:
                height = bar.get_height()
                self.product_sales_ax.text(bar.get_x() + bar.get_width()/2., height,
                        f'{int(height)}',
                        ha='center', va='bottom', fontsize=6, color=self.COLORS['text_primary'])
            
            annot = self.product_sales_ax.annotate("", xy=(0, 0), xytext=(5, -20), textcoords="offset points",
                                bbox=dict(boxstyle="round,pad=0.5", fc=self.COLORS['secondary_accent'], alpha=0.8),
                                fontsize=8, color="#FFFFFF", ha='center', va='top', zorder=10)
            annot.set_visible(False)

            def update_annot(bar, idx):
                x = bar.get_x() + bar.get_width()/2.
                y = bar.get_height()
                annot.xy = (x, y)
                text = f"{sales[idx]}"  # Updated tooltip to show only the sales number
                annot.set_text(text)
                annot.get_bbox_patch().set_alpha(0.8)

            def hover(event):
                vis = annot.get_visible()
                if event.inaxes == self.product_sales_ax:
                    for i, bar in enumerate(bars):
                        cont, ind = bar.contains(event)
                        if cont:
                            update_annot(bar, i)
                            annot.set_visible(True)
                            self.product_sales_fig.canvas.draw_idle()
                            return
                    if vis:
                        annot.set_visible(False)
                        self.product_sales_fig.canvas.draw_idle()

            self.product_sales_fig.canvas.mpl_connect("motion_notify_event", hover)
            
            self.product_sales_ax.spines['top'].set_visible(False)
            self.product_sales_ax.spines['right'].set_visible(False)
            self.product_sales_ax.spines['bottom'].set_color(self.COLORS['border'])
            self.product_sales_ax.spines['left'].set_color(self.COLORS['border'])
            self.product_sales_ax.tick_params(axis='x', colors=self.COLORS['text_secondary'], rotation=45, labelsize=6)
            self.product_sales_ax.tick_params(axis='y', colors=self.COLORS['text_secondary'], labelsize=6)
            
            self.product_sales_ax.set_xlabel('')
            self.product_sales_ax.set_ylabel('')
            
            self.product_sales_fig.tight_layout(pad=0.5)
        
        if hasattr(self, 'product_sales_canvas'):
            self.product_sales_canvas.draw()

    def _update_product_sales_chart(self):
        product_sales = self._get_product_sales()
        self._update_product_sales_plot(product_sales)
        self.after(5000, self._update_product_sales_chart)

    def _create_last_six_months_bills_chart(self, parent):
        paid_bills, unpaid_bills = self._get_last_six_months_bills()
        
        # Increased vertical size from 0.92 to 1.2
        fig = Figure(figsize=(1.2, 1.2), dpi=100, facecolor=self.COLORS['card_bg'])
        ax = fig.add_subplot(111, facecolor=self.COLORS['card_bg'])
        
        if (not paid_bills or all(amount == 0 for _, _, amount in paid_bills)) and \
           (not unpaid_bills or all(amount == 0 for _, _, amount in unpaid_bills)):
            ax.text(0.5, 0.5, 'No bills data',
                   ha='center', va='center', fontsize=8, color=self.COLORS['text_secondary'])
            ax.axis('off')
        else:
            labels = [f"{month:02d}/{str(year)[-2:]}" for month, year, _ in paid_bills]
            paid_amounts = [amount for _, _, amount in paid_bills]
            unpaid_amounts = [amount for _, _, amount in unpaid_bills]
            
            paid_amounts_in_lakhs = [amount / 100000 for amount in paid_amounts]
            unpaid_amounts_in_lakhs = [amount / 100000 for amount in unpaid_amounts]
            
            bar_width = 0.35
            group_width = bar_width * 2
            spacing = 0.5
            indices = np.arange(len(labels)) * (group_width + spacing)
            
            paid_bars = ax.bar(
                indices,
                paid_amounts_in_lakhs,
                bar_width,
                color=self.COLORS['pie_color1'],
                edgecolor=self.COLORS['border']
            )
            
            unpaid_bars = ax.bar(
                indices + bar_width,
                unpaid_amounts_in_lakhs,
                bar_width,
                color=self.COLORS['pie_color2'],
                edgecolor=self.COLORS['border']
            )
            
            ax.set_ylim(0, None)
            
            for bars, amounts, amounts_in_lakhs in [(paid_bars, paid_amounts, paid_amounts_in_lakhs),
                                            (unpaid_bars, unpaid_amounts, unpaid_amounts_in_lakhs)]:
                for bar, amount, amount_in_lakhs in zip(bars, amounts, amounts_in_lakhs):
                    if amount_in_lakhs > 0:
                        ax.text(
                            bar.get_x() + bar.get_width()/2., amount_in_lakhs,
                            f'{amount_in_lakhs:.2f}L',
                            ha='center', va='bottom', fontsize=6, color=self.COLORS['text_primary']
                        )
            
            annot = ax.annotate("", xy=(0, 0), xytext=(5, -20), textcoords="offset points",
                                bbox=dict(boxstyle="round,pad=0.5", fc=self.COLORS['secondary_accent'], alpha=0.8),
                                fontsize=8, color="#FFFFFF", ha='center', va='top', zorder=10)
            annot.set_visible(False)

            def update_annot(bar, is_paid, idx):
                x = bar.get_x() + bar.get_width()/2.
                y = bar.get_height()
                annot.xy = (x, y)
                exact_amount = paid_amounts[idx] if is_paid else unpaid_amounts[idx]
                text = f"{exact_amount:,.0f}"  # Updated tooltip to show only the amount
                annot.set_text(text)
                annot.get_bbox_patch().set_alpha(0.8)

            def hover(event):
                vis = annot.get_visible()
                if event.inaxes == ax:
                    for i, bar in enumerate(paid_bars):
                        cont, ind = bar.contains(event)
                        if cont:
                            update_annot(bar, True, i)
                            annot.set_visible(True)
                            fig.canvas.draw_idle()
                            return
                        
                    for i, bar in enumerate(unpaid_bars):
                        cont, ind = bar.contains(event)
                        if cont:
                            update_annot(bar, False, i)
                            annot.set_visible(True)
                            fig.canvas.draw_idle()
                            return
                        
                    if vis:
                        annot.set_visible(False)
                        fig.canvas.draw_idle()

            fig.canvas.mpl_connect("motion_notify_event", hover)
            
            ax.set_xticks(indices + bar_width / 2)
            ax.set_xticklabels(labels)
            
            ax.spines['top'].set_visible(False)
            ax.spines['right'].set_visible(False)
            ax.spines['bottom'].set_color(self.COLORS['border'])
            ax.spines['left'].set_color(self.COLORS['border'])
            ax.tick_params(axis='x', colors=self.COLORS['text_secondary'], rotation=45, labelsize=6)
            ax.tick_params(axis='y', colors=self.COLORS['text_secondary'], labelsize=6)
            
            ax.set_xlabel('')
            ax.set_ylabel('')
            
            fig.tight_layout(pad=0.5)

        canvas = FigureCanvasTkAgg(fig, master=parent)
        canvas.draw()
        canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True, padx=0, pady=0)

    def _create_monthly_revenue_comparison_chart(self, parent):
        total_revenue, paid_amount, unpaid_amount = self._get_monthly_revenue_data()
        
        # Increased vertical size from 0.92 to 1.2
        self.monthly_revenue_fig = Figure(figsize=(1.2, 1.2), dpi=100, facecolor=self.COLORS['card_bg'])
        self.monthly_revenue_ax = self.monthly_revenue_fig.add_subplot(111, facecolor=self.COLORS['card_bg'])
        
        self._update_monthly_revenue_plot(total_revenue, paid_amount, unpaid_amount)
        
        self.monthly_revenue_canvas = FigureCanvasTkAgg(self.monthly_revenue_fig, master=parent)
        self.monthly_revenue_canvas.draw()
        self.monthly_revenue_canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True, padx=0, pady=0)

    def _update_monthly_revenue_plot(self, total_revenue, paid_amount, unpaid_amount):
        self.monthly_revenue_ax.clear()
        
        if total_revenue == 0 and paid_amount == 0 and unpaid_amount == 0:
            self.monthly_revenue_ax.text(0.5, 0.5, 'No revenue data',
                   ha='center', va='center', fontsize=8, color=self.COLORS['text_secondary'])
            self.monthly_revenue_ax.axis('off')
        else:
            labels = ['Total Revenue', 'Paid Bills', 'Unpaid Bills']
            amounts = [total_revenue, paid_amount, unpaid_amount]
            amounts_in_lakhs = [amt / 100000 for amt in amounts]
            
            colors = [self.COLORS['bar_colors'][i] for i in [0, 1, 3]]
            
            bars = self.monthly_revenue_ax.bar(labels, amounts_in_lakhs, color=colors, edgecolor=self.COLORS['border'])
            self.monthly_revenue_ax.set_ylim(0, None)
            
            for bar in bars:
                height = bar.get_height()
                if height > 0:
                    self.monthly_revenue_ax.text(bar.get_x() + bar.get_width()/2., height,
                            f'{height:.2f}L',
                            ha='center', va='bottom', fontsize=6, color=self.COLORS['text_primary'])
            
            annot = self.monthly_revenue_ax.annotate("", xy=(0, 0), xytext=(5, -20), textcoords="offset points",
                                bbox=dict(boxstyle="round,pad=0.5", fc=self.COLORS['secondary_accent'], alpha=0.8),
                                fontsize=8, color="#FFFFFF", ha='center', va='top', zorder=10)
            annot.set_visible(False)

            def update_annot(bar, idx):
                x = bar.get_x() + bar.get_width()/2.
                y = bar.get_height()
                annot.xy = (x, y)
                text = f"{amounts[idx]:,.0f}"  # Updated tooltip to show only the amount
                annot.set_text(text)
                annot.get_bbox_patch().set_alpha(0.8)

            def hover(event):
                vis = annot.get_visible()
                if event.inaxes == self.monthly_revenue_ax:
                    for i, bar in enumerate(bars):
                        cont, ind = bar.contains(event)
                        if cont:
                            update_annot(bar, i)
                            annot.set_visible(True)
                            self.monthly_revenue_fig.canvas.draw_idle()
                            return
                    if vis:
                        annot.set_visible(False)
                        self.monthly_revenue_fig.canvas.draw_idle()

            self.monthly_revenue_fig.canvas.mpl_connect("motion_notify_event", hover)
            
            self.monthly_revenue_ax.spines['top'].set_visible(False)
            self.monthly_revenue_ax.spines['right'].set_visible(False)
            self.monthly_revenue_ax.spines['bottom'].set_color(self.COLORS['border'])
            self.monthly_revenue_ax.spines['left'].set_color(self.COLORS['border'])
            self.monthly_revenue_ax.tick_params(axis='x', colors=self.COLORS['text_secondary'], labelsize=6)
            self.monthly_revenue_ax.tick_params(axis='y', colors=self.COLORS['text_secondary'], labelsize=6)
            
            self.monthly_revenue_ax.set_xlabel('')
            self.monthly_revenue_ax.set_ylabel('')
            
            self.monthly_revenue_fig.tight_layout(pad=0.5)
        
        if hasattr(self, 'monthly_revenue_canvas'):
            self.monthly_revenue_canvas.draw()

    def _update_monthly_revenue_comparison_chart(self):
        total_revenue, paid_amount, unpaid_amount = self._get_monthly_revenue_data()
        self._update_monthly_revenue_plot(total_revenue, paid_amount, unpaid_amount)
        self.after(5000, self._update_monthly_revenue_comparison_chart)

    def _create_monthly_revenue_by_region_chart(self, parent):
        try:
            region_data = self._get_monthly_revenue_by_region()
            
            # Increased vertical size from 0.92 to 1.2
            self.monthly_revenue_by_region_fig = Figure(figsize=(1.2, 1.2), dpi=100, facecolor=self.COLORS['card_bg'])
            self.monthly_revenue_by_region_ax = self.monthly_revenue_by_region_fig.add_subplot(111, facecolor=self.COLORS['card_bg'])
            
            self._update_monthly_revenue_by_region_plot(region_data)
            
            self.monthly_revenue_by_region_canvas = FigureCanvasTkAgg(self.monthly_revenue_by_region_fig, master=parent)
            self.monthly_revenue_by_region_canvas.draw()
            self.monthly_revenue_by_region_canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True, padx=0, pady=0)
            logging.info("Successfully created monthly revenue by region chart")
        except Exception as e:
            logging.error(f"Error creating monthly revenue by region chart: {str(e)}")
            messagebox.showerror("Chart Error", f"Failed to create monthly revenue by region chart: {str(e)}")

    def _update_monthly_revenue_by_region_plot(self, region_data):
        self.monthly_revenue_by_region_ax.clear()
        
        if not region_data:
            self.monthly_revenue_by_region_ax.text(0.5, 0.5, 'No region revenue data',
                   ha='center', va='center', fontsize=8, color=self.COLORS['text_secondary'])
            self.monthly_revenue_by_region_ax.axis('off')
        else:
            regions = [data[0] for data in region_data]
            paid_amounts = [data[1] or 0 for data in region_data]
            unpaid_amounts = [data[2] or 0 for data in region_data]
            
            paid_amounts_in_lakhs = [amt / 100000 for amt in paid_amounts]
            unpaid_amounts_in_lakhs = [amt / 100000 for amt in unpaid_amounts]
            
            bar_width = 0.35
            group_width = bar_width * 2
            spacing = 0.5
            indices = np.arange(len(regions)) * (group_width + spacing)
            
            paid_bars = self.monthly_revenue_by_region_ax.bar(
                indices,
                paid_amounts_in_lakhs,
                bar_width,
                color=self.COLORS['pie_color1'],
                edgecolor=self.COLORS['border'],
                label='Paid'
            )
            
            unpaid_bars = self.monthly_revenue_by_region_ax.bar(
                indices + bar_width,
                unpaid_amounts_in_lakhs,
                bar_width,
                color=self.COLORS['pie_color2'],
                edgecolor=self.COLORS['border'],
                label='Unpaid'
            )
            
            self.monthly_revenue_by_region_ax.set_ylim(0, None)
            
            for bars, amounts, amounts_in_lakhs in [(paid_bars, paid_amounts, paid_amounts_in_lakhs),
                                                  (unpaid_bars, unpaid_amounts, unpaid_amounts_in_lakhs)]:
                for bar, amount, amount_in_lakhs in zip(bars, amounts, amounts_in_lakhs):
                    if amount_in_lakhs > 0:
                        self.monthly_revenue_by_region_ax.text(
                            bar.get_x() + bar.get_width()/2., amount_in_lakhs,
                            f'{amount_in_lakhs:.2f}L',
                            ha='center', va='bottom', fontsize=6, color=self.COLORS['text_primary']
                        )
            
            annot = self.monthly_revenue_by_region_ax.annotate("", xy=(0, 0), xytext=(5, -20),
                                textcoords="offset points",
                                bbox=dict(boxstyle="round,pad=0.5", fc=self.COLORS['secondary_accent'], alpha=0.8),
                                fontsize=8, color="#FFFFFF", ha='center', va='top', zorder=10)
            annot.set_visible(False)
            
            def update_annot(bar, is_paid, idx):
                x = bar.get_x() + bar.get_width()/2
                y = bar.get_height()
                annot.xy = (x, y)
                exact_amount = paid_amounts[idx] if is_paid else unpaid_amounts[idx]
                text = f"{exact_amount:,.0f}"  # Updated tooltip to show only the amount
                annot.set_text(text)
                annot.get_bbox_patch().set_alpha(0.8)
            
            def hover(event):
                vis = annot.get_visible()
                if event.inaxes == self.monthly_revenue_by_region_ax:
                    for i, bar in enumerate(paid_bars):
                        cont, ind = bar.contains(event)
                        if cont:
                            update_annot(bar, True, i)
                            annot.set_visible(True)
                            self.monthly_revenue_by_region_fig.canvas.draw_idle()
                            return
                    
                    for i, bar in enumerate(unpaid_bars):
                        cont, ind = bar.contains(event)
                        if cont:
                            update_annot(bar, False, i)
                            annot.set_visible(True)
                            self.monthly_revenue_by_region_fig.canvas.draw_idle()
                            return
                    
                    if vis:
                        annot.set_visible(False)
                        self.monthly_revenue_by_region_fig.canvas.draw_idle()
            
            self.monthly_revenue_by_region_fig.canvas.mpl_connect("motion_notify_event", hover)
            
            self.monthly_revenue_by_region_ax.set_xticks(indices + bar_width / 2)
            self.monthly_revenue_by_region_ax.set_xticklabels(regions)
            
            self.monthly_revenue_by_region_ax.spines['top'].set_visible(False)
            self.monthly_revenue_by_region_ax.spines['right'].set_visible(False)
            self.monthly_revenue_by_region_ax.spines['bottom'].set_color(self.COLORS['border'])
            self.monthly_revenue_by_region_ax.spines['left'].set_color(self.COLORS['border'])
            self.monthly_revenue_by_region_ax.tick_params(axis='x', colors=self.COLORS['text_secondary'], labelsize=6)
            self.monthly_revenue_by_region_ax.tick_params(axis='y', colors=self.COLORS['text_secondary'], labelsize=6)
            
            self.monthly_revenue_by_region_ax.set_xlabel('')
            self.monthly_revenue_by_region_ax.set_ylabel('')
            
            self.monthly_revenue_by_region_fig.tight_layout(pad=0.5)
        
        if hasattr(self, 'monthly_revenue_by_region_canvas'):
            self.monthly_revenue_by_region_canvas.draw()

    def _update_monthly_revenue_by_region_chart(self):
        region_data = self._get_monthly_revenue_by_region()
        self._update_monthly_revenue_by_region_plot(region_data)
        self.after(5000, self._update_monthly_revenue_by_region_chart)

    def _create_categories_panel(self):
        # Reduced pady from 25 to 5 to remove extra space
        self.categories_frame = tk.Frame(self, bg=self.COLORS['background'], padx=20, pady=5)
        self.categories_frame.pack(fill="x", pady=(0, 0))

        ttk.Separator(self.categories_frame, orient="horizontal").pack(fill="x", pady=5)

        tk.Label(
            self.categories_frame,
            text="Categories",
            font=("Helvetica", 14, "bold"),
            fg=self.COLORS['text_primary'],
            bg=self.COLORS['background']
        ).pack(anchor="w", pady=(0, 10))

        self.content_frame = tk.Frame(self.categories_frame, bg=self.COLORS['background'])
        self.content_frame.pack(fill="both", expand=True)

        self.content_frame.grid_columnconfigure(0, weight=1)
        self.content_frame.grid_columnconfigure(1, weight=1)
        self.content_frame.grid_columnconfigure(2, weight=1)
        self.content_frame.grid_rowconfigure(0, weight=1)

        self.categories_content = tk.Frame(
            self.content_frame,
            bg=self.COLORS['div_background'],
            padx=5,
            pady=12,
            highlightbackground=self.COLORS['border'],
            highlightthickness=1
        )
        self.categories_content.grid(row=0, column=0, sticky="nsew", padx=(0, 10))

        self.line_chart_container = tk.Frame(
            self.content_frame,
            bg=self.COLORS['div_background'],
            highlightbackground=self.COLORS['border'],
            highlightthickness=1,
            padx=5,
            pady=12
        )
        self.line_chart_container.grid(row=0, column=1, sticky="nsew", padx=(0, 10))

        self.stock_container = tk.Frame(
            self.content_frame,
            bg=self.COLORS['div_background'],
            highlightbackground=self.COLORS['border'],
            highlightthickness=1,
            padx=5,
            pady=12
        )
        self.stock_container.grid(row=0, column=2, sticky="nsew")

        self._create_categories_content()
        self._create_new_customers_chart()

    def _create_categories_content(self):
        tk.Label(
            self.categories_content,
            text="Package Plans",
            font=("Helvetica", 12, "bold"),
            fg=self.COLORS['text_primary'],
            bg=self.COLORS['div_background']
        ).pack(anchor="w", pady=(0, 10))

        packages = self._get_package_plans()
        for name, price in packages:
            tk.Label(
                self.categories_content,
                text=f"{name}: {price:,.0f} PKR",
                font=("Helvetica", 10),
                fg=self.COLORS['text_secondary'],
                bg=self.COLORS['div_background']
            ).pack(anchor="w", pady=2)

        tk.Label(
            self.categories_content,
            text="Top Package",
            font=("Helvetica", 12, "bold"),
            fg=self.COLORS['text_primary'],
            bg=self.COLORS['div_background']
        ).pack(anchor="w", pady=(20, 10))

        top_package = self._get_top_package()
        if top_package:
            name, sales = top_package
            tk.Label(
                self.categories_content,
                text=f"{name}: {sales} sales",
                font=("Helvetica", 10),
                fg=self.COLORS['text_secondary'],
                bg=self.COLORS['div_background']
            ).pack(anchor="w", pady=2)
        else:
            tk.Label(
                self.categories_content,
                text="No sales data",
                font=("Helvetica", 10),
                fg=self.COLORS['text_secondary'],
                bg=self.COLORS['div_background']
            ).pack(anchor="w", pady=2)

    def _create_new_customers_chart(self):
        tk.Label(
            self.line_chart_container,
            text="New Customers (This Month)",
            font=("Helvetica", 12, "bold"),
            fg=self.COLORS['text_primary'],
            bg=self.COLORS['div_background']
        ).pack(anchor="w", pady=(0, 10))

        # Create a matplotlib figure for the line chart
        self.line_fig = Figure(figsize=(3, 2), dpi=100, facecolor=self.COLORS['div_background'])
        self.line_ax = self.line_fig.add_subplot(111, facecolor=self.COLORS['div_background'])

        self._update_new_customers_plot()

        # Embed the plot in Tkinter
        self.line_canvas = FigureCanvasTkAgg(self.line_fig, master=self.line_chart_container)
        self.line_canvas.draw()
        self.line_canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True, padx=0, pady=0)

        # Schedule periodic updates
        self._update_new_customers_chart()

    def _update_new_customers_plot(self):
        self.line_ax.clear()

        dates, counts = self._get_new_customers_data()

        if not dates or not counts or sum(counts) == 0:
            self.line_ax.text(0.5, 0.5, 'No new customers',
                ha='center', va='center', fontsize=8, color=self.COLORS['text_secondary'])
            self.line_ax.axis('off')
        else:
            self.line_ax.plot(dates, counts, color=self.COLORS['line_chart'], linewidth=2)
            
            self.line_ax.fill_between(dates, counts, color=self.COLORS['line_chart'], alpha=0.1)
            
            self.line_ax.xaxis.set_major_formatter(mdates.DateFormatter('%d'))
            self.line_ax.xaxis.set_major_locator(mdates.DayLocator(interval=5))

            self.line_ax.spines['top'].set_visible(False)
            self.line_ax.spines['right'].set_visible(False)
            self.line_ax.spines['bottom'].set_color(self.COLORS['border'])
            self.line_ax.spines['left'].set_color(self.COLORS['border'])
            self.line_ax.tick_params(axis='x', colors=self.COLORS['text_secondary'], labelsize=6)
            self.line_ax.tick_params(axis='y', colors=self.COLORS['text_secondary'], labelsize=6)
            
            self.line_ax.set_xlabel('Day', fontsize=6, color=self.COLORS['text_secondary'])
            self.line_ax.set_ylabel('New Customers', fontsize=6, color=self.COLORS['text_secondary'])
            
            self.line_fig.tight_layout(pad=0.5)

        if hasattr(self, 'line_canvas'):
            self.line_canvas.draw()

    def _update_new_customers_chart(self):
        self._update_new_customers_plot()
        self.after(5000, self._update_new_customers_chart)

    def _create_stock_chart(self):
        """Create the stock bar chart in the stock container"""
        tk.Label(
            self.stock_container,
            text="Stock Levels",
            font=("Helvetica", 12, "bold"),
            fg=self.COLORS['text_primary'],
            bg=self.COLORS['div_background']
        ).pack(anchor="w", pady=(0, 10))

        # Create a matplotlib figure for the stock chart
        self.stock_fig = Figure(figsize=(3, 2), dpi=100, facecolor=self.COLORS['div_background'])
        self.stock_ax = self.stock_fig.add_subplot(111, facecolor=self.COLORS['div_background'])

        self._update_stock_plot()

        # Embed the plot in Tkinter
        self.stock_canvas = FigureCanvasTkAgg(self.stock_fig, master=self.stock_container)
        self.stock_canvas.draw()
        self.stock_canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True, padx=0, pady=0)

    def _update_stock_plot(self):
        """Update the stock bar chart with current stock data"""
        self.stock_ax.clear()

        stock_data = self._get_stock_data()

        if not stock_data:
            self.stock_ax.text(0.5, 0.5, 'No stock data',
                             ha='center', va='center', fontsize=8, color=self.COLORS['text_secondary'])
            self.stock_ax.axis('off')
        else:
            products = [item[0] for item in stock_data]
            quantities = [item[1] for item in stock_data]

            colors = [self.COLORS['bar_colors'][i % len(self.COLORS['bar_colors'])] for i in range(len(products))]

            bars = self.stock_ax.bar(products, quantities, color=colors, edgecolor=self.COLORS['border'])
            self.stock_ax.set_ylim(0, max(quantities) * 1.1 if quantities else 1)

            for bar in bars:
                height = bar.get_height()
                self.stock_ax.text(bar.get_x() + bar.get_width()/2., height,
                                 f'{int(height)}',
                                 ha='center', va='bottom', fontsize=6, color=self.COLORS['text_primary'])

            annot = self.stock_ax.annotate("", xy=(0, 0), xytext=(5, -20), textcoords="offset points",
                            bbox=dict(boxstyle="round,pad=0.5", fc=self.COLORS['secondary_accent'], alpha=0.8),
                            fontsize=8, color="#FFFFFF", ha='center', va='top', zorder=10)
            annot.set_visible(False)

            def update_annot(bar, idx):
                x = bar.get_x() + bar.get_width()/2
                y = bar.get_height()
                annot.xy = (x, y)
                text = f"{quantities[idx]}"  # Updated tooltip to show only the quantity
                annot.set_text(text)
                annot.get_bbox_patch().set_alpha(0.8)

            def hover(event):
                vis = annot.get_visible()
                if event.inaxes == self.stock_ax:
                    for i, bar in enumerate(bars):
                        cont, ind = bar.contains(event)
                        if cont:
                            update_annot(bar, i)
                            annot.set_visible(True)
                            self.stock_fig.canvas.draw_idle()
                            return
                    if vis:
                        annot.set_visible(False)
                        self.stock_fig.canvas.draw_idle()

            self.stock_fig.canvas.mpl_connect("motion_notify_event", hover)

            self.stock_ax.spines['top'].set_visible(False)
            self.stock_ax.spines['right'].set_visible(False)
            self.stock_ax.spines['bottom'].set_color(self.COLORS['border'])
            self.stock_ax.spines['left'].set_color(self.COLORS['border'])
            self.stock_ax.tick_params(axis='x', colors=self.COLORS['text_secondary'], rotation=45, labelsize=6)
            self.stock_ax.tick_params(axis='y', colors=self.COLORS['text_secondary'], labelsize=6)
            
            self.stock_ax.set_xlabel('')
            self.stock_ax.set_ylabel('')

            self.stock_fig.tight_layout(pad=0.5)

        if hasattr(self, 'stock_canvas'):
            self.stock_canvas.draw()

    def _refresh_stock_chart(self):
        """Refresh the stock chart periodically"""
        self._update_stock_plot()
        self.after(5000, self._refresh_stock_chart)

    def refresh_stock(self):
        """Public method to refresh stock chart on demand"""
        self._update_stock_plot()

    def _create_ui(self):
        self._create_header()
        self._create_navigation_panel()
        self._create_stats_panel()
        self._create_categories_panel()
        self._create_stock_chart()
