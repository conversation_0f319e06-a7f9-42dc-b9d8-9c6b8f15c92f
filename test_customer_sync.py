#!/usr/bin/env python3
"""
Test script to verify customer-billing synchronization
"""
import sqlite3
import sys
import os
from datetime import datetime

# Add the project root to the path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from database_utils import get_db_connection

def test_customer_billing_sync():
    """Test that customer and billing tables are properly synchronized"""
    print("Testing customer-billing synchronization...")
    
    try:
        with get_db_connection() as conn:
            c = conn.cursor()
            
            # Get all customers with their outstanding amounts
            c.execute('''
                SELECT c.id, c.user_name, c.name, c.outstanding_amount, c.credit_balance
                FROM customers c
                ORDER BY c.id
            ''')
            customers = c.fetchall()
            
            print(f"\nFound {len(customers)} customers:")
            print("ID | User Name | Name | Outstanding | Credit")
            print("-" * 60)
            
            for customer in customers:
                customer_id, user_name, name, outstanding, credit = customer
                outstanding = outstanding or 0.0
                credit = credit or 0.0
                
                # Calculate outstanding from billing table
                c.execute('''
                    SELECT COALESCE(SUM(amount - COALESCE(paid_amount, 0)), 0) as total_outstanding
                    FROM billing
                    WHERE customer_id = ? AND (amount - COALESCE(paid_amount, 0)) > 0
                ''', (customer_id,))
                calculated_outstanding = c.fetchone()[0] or 0.0
                
                # Get credit from billing table
                c.execute('''
                    SELECT COALESCE(SUM(credit_amount), 0) as total_credit
                    FROM billing
                    WHERE customer_id = ? AND credit_amount > 0
                ''', (customer_id,))
                calculated_credit = c.fetchone()[0] or 0.0
                
                sync_status = "✓" if (abs(outstanding - calculated_outstanding) < 0.01 and 
                                    abs(credit - calculated_credit) < 0.01) else "✗"
                
                print(f"{customer_id:2d} | {user_name:9s} | {name:4s} | {outstanding:11.2f} | {credit:6.2f} {sync_status}")
                
                if sync_status == "✗":
                    print(f"    Calculated: Outstanding={calculated_outstanding:.2f}, Credit={calculated_credit:.2f}")
                    
                # Show billing records for this customer
                c.execute('''
                    SELECT month, year, amount, paid_amount, outstanding_amount, credit_amount, status
                    FROM billing
                    WHERE customer_id = ?
                    ORDER BY year DESC, month DESC
                ''', (customer_id,))
                bills = c.fetchall()
                
                if bills:
                    print(f"    Bills: {len(bills)} records")
                    for bill in bills[:3]:  # Show only first 3 bills
                        month, year, amount, paid, bill_outstanding, bill_credit, status = bill
                        paid = paid or 0.0
                        bill_outstanding = bill_outstanding or 0.0
                        bill_credit = bill_credit or 0.0
                        print(f"      {month:02d}/{year}: Amount={amount:.2f}, Paid={paid:.2f}, Outstanding={bill_outstanding:.2f}, Credit={bill_credit:.2f}, Status={status}")
                else:
                    print(f"    No billing records found")
                print()
                
    except Exception as e:
        print(f"Error: {e}")
        return False
    
    return True

if __name__ == "__main__":
    test_customer_billing_sync()
