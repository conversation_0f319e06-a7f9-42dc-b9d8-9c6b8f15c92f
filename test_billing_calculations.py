#!/usr/bin/env python3
"""
Test script to verify the new billing calculation system:
1. Display values come from payment history table
2. Billing table stores only last bill's credit/outstanding for calculations
3. Month Bill = Current Bill + Previous Outstanding - Previous Credit
4. Test different bill types and payment scenarios
"""

import sqlite3
import sys
import os
from datetime import datetime

# Add the current directory to the path so we can import our modules
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from database import Database

def create_test_scenario():
    """Create a test scenario with multiple bills and payments"""
    print("Creating test billing scenario...")
    
    db = Database()
    
    with db.get_connection_context() as conn:
        c = conn.cursor()
        
        # Get or create a test customer
        c.execute("SELECT id, name FROM customers LIMIT 1")
        customer = c.fetchone()
        if not customer:
            print("No customers found. Please add a customer first.")
            return None
            
        customer_id, customer_name = customer
        print(f"Using customer: {customer_name} (ID: {customer_id})")
        
        # Clear existing bills for this customer to start fresh
        c.execute("DELETE FROM billing WHERE customer_id = ?", (customer_id,))
        c.execute("DELETE FROM payment_history WHERE customer_id = ?", (customer_id,))
        
        # Create test bills scenario
        bills = []
        
        # Bill 1: January 2025 - Package bill (1500 PKR)
        c.execute('''
            INSERT INTO billing 
            (customer_id, month, year, amount, paid_amount, status, outstanding_amount, credit_amount, is_manual, invoice_number)
            VALUES (?, 1, 2025, 1500, 0, 'Unpaid', 1500, 0, 0, 'INV-TEST1')
        ''', (customer_id,))
        bill1_id = c.lastrowid
        bills.append(('January Package Bill', bill1_id, 1500))
        
        # Payment for Bill 1: Partial payment of 1000 PKR (500 outstanding)
        c.execute('''
            UPDATE billing SET paid_amount = 1000, outstanding_amount = 500, status = 'Partially Paid'
            WHERE id = ?
        ''', (bill1_id,))
        
        c.execute('''
            INSERT INTO payment_history
            (bill_id, invoice_number, customer_id, payment_date, amount_paid, credit_amount, outstanding_amount, payment_method, recorded_by)
            VALUES (?, 'INV-TEST1', ?, '2025-01-15', 1000, 0, 500, 'Test', 'Test User')
        ''', (bill1_id, customer_id))
        
        # Bill 2: February 2025 - Package bill (1500 PKR) + Previous Outstanding (500)
        # Month Bill should be: 1500 + 500 = 2000 PKR
        c.execute('''
            INSERT INTO billing 
            (customer_id, month, year, amount, paid_amount, status, outstanding_amount, credit_amount, is_manual, invoice_number)
            VALUES (?, 2, 2025, 1500, 0, 'Unpaid', 2000, 0, 0, 'INV-TEST2')
        ''', (customer_id,))
        bill2_id = c.lastrowid
        bills.append(('February Package Bill', bill2_id, 1500))
        
        # Clear previous bill's amounts (only latest bill stores amounts)
        c.execute('''
            UPDATE billing SET outstanding_amount = 0, credit_amount = 0 WHERE id = ?
        ''', (bill1_id,))
        
        # Payment for Bill 2: Overpayment of 2200 PKR (200 credit)
        c.execute('''
            UPDATE billing SET paid_amount = 2200, outstanding_amount = 0, credit_amount = 200, status = 'Paid'
            WHERE id = ?
        ''', (bill2_id,))
        
        c.execute('''
            INSERT INTO payment_history
            (bill_id, invoice_number, customer_id, payment_date, amount_paid, credit_amount, outstanding_amount, payment_method, recorded_by)
            VALUES (?, 'INV-TEST2', ?, '2025-02-15', 2200, 200, 0, 'Test', 'Test User')
        ''', (bill2_id, customer_id))
        
        # Bill 3: March 2025 - Manual bill (800 PKR) - Previous Credit (200)
        # Month Bill should be: 800 - 200 = 600 PKR
        c.execute('''
            INSERT INTO billing 
            (customer_id, month, year, amount, paid_amount, status, outstanding_amount, credit_amount, is_manual, invoice_number)
            VALUES (?, 3, 2025, 800, 0, 'Unpaid', 600, 0, 1, 'INV-TEST3')
        ''', (customer_id,))
        bill3_id = c.lastrowid
        bills.append(('March Manual Bill', bill3_id, 800))
        
        # Clear previous bill's amounts
        c.execute('''
            UPDATE billing SET outstanding_amount = 0, credit_amount = 0 WHERE id = ?
        ''', (bill2_id,))
        
        conn.commit()
        
        return customer_id, bills

def test_display_vs_calculation():
    """Test that display values come from payment history while calculations use billing table"""
    print("\n=== Testing Display vs Calculation Logic ===")
    
    db = Database()
    
    with db.get_connection_context() as conn:
        c = conn.cursor()
        
        # Get test bills
        c.execute('''
            SELECT b.id, b.invoice_number, b.month, b.year, b.amount, 
                   b.outstanding_amount as billing_outstanding, 
                   b.credit_amount as billing_credit,
                   b.status
            FROM billing b
            ORDER BY b.year, b.month
        ''')
        bills = c.fetchall()
        
        print("Bill Analysis:")
        print("=" * 80)
        
        for bill in bills:
            bill_id, invoice, month, year, amount, billing_outstanding, billing_credit, status = bill
            
            # Get display values from payment history
            c.execute('''
                SELECT credit_amount, outstanding_amount 
                FROM payment_history 
                WHERE bill_id = ? 
                ORDER BY timestamp DESC 
                LIMIT 1
            ''', (bill_id,))
            history_result = c.fetchone()
            
            display_credit = history_result[0] if history_result else 0
            display_outstanding = history_result[1] if history_result else 0
            
            print(f"Bill {invoice} ({month:02d}/{year}) - Amount: {amount} PKR")
            print(f"  Status: {status}")
            print(f"  Billing Table (for calculations): Outstanding={billing_outstanding}, Credit={billing_credit}")
            print(f"  Payment History (for display): Outstanding={display_outstanding}, Credit={display_credit}")
            print()

def test_month_bill_calculation():
    """Test Month Bill calculation logic"""
    print("\n=== Testing Month Bill Calculation ===")
    
    # Import the billing module to test the calculation function
    from views.billing import BillingManager
    
    # Create a mock billing manager to access the calculation method
    import tkinter as tk
    root = tk.Tk()
    root.withdraw()  # Hide the window
    
    billing_manager = BillingManager(root, {})
    
    db = Database()
    
    with db.get_connection_context() as conn:
        c = conn.cursor()
        
        # Get bills in chronological order
        c.execute('''
            SELECT id, month, year, amount, outstanding_amount, credit_amount
            FROM billing 
            ORDER BY year, month, id
        ''')
        bills = c.fetchall()
        
        print("Month Bill Calculation Test:")
        print("=" * 60)
        
        for i, bill in enumerate(bills):
            bill_id, month, year, amount, outstanding, credit = bill
            
            # Test the calculation function
            calculated_month_bill = billing_manager._calculate_month_bill_amount(c, 1, bill_id, amount)
            
            print(f"Bill {i+1} ({month:02d}/{year}):")
            print(f"  Base Amount: {amount} PKR")
            print(f"  Calculated Month Bill: {calculated_month_bill} PKR")
            
            if i == 0:
                print(f"  Logic: First bill = {amount} PKR")
            else:
                prev_bill = bills[i-1]
                prev_outstanding = prev_bill[4]
                prev_credit = prev_bill[5]
                expected = amount + prev_outstanding - prev_credit
                print(f"  Logic: {amount} + {prev_outstanding} - {prev_credit} = {expected} PKR")
            print()
    
    root.destroy()

def main():
    """Run all billing calculation tests"""
    print("=== Testing New Billing Calculation System ===\n")
    
    try:
        # Create test scenario
        result = create_test_scenario()
        if not result:
            return
            
        customer_id, bills = result
        
        print(f"\nCreated test scenario with {len(bills)} bills:")
        for bill_name, bill_id, amount in bills:
            print(f"  - {bill_name}: {amount} PKR (ID: {bill_id})")
        
        # Test display vs calculation logic
        test_display_vs_calculation()
        
        # Test month bill calculation
        test_month_bill_calculation()
        
        print("✅ Billing calculation testing completed!")
        print("\nKey features verified:")
        print("- Display values come from payment history table")
        print("- Billing table stores only last bill's amounts for calculations")
        print("- Month Bill calculation includes previous outstanding/credit")
        print("- Payment processing updates amounts correctly")
        
    except Exception as e:
        print(f"\n❌ Error during billing calculation testing: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
