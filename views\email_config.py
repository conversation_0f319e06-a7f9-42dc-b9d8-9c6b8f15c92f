import tkinter as tk
from tkinter import ttk, messagebox
import re
import logging
from database import Database
from PIL import Image, ImageTk
import os
import sys

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

class EmailConfig(tk.Frame):
    COLORS = {
        'background': '#F8F9FA',        # Light gray background
        'card_bg': '#FFFFFF',           # White for cards
        'primary_accent': '#4A6FA5',    # Primary blue accent
        'secondary_accent': '#6C8FC7',  # Lighter blue for hover
        'text_primary': '#2D3748',      # Dark gray for primary text
        'text_secondary': '#718096',    # Lighter gray for secondary text
        'button_start': '#4A6FA5',      # Button default color
        'button_end': '#3A5A8C',        # Button hover/pressed color
        'border': '#E2E8F0',            # Light gray border
        'warning': '#E53E3E',           # Red for warnings/errors
        'input_bg': '#EDF2F7',          # Light gray for input fields
    }

    def __init__(self, parent, nav_commands):
        super().__init__(parent)
        self.nav_commands = nav_commands
        self.db = Database()
        self.configure(bg=self.COLORS['background'])
        self.icons = {}
        self._load_images()
        self._create_ui()

    def _load_images(self):
        try:
            # Determine the base path (handles PyInstaller packaging)
            base_path = getattr(sys, '_MEIPASS', os.path.dirname(os.path.abspath(__file__)))
            
            # Load home.png
            img_path_home = os.path.join(base_path, 'assets', 'dashboard', 'home.png')
            logging.info(f"Loading home icon from: {img_path_home}")
            if not os.path.exists(img_path_home):
                logging.error("home.png not found!")
                self.icons['home'] = ImageTk.PhotoImage(Image.new('RGBA', (30, 30), (255, 255, 255, 0)))
            else:
                img = Image.open(img_path_home).convert("RGBA")
                data = img.getdata()
                new_data = []
                for item in data:
                    if item[3] > 0:  # If pixel is not transparent
                        new_data.append((255, 255, 255, item[3]))  # Set to white
                    else:
                        new_data.append(item)
                img.putdata(new_data)
                img = img.resize((30, 30), Image.LANCZOS)
                self.icons['home'] = ImageTk.PhotoImage(img)
        except Exception as e:
            logging.error(f"Error loading images: {str(e)}")
            self.icons['home'] = ImageTk.PhotoImage(Image.new('RGBA', (30, 30), (255, 255, 255, 0)))

    def _create_ui(self):
        main_container = tk.Frame(self, bg=self.COLORS['background'])
        main_container.pack(fill="both", expand=True)

        header_frame = tk.Frame(main_container, bg=self.COLORS['primary_accent'], height=90)
        header_frame.pack(fill="x", pady=(0, 20))

        tk.Label(
            header_frame,
            text="Email Configuration",
            font=("Helvetica", 24, "bold"),
            fg="#FFFFFF",
            bg=self.COLORS['primary_accent']
        ).place(relx=0.5, rely=0.5, anchor="center")

        home_btn = tk.Button(
            header_frame,
            image=self.icons['home'],
            command=self.nav_commands['show_dashboard'],
            bg=self.COLORS['primary_accent'],
            relief=tk.FLAT,
            activebackground=self.COLORS['secondary_accent'],
            width=30, height=30
        )
        home_btn.pack(side="left", padx=10)
        self._create_tooltip(home_btn, "Dashboard")

        config_frame = tk.Frame(main_container, bg=self.COLORS['card_bg'], 
                               highlightbackground=self.COLORS['border'], highlightthickness=1)
        config_frame.pack(fill="both", expand=True, padx=10, pady=10)

        label_style = {"bg": self.COLORS['card_bg'], "font": ("Helvetica", 10), "fg": self.COLORS['text_primary']}
        entry_style = {"font": ("Helvetica", 10), "bg": self.COLORS['input_bg']}

        # SMTP Email
        tk.Label(config_frame, text="SMTP Email:", **label_style).grid(row=0, column=0, padx=5, pady=10, sticky="e")
        self.smtp_email_entry = tk.Entry(config_frame, width=40, **entry_style)
        self.smtp_email_entry.grid(row=0, column=1, padx=5, pady=10, sticky="w")

        # SMTP Password
        tk.Label(config_frame, text="SMTP Password:", **label_style).grid(row=1, column=0, padx=5, pady=10, sticky="e")
        self.smtp_password_entry = tk.Entry(config_frame, width=40, show="*", **entry_style)
        self.smtp_password_entry.grid(row=1, column=1, padx=5, pady=10, sticky="w")

        # SMTP Server
        tk.Label(config_frame, text="SMTP Server:", **label_style).grid(row=2, column=0, padx=5, pady=10, sticky="e")
        self.smtp_server_entry = tk.Entry(config_frame, width=40, **entry_style)
        self.smtp_server_entry.insert(0, "smtp.gmail.com")
        self.smtp_server_entry.grid(row=2, column=1, padx=5, pady=10, sticky="w")

        # SMTP Port
        tk.Label(config_frame, text="SMTP Port:", **label_style).grid(row=3, column=0, padx=5, pady=10, sticky="e")
        self.smtp_port_entry = tk.Entry(config_frame, width=40, **entry_style)
        self.smtp_port_entry.insert(0, "465")
        self.smtp_port_entry.grid(row=3, column=1, padx=5, pady=10, sticky="w")

        # Buttons
        button_frame = tk.Frame(config_frame, bg=self.COLORS['card_bg'])
        button_frame.grid(row=4, column=0, columnspan=2, pady=20)
        
        tk.Button(
            button_frame,
            text="Save Configuration",
            command=self._save_config,
            bg=self.COLORS['button_start'],
            fg="#FFFFFF",
            font=("Helvetica", 10, "bold"),
            relief=tk.FLAT,
            padx=20,
            pady=10,
            activebackground=self.COLORS['button_end']
        ).pack(side="left", padx=5)
        
        tk.Button(
            button_frame,
            text="Test Email",
            command=self._test_email,
            bg=self.COLORS['button_start'],
            fg="#FFFFFF",
            font=("Helvetica", 10, "bold"),
            relief=tk.FLAT,
            padx=20,
            pady=10,
            activebackground=self.COLORS['button_end']
        ).pack(side="left", padx=5)

        self._load_config()

    def _create_tooltip(self, widget, text):
        """Create a centrally aligned tooltip for a given widget, ensuring it stays within screen boundaries."""
        def enter(event):
            # Create the tooltip
            self.tooltip = tk.Toplevel(widget)
            self.tooltip.wm_overrideredirect(True)
            label = tk.Label(
                self.tooltip,
                text=text,
                background=self.COLORS['primary_accent'],
                relief="solid",
                borderwidth=1,
                font=("Helvetica", 10),
                fg="#FFFFFF"
            )
            label.pack()

            # Get widget and screen dimensions
            widget_x = widget.winfo_rootx()
            widget_y = widget.winfo_rooty()
            widget_width = widget.winfo_width()
            tooltip_width = label.winfo_reqwidth()
            tooltip_height = label.winfo_reqheight()
            screen_width = self.winfo_screenwidth()
            screen_height = self.winfo_screenheight()

            # Center the tooltip horizontally
            x = widget_x + (widget_width - tooltip_width) // 2

            # Position tooltip above or below based on available space
            space_above = widget_y
            space_below = screen_height - (widget_y + widget.winfo_height())
            if space_above > space_below and space_above >= tooltip_height + 10:
                y = widget_y - tooltip_height - 10  # Place above
            else:
                y = widget_y + widget.winfo_height() + 10  # Place below

            # Adjust x to keep tooltip within screen bounds
            if x < 0:
                x = 0
            elif x + tooltip_width > screen_width:
                x = screen_width - tooltip_width

            # Adjust y to ensure tooltip stays on screen
            if y < 0:
                y = widget_y + widget.winfo_height() + 10  # Fallback to below
            elif y + tooltip_height > screen_height:
                y = widget_y - tooltip_height - 10  # Fallback to above

            self.tooltip.wm_geometry(f"+{x}+{y}")

        def leave(event):
            if hasattr(self, 'tooltip'):
                self.tooltip.destroy()

        widget.bind("<Enter>", enter)
        widget.bind("<Leave>", leave)

    def _load_config(self):
        try:
            config = self.db.get_email_config()
            if config:
                self.smtp_email_entry.delete(0, tk.END)
                self.smtp_email_entry.insert(0, config[1])
                self.smtp_password_entry.delete(0, tk.END)
                self.smtp_password_entry.insert(0, "********")
                self.smtp_server_entry.delete(0, tk.END)
                self.smtp_server_entry.insert(0, config[3])
                self.smtp_port_entry.delete(0, tk.END)
                self.smtp_port_entry.insert(0, str(config[4]))
        except Exception as e:
            logging.error(f"Error loading email configuration: {str(e)}")
            messagebox.showwarning("Warning", f"Failed to load email configuration: {str(e)}")

    def _save_config(self):
        smtp_email = self.smtp_email_entry.get().strip()
        smtp_password = self.smtp_password_entry.get().strip()
        smtp_server = self.smtp_server_entry.get().strip()
        smtp_port = self.smtp_port_entry.get().strip()

        if not all([smtp_email, smtp_password, smtp_server, smtp_port]):
            messagebox.showerror("Error", "Please fill in all fields")
            return

        if smtp_password == "********":
            # If password is masked, use existing password
            config = self.db.get_email_config()
            if config and config[1] == smtp_email and isinstance(config[2], str):
                smtp_password = config[2]
            else:
                messagebox.showerror("Error", "Please enter a new password for this email configuration")
                return

        try:
            # Validate email format
            email_regex = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
            if not re.match(email_regex, smtp_email):
                messagebox.showerror("Error", "Invalid email format")
                return

            # Validate port is numeric
            if not smtp_port.isdigit():
                messagebox.showerror("Error", "SMTP port must be a number")
                return

            # Save the new configuration
            self.db.save_email_config(smtp_email, smtp_password, smtp_server, int(smtp_port))
            messagebox.showinfo("Success", "Email configuration saved successfully!")
            
            # Update password entry to show masked version
            self.smtp_password_entry.delete(0, tk.END)
            self.smtp_password_entry.insert(0, "********")
        except Exception as e:
            logging.error(f"Failed to save email configuration: {str(e)}")
            messagebox.showerror("Error", f"Failed to save email configuration: {str(e)}")

    def _test_email(self):
        smtp_email = self.smtp_email_entry.get()
        smtp_password = self.smtp_password_entry.get()
        smtp_server = self.smtp_server_entry.get()
        smtp_port = self.smtp_port_entry.get()

        if not smtp_email:
            messagebox.showerror("Error", "Please fill in the SMTP email field")
            return

        if smtp_password == "********":
            config = self.db.get_email_config()
            if config and config[1] == smtp_email and isinstance(config[2], str):
                smtp_password = config[2]
            else:
                messagebox.showerror("Error", "Please enter a valid SMTP password")
                return

        try:
            # Validate port
            if not smtp_port.isdigit():
                raise ValueError("SMTP port must be a number")

            # Create test email
            import smtplib
            from email.mime.multipart import MIMEMultipart
            from email.mime.text import MIMEText

            msg = MIMEMultipart()
            msg['From'] = smtp_email
            msg['To'] = smtp_email
            msg['Subject'] = "CRM Email Configuration Test"
            msg.attach(MIMEText("This is a test email from the CRM system.", 'plain'))

            # Send email
            with smtplib.SMTP_SSL(smtp_server, int(smtp_port)) as server:
                server.login(smtp_email, smtp_password)
                server.sendmail(smtp_email, smtp_email, msg.as_string())

            messagebox.showinfo("Success", "Test email sent successfully!")
        except Exception as e:
            messagebox.showerror("Error", f"Failed to send test email: {str(e)}")

    def configure_styles(self):
        pass  # No ttk styles needed since buttons use tk.Button
