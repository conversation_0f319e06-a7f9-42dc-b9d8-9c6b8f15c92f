#!/usr/bin/env python3
"""
Simple test to check database structure and data
"""
import sqlite3
import os

def test_database():
    """Test database structure and data"""
    db_path = os.path.join(os.getenv('APPDATA', ''), 'CRM_System', 'crm_database.db')
    
    if not os.path.exists(db_path):
        print(f"Database not found at: {db_path}")
        return
    
    print(f"Database found at: {db_path}")
    
    try:
        conn = sqlite3.connect(db_path)
        c = conn.cursor()
        
        # Check customers table structure
        c.execute("PRAGMA table_info(customers)")
        customers_columns = c.fetchall()
        print("\nCustomers table columns:")
        for col in customers_columns:
            print(f"  {col[1]} ({col[2]})")
        
        # Check billing table structure
        c.execute("PRAGMA table_info(billing)")
        billing_columns = c.fetchall()
        print("\nBilling table columns:")
        for col in billing_columns:
            print(f"  {col[1]} ({col[2]})")
        
        # Get sample data
        c.execute("SELECT COUNT(*) FROM customers")
        customer_count = c.fetchone()[0]
        print(f"\nTotal customers: {customer_count}")
        
        c.execute("SELECT COUNT(*) FROM billing")
        billing_count = c.fetchone()[0]
        print(f"Total billing records: {billing_count}")
        
        # Show some sample customers with their outstanding amounts
        c.execute('''
            SELECT c.id, c.user_name, c.name, c.outstanding_amount, c.credit_balance
            FROM customers c
            LIMIT 5
        ''')
        customers = c.fetchall()
        
        print("\nSample customers:")
        print("ID | User Name | Name | Outstanding | Credit")
        print("-" * 50)
        for customer in customers:
            customer_id, user_name, name, outstanding, credit = customer
            outstanding = outstanding or 0.0
            credit = credit or 0.0
            print(f"{customer_id:2d} | {user_name:9s} | {name:4s} | {outstanding:11.2f} | {credit:6.2f}")
        
        conn.close()
        
    except Exception as e:
        print(f"Error: {e}")

if __name__ == "__main__":
    test_database()
