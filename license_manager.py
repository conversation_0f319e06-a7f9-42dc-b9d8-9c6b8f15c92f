import gspread
from oauth2client.service_account import ServiceAccountCredentials
import json
import os
import logging
from typing import Optional, Tuple, Callable
from datetime import datetime, timedelta
import hashlib
from cryptography.fernet import Fernet
import time
import pickle
import platform
import subprocess
import uuid
from tkinter import messagebox, simpledialog
import tkinter as tk
from tkinter import ttk
import asyncio
import threading

class LicenseManager:
    def __init__(self, on_register_again: Optional[Callable] = None):
        self.license_key = None
        self.license_valid = False
        self.license_expiry = None
        self.last_login = None
        self.last_reminder = None  # Track the last time the reminder was shown
        self.on_register_again = on_register_again  # FIXED: Corrected typo from 'on_register_ington'
        self.app_data_dir = os.path.join(os.getenv('APPDATA', ''), 'CRM_System')
        self.local_license_path = os.path.join(self.app_data_dir, 'user_license.enc')
        self.license_cache_path = os.path.join(self.app_data_dir, 'license_cache.dat')
        self.attempts_log_path = os.path.join(self.app_data_dir, 'attempts.log')
        self.encryption_key_path = os.path.join(self.app_data_dir, 'key.key')
        self.trial_file = os.path.join(self.app_data_dir, 'trial.key')
        self.license_file_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'license.key')
        
        # Configuration
        self.max_attempts = 5
        self.lockout_time = 300  # 5 minutes in seconds
        self.offline_grace_period = 7  # Days allowed for offline use
        self.trial_days = 7  # Trial period
        self.full_version_days = 365  # Full version license duration
        self.reminder_interval = 14400  # 4 hours in seconds
        self.secret_salt = "YourSecretSalt123!"  # MUST match generate_license.py
        self.default_keys = ["76C4EDB3C004BA89", "B5E9F3A2C8D701E6"]  # Default keys
        
        # Color scheme
        self.COLORS = {
            'background': '#F8F9FA',
            'card_bg': '#FFFFFF',
            'primary_accent': '#4A6FA5',
            'secondary_accent': '#6C8FC7',
            'text_primary': '#2D3748',
            'text_secondary': '#718096',
            'button_start': '#4A6FA5',
            'button_end': '#3A5A8C',
            'transparent': 'SystemTransparent',
            'warning': '#E53E3E',
            'input_bg': '#EDF2F7',
            'border': '#E2E8F0',
            'spinner': '#4A6FA5'
        }
        
        # Ensure app directory exists
        os.makedirs(self.app_data_dir, exist_ok=True)
        
        # Initialize encryption
        self._init_encryption()
        # Load existing license data
        self.check_local_license()

        # Initialize asyncio event loop for async operations
        self.loop = asyncio.new_event_loop()
        threading.Thread(target=self._run_event_loop, daemon=True).start()

    def _run_event_loop(self):
        """Run asyncio event loop in a separate thread"""
        asyncio.set_event_loop(self.loop)
        self.loop.run_forever()

    def _init_encryption(self):
        """Initialize encryption key for local storage"""
        if not os.path.exists(self.encryption_key_path):
            key = Fernet.generate_key()
            with open(self.encryption_key_path, 'wb') as key_file:
                key_file.write(key)
        
        with open(self.encryption_key_path, 'rb') as key_file:
            self.fernet = Fernet(key_file.read())

    def _encrypt_data(self, data: dict) -> bytes:
        """Encrypt sensitive data"""
        return self.fernet.encrypt(json.dumps(data).encode())

    def _decrypt_data(self, encrypted_data: bytes) -> dict:
        """Decrypt sensitive data"""
        return json.loads(self.fernet.decrypt(encrypted_data).decode())

    def _check_rate_limit(self) -> bool:
        """Check if too many failed attempts have occurred"""
        try:
            if not os.path.exists(self.attempts_log_path):
                return True
            
            with open(self.attempts_log_path, 'r') as f:
                attempts = f.readlines()
                recent_failures = [a for a in attempts if float(a.split(':')[0]) > time.time() - self.lockout_time]
                
                if len(recent_failures) >= self.max_attempts:
                    return False
        except Exception as e:
            logging.error(f"Rate limit check error: {str(e)}")
        
        return True

    def _log_failed_attempt(self):
        """Log a failed license attempt"""
        try:
            with open(self.attempts_log_path, 'a') as f:
                f.write(f"{time.time()}:failed\n")
        except Exception as e:
            logging.error(f"Failed to log attempt: {str(e)}")

    def _clear_attempts_log(self):
        """Clear old failed attempts"""
        try:
            if os.path.exists(self.attempts_log_path):
                os.remove(self.attempts_log_path)
        except Exception as e:
            logging.error(f"Failed to clear attempts log: {str(e)}")

    def get_hardware_fingerprint(self):
        """Generate a unique hardware identifier"""
        try:
            # Windows-specific hardware info
            if platform.system() == 'Windows':
                try:
                    disk_serial = subprocess.check_output(
                        "wmic diskdrive get serialnumber", 
                        shell=True, 
                        stderr=subprocess.DEVNULL
                    ).decode().split('\n')[1].strip()
                except:
                    disk_serial = "unknown"
            else:
                disk_serial = "unknown"

            # Cross-platform system info
            system_info = {
                'machine': platform.machine(),
                'processor': platform.processor(),
                'node': platform.node(),
                'disk_serial': disk_serial,
                'mac': ':'.join(['{:02x}'.format((uuid.getnode() >> elements) & 0xff) 
                       for elements in range(5, -1, -1)])
            }
            
            return hashlib.sha256(str(system_info).encode()).hexdigest()[:12]
        except Exception as e:
            logging.error(f"Hardware fingerprint error: {str(e)}")
            return "unknown"

    def setup_online_verify(self):
        """Initialize Google Sheets connection"""
        try:
            scope = ['https://spreadsheets.google.com/feeds',
                    'https://www.googleapis.com/auth/drive']
            
            creds_paths = [
                os.path.join(self.app_data_dir, 'google_auth.json'),
                os.path.join(os.path.dirname(__file__), 'google_auth.json'),
                r'D:\CRM_System\google_auth.json'
            ]
            
            creds_path = None
            for path in creds_paths:
                if os.path.exists(path):
                    creds_path = path
                    break
            
            if not creds_path:
                logging.error("Google auth credentials not found")
                return None
            
            creds = ServiceAccountCredentials.from_json_keyfile_name(creds_path, scope)
            return gspread.authorize(creds)
        except Exception as e:
            logging.error(f"Google Sheets setup error: {str(e)}")
            return None

    async def verify_license_online_async(self, license_key: str) -> Tuple[bool, Optional[datetime]]:
        """Asynchronous version of online license verification"""
        try:
            gc = self.setup_online_verify()
            if not gc:
                logging.warning("Could not connect to Google Sheets")
                return (False, None)
            
            sheet = gc.open_by_url(
                "https://docs.google.com/spreadsheets/d/1eezyIIAbTf_AYyAeJ_JKvIgSGeTZdG17ksdTaUclfu4/edit#gid=0"
            ).sheet1
            
            records = sheet.get_all_records()
            
            for record in records:
                if record.get('LicenseKey', '').strip() == license_key.strip():
                    active = record.get('Active', '').strip().upper() == "YES"
                    expiry_str = record.get('ExpiryDate', '')
                    
                    expiry_date = None
                    if expiry_str:
                        try:
                            expiry_date = datetime.strptime(expiry_str, '%Y-%m-%d')
                            if datetime.now() > expiry_date:
                                active = False
                        except ValueError:
                            logging.warning(f"Invalid expiry date format: {expiry_str}")
                    
                    return (active, expiry_date)
            
            logging.warning(f"License key not found: {license_key}")
            return (False, None)
        except Exception as e:
            logging.error(f"Online verification error: {str(e)}")
            return (False, None)

    def verify_license_online(self, license_key: str) -> Tuple[bool, Optional[datetime]]:
        """Run async verification in sync context"""
        try:
            future = asyncio.run_coroutine_threadsafe(
                self.verify_license_online_async(license_key),
                self.loop
            )
            return future.result(timeout=10)  # Timeout after 10 seconds
        except asyncio.TimeoutError:
            logging.error("Online verification timed out")
            return (False, None)
        except Exception as e:
            logging.error(f"Online verification sync error: {str(e)}")
            return (False, None)

    def check_local_license_cache(self) -> Optional[dict]:
        """Check for cached license validation"""
        try:
            if os.path.exists(self.license_cache_path):
                with open(self.license_cache_path, 'rb') as f:
                    cache = pickle.load(f)
                    
                    # Check if cache is still valid
                    if cache.get('valid_until', datetime.min) > datetime.now():
                        return cache
        except Exception as e:
            logging.error(f"Error reading license cache: {str(e)}")
        return None

    def save_license_cache(self, license_key: str, expiry_date: Optional[datetime], valid_days: int = 7):
        """Cache license validation for offline use"""
        try:
            cache_data = {
                'license_key': license_key,
                'valid_until': datetime.now() + timedelta(days=valid_days),
                'expiry_date': expiry_date,
                'last_online_verification': datetime.now(),
                'hwid': self.get_hardware_fingerprint()
            }
            
            with open(self.license_cache_path, 'wb') as f:
                pickle.dump(cache_data, f)
        except Exception as e:
            logging.error(f"Error saving license cache: {str(e)}")

    def validate_license_file(self, license_path):
        """Validate the license file with all new checks"""
        try:
            with open(license_path, 'r') as f:
                license = json.load(f)
            
            # Backward compatibility with old license format
            if isinstance(license, str):
                license_key = license.strip()
                if ':' in license_key:
                    license_key, hwid = license_key.split(':')
                    current_hwid = self.get_hardware_fingerprint()
                    if hwid != current_hwid:
                        return False, "License not valid for this hardware"
                
                if license_key in self.default_keys:
                    return True, "Valid legacy license"
                return False, "Invalid legacy license key"
            
            # New license format validation
            if 'data' not in license or 'key' not in license:
                return False, "Invalid license format"
            
            # Check signature
            signature_data = f"{license['key']}{license['data']['created']}{self.secret_salt}"
            expected_sig = hashlib.md5(signature_data.encode()).hexdigest()
            if license['data'].get('signature') != expected_sig:
                return False, "Invalid license signature"
            
            # Check expiration
            if 'expiry' in license['data']:
                try:
                    expiry_date = datetime.strptime(license['data']['expiry'], "%Y-%m-%d")
                    if datetime.now() > expiry_date:
                        return False, f"License expired on {license['data']['expiry']}"
                except:
                    return False, "Invalid expiration date format"
            
            # Check hardware binding
            if 'hwid' in license['data'] and license['data']['hwid']:
                current_hwid = self.get_hardware_fingerprint()
                if license['data']['hwid'] != current_hwid:
                    return False, "License not valid for this hardware"
            
            # Check activation limits
            activations_used = license['data'].get('activations_used', 0)
            activations_allowed = license['data'].get('activations_allowed', 1)
            if activations_used >= activations_allowed:
                return False, "Maximum activations reached"
            
            # Update activation count
            license['data']['activations_used'] = activations_used + 1
            with open(license_path, 'w') as f:
                json.dump(license, f, indent=2)
            
            return True, "License valid"
        
        except Exception as e:
            return False, f"License validation error: {str(e)}"

    def check_local_license(self) -> Optional[dict]:
        """Check for locally stored license, expiry date, last login, and last reminder"""
        try:
            if os.path.exists(self.local_license_path):
                with open(self.local_license_path, 'rb') as f:
                    encrypted_data = f.read()
                    data = self._decrypt_data(encrypted_data)
                    self.license_key = data.get('license_key')
                    if 'expiry_date' in data:
                        self.license_expiry = datetime.strptime(data['expiry_date'], "%Y-%m-%d")
                    if 'last_login' in data:
                        self.last_login = datetime.strptime(data['last_login'], "%Y-%m-%d %H:%M:%S")
                    if 'last_reminder' in data:
                        self.last_reminder = datetime.strptime(data['last_reminder'], "%Y-%m-%d %H:%M:%S")
                    return data
        except Exception as e:
            logging.error(f"Error reading local license: {str(e)}")
        return None

    def save_license_locally(self, license_key: str, expiry_date: Optional[datetime] = None, 
                           last_login: Optional[datetime] = None, last_reminder: Optional[datetime] = None):
        """Store license key, expiry date, last login, and last reminder locally with encryption"""
        try:
            data = {'license_key': license_key}
            if expiry_date:
                data['expiry_date'] = expiry_date.strftime("%Y-%m-%d")
            if last_login:
                data['last_login'] = last_login.strftime("%Y-%m-%d %H:%M:%S")
            if last_reminder:
                data['last_reminder'] = last_reminder.strftime("%Y-%m-%d %H:%M:%S")
            encrypted_data = self._encrypt_data(data)
            with open(self.local_license_path, 'wb') as f:
                f.write(encrypted_data)
        except Exception as e:
            logging.error(f"Error saving license: {str(e)}")

    def _show_loading_indicator(self, root, callback):
        """Show a rotating loading indicator during async operations"""
        dialog = tk.Toplevel(root)
        dialog.title("Validating License")
        dialog.configure(bg=self.COLORS['background'])
        dialog.transient(root)
        dialog.grab_set()
        
        dialog_width = 200
        dialog_height = 200
        screen_width = root.winfo_screenwidth()
        screen_height = root.winfo_screenheight()
        x = (screen_width - dialog_width) // 2
        y = (screen_height - dialog_height) // 2
        dialog.geometry(f"{dialog_width}x{dialog_height}+{x}+{y}")

        frame = tk.Frame(dialog, bg=self.COLORS['card_bg'])
        frame.pack(fill="both", expand=True)

        canvas = tk.Canvas(frame, width=100, height=100, bg=self.COLORS['card_bg'], highlightthickness=0)
        canvas.pack(pady=20)

        tk.Label(
            frame,
            text="Validating license...",
            font=("Helvetica", 12),
            fg=self.COLORS['text_primary'],
            bg=self.COLORS['card_bg']
        ).pack()

        # Create rotating arc
        angle = 0
        arc = canvas.create_arc(
            20, 20, 80, 80,
            start=0, extent=90,
            outline=self.COLORS['spinner'],
            width=4,
            style="arc"
        )

        def rotate():
            nonlocal angle
            if dialog.winfo_exists():
                angle = (angle + 10) % 360
                canvas.itemconfig(arc, start=angle)
                dialog.after(50, rotate)

        dialog.after(50, rotate)

        def check_callback():
            if dialog.winfo_exists():
                try:
                    if callback():
                        dialog.destroy()
                    else:
                        dialog.after(100, check_callback)
                except Exception as e:
                    logging.error(f"Error in loading callback: {str(e)}")
                    dialog.destroy()

        dialog.after(100, check_callback)
        return dialog

    def validate_license(self, license_key: str, root=None) -> bool:
        """Validate license with loading indicator and optimized checks"""
        if not self._check_rate_limit():
            if root:
                self._show_error_message("Too many failed attempts. Please try again later.", root)
            logging.error("Too many failed attempts. Please try again later.")
            return False
        
        # Trim and normalize the license key
        license_key = license_key.strip().upper()
        
        # Check local and cached licenses first to avoid network delay
        cache = self.check_local_license_cache()
        if cache and cache.get('license_key') == license_key:
            if cache.get('hwid') and cache['hwid'] != self.get_hardware_fingerprint():
                logging.warning("Cached license not valid for this hardware")
                self._log_failed_attempt()
                if root:
                    self._show_error_message("Cached license not valid for this hardware.", root)
                return False
                
            if cache.get('expiry_date') and datetime.now() > cache['expiry_date']:
                logging.warning("Cached license has expired")
                self._log_failed_attempt()
                if root:
                    self._show_error_message("Cached license has expired.", root)
                return False
            self.license_key = license_key
            self.license_expiry = cache.get('expiry_date')
            if root:
                self._show_success_message("Valid license found in cache.", root)
            return True
        
        local_data = self.check_local_license()
        if local_data and local_data.get('license_key') == license_key:
            if self.license_expiry and datetime.now() > self.license_expiry:
                logging.warning("Local license has expired")
                self._log_failed_attempt()
                if root:
                    self._show_error_message("Local license has expired.", root)
                return False
            self.license_key = license_key
            if root:
                self._show_success_message("Valid license found locally.", root)
            return True
        
        if license_key in self.default_keys:
            expiry_date = datetime.now() + timedelta(days=self.full_version_days)
            self.save_license_locally(license_key, expiry_date, datetime.now())
            self._clear_attempts_log()
            self.license_key = license_key
            self.license_expiry = expiry_date
            if root:
                self._show_success_message(f"Valid default license. Valid until {expiry_date.strftime('%Y-%m-%d')}.", root)
            return True
        
        # Show loading indicator for online verification
        validation_complete = [False]
        validation_result = [False]
        expiry_date_result = [None]

        def online_validation():
            nonlocal validation_result, expiry_date_result
            online_valid, expiry_date = self.verify_license_online(license_key)
            if online_valid:
                self.save_license_locally(license_key, expiry_date, datetime.now())
                self.save_license_cache(license_key, expiry_date, self.offline_grace_period)
                self._clear_attempts_log()
                self.license_key = license_key
                self.license_expiry = expiry_date
                validation_result[0] = True
                expiry_date_result[0] = expiry_date
            validation_complete[0] = True
            return validation_result[0]

        if root:
            self._show_loading_indicator(root, lambda: validation_complete[0])
        
        # Perform online validation
        if online_validation():
            if root:
                self._show_success_message(
                    f"License validated online. Valid until {expiry_date_result[0].strftime('%Y-%m-%d') if expiry_date_result[0] else 'unknown'}.",
                    root
                )
            return True
        
        # Check if this is a valid license file
        if os.path.exists(self.license_file_path):
            with open(self.license_file_path, 'r') as f:
                try:
                    license_data = json.load(f)
                    if license_data.get('key') == license_key:
                        valid, message = self.validate_license_file(self.license_file_path)
                        if valid:
                            self.license_key = license_key
                            if 'data' in license_data and 'expiry' in license_data['data']:
                                self.license_expiry = datetime.strptime(license_data['data']['expiry'], "%Y-%m-%d")
                            if root:
                                self._show_success_message(f"Valid license file found. {message}", root)
                            return True
                        else:
                            if root:
                                self._show_error_message(f"License file validation failed: {message}", root)
                except Exception as e:
                    logging.error(f"Error validating license file: {str(e)}")
                    if root:
                        self._show_error_message(f"Error validating license file: {str(e)}", root)
        
        self._log_failed_attempt()
        if root:
            self._show_error_message("Invalid license key.", root)
        return False

    def check_trial_period(self) -> bool:
        """Check if trial period is active"""
        if os.path.exists(self.trial_file):
            try:
                with open(self.trial_file, 'r') as f:
                    trial_data = json.load(f)
                
                install_date = datetime.strptime(trial_data['install_date'], "%Y-%m-%d")
                trial_days = trial_data['trial_days']
                days_used = (datetime.now() - install_date).days
                
                if days_used >= trial_days:
                    logging.error(f"Trial expired {days_used - trial_days} days ago")
                    if os.path.exists(self.trial_file):
                        os.remove(self.trial_file)
                    return False
                
                logging.info(f"Trial period: {trial_days - days_used} days remaining")
                return True
            except Exception as e:
                logging.error(f"Trial check error: {str(e)}")
                if os.path.exists(self.trial_file):
                    os.remove(self.trial_file)
                return False
        return False

    def start_trial(self):
        """Start a new trial period"""
        trial_data = {
            'install_date': datetime.now().strftime("%Y-%m-%d"),
            'trial_days': self.trial_days,
            'first_run': True
        }
        with open(self.trial_file, 'w') as f:
            json.dump(trial_data, f)
        logging.info(f"New {self.trial_days}-day trial period started")

    def select_version(self, root=None) -> bool:
        """Show dialog for selecting trial or full version"""
        if root:
            dialog = tk.Toplevel(root)
            dialog.title("Select Version")
            dialog.configure(bg=self.COLORS['background'])
            dialog.transient(root)
            dialog.grab_set()
            
            # Center the dialog
            dialog_width = 400
            dialog_height = 200
            screen_width = root.winfo_screenwidth()
            screen_height = root.winfo_screenheight()
            x = (screen_width - dialog_width) // 2
            y = (screen_height - dialog_height) // 2
            dialog.geometry(f"{dialog_width}x{dialog_height}+{x}+{y}")
            
            frame = tk.Frame(dialog, bg=self.COLORS['card_bg'], padx=20, pady=20)
            frame.pack(fill="both", expand=True)
            
            tk.Label(
                frame,
                text="Please select version to use:",
                font=("Helvetica", 12),
                fg=self.COLORS['text_primary'],
                bg=self.COLORS['card_bg']
            ).pack(pady=(0, 20))
            
            version_selected = [None]
            
            def select_trial():
                version_selected[0] = "trial"
                dialog.destroy()
            
            def select_full():
                version_selected[0] = "full"
                dialog.destroy()
            
            tk.Button(
                frame,
                text=f"Trial Version ({self.trial_days} days)",
                font=("Helvetica", 12, "bold"),
                bg=self.COLORS['primary_accent'],
                fg="#FFFFFF",
                activebackground=self.COLORS['secondary_accent'],
                activeforeground="#FFFFFF",
                width=20,
                command=select_trial
            ).pack(pady=5)
            
            tk.Button(
                frame,
                text="Full Version",
                font=("Helvetica", 12, "bold"),
                bg=self.COLORS['primary_accent'],
                fg="#FFFFFF",
                activebackground=self.COLORS['secondary_accent'],
                activeforeground="#FFFFFF",
                width=20,
                command=select_full
            ).pack(pady=5)
            
            root.wait_window(dialog)
            return version_selected[0]
        return None

    def check_expiry_reminder(self, root=None) -> bool:
        """Check if a reminder should be shown when license has <10 days remaining, but only every 4 hours"""
        if not root:
            return False

        def check_and_show_reminder():
            # Check if enough time has passed since the last reminder (4 hours = 14400 seconds)
            current_time = datetime.now()
            if self.last_reminder:
                time_since_last_reminder = (current_time - self.last_reminder).total_seconds()
                if time_since_last_reminder < self.reminder_interval:  # Less than 4 hours
                    logging.info(f"Skipping reminder, last shown {(time_since_last_reminder/3600):.2f} hours ago")
                    # Schedule the next check
                    time_to_next_check = self.reminder_interval - time_since_last_reminder
                    root.after(int(time_to_next_check * 1000), check_and_show_reminder)
                    return False

            # Check full version remaining days
            if self.license_expiry:
                days_remaining = (self.license_expiry - datetime.now()).days
                if 0 < days_remaining <= 10:
                    self._show_expiry_reminder_dialog(root, days_remaining, "Full Version")
                    self.last_reminder = datetime.now()
                    self.save_license_locally(self.license_key, self.license_expiry, self.last_login, self.last_reminder)
                    # Schedule the next check in 4 hours
                    root.after(self.reminder_interval * 1000, check_and_show_reminder)
                    return True

            # Check trial remaining days
            if os.path.exists(self.trial_file):
                try:
                    with open(self.trial_file, 'r') as f:
                        trial_data = json.load(f)
                    install_date = datetime.strptime(trial_data['install_date'], "%Y-%m-%d")
                    trial_days = trial_data['trial_days']
                    days_used = (datetime.now() - install_date).days
                    days_remaining = trial_days - days_used
                    if 0 < days_remaining <= 10:
                        self._show_expiry_reminder_dialog(root, days_remaining, "Trial")
                        self.last_reminder = datetime.now()
                        self.save_license_locally(self.license_key, self.license_expiry, self.last_login, self.last_reminder)
                        # Schedule the next check in 4 hours
                        root.after(self.reminder_interval * 1000, check_and_show_reminder)
                        return True
                except Exception as e:
                    logging.error(f"Error checking trial expiry for reminder: {str(e)}")
            
            # If no reminder is needed, schedule the next check in 4 hours
            root.after(self.reminder_interval * 1000, check_and_show_reminder)
            return False

        # Schedule the first check after 1 minute (60000 milliseconds)
        root.after(60000, check_and_show_reminder)
        return True

    def _show_expiry_reminder_dialog(self, root=None, days_remaining: int = 0, version_type: str = "Trial"):
        """Show a styled reminder dialog for license expiry"""
        if not root:
            return

        dialog = tk.Toplevel(root)
        dialog.title(f"{version_type} License Expiry Reminder")
        dialog.configure(bg=self.COLORS['background'])
        dialog.transient(root)
        dialog.grab_set()
        
        # Make dialog resizable and set minimum size
        dialog_width = 450
        dialog_height = 220
        screen_width = root.winfo_screenwidth()
        screen_height = root.winfo_screenheight()
        x = (screen_width - dialog_width) // 2
        y = (screen_height - dialog_height) // 2
        dialog.geometry(f"{dialog_width}x{dialog_height}+{x}+{y}")
        dialog.minsize(dialog_width, dialog_height)
        
        # Automatically close after 1 minute (60000 milliseconds) if no interaction
        def auto_close():
            if dialog.winfo_exists():
                dialog.destroy()
                logging.info("Expiry reminder dialog auto-closed after 1 minute, user continues working")
        
        dialog.after(60000, auto_close)
        
        # Add close button in title bar
        def on_close():
            dialog.destroy()
            # Do not trigger logout; user should continue working
            logging.info("Expiry reminder dialog closed, user continues working")
        
        dialog.protocol("WM_DELETE_WINDOW", on_close)
        
        # Main frame
        frame = tk.Frame(dialog, bg=self.COLORS['card_bg'], padx=20, pady=20)
        frame.pack(fill="both", expand=True)
        
        # Message label
        message = (f"Your {version_type} license will expire in {days_remaining} day{'s' if days_remaining != 1 else ''}.\n"
                   "Please register again to continue using the application without interruption.")
        
        tk.Label(
            frame,
            text=message,
            font=("Helvetica", 12),
            fg=self.COLORS['text_primary'],
            bg=self.COLORS['card_bg'],
            wraplength=dialog_width-40,
            justify="left"
        ).pack(pady=(0, 20), anchor="w")
        
        # Button frame
        button_frame = tk.Frame(frame, bg=self.COLORS['card_bg'])
        button_frame.pack(fill="x", pady=(10, 0))
        
        action_taken = [None]
        
        def remind_later():
            action_taken[0] = "remind_later"
            dialog.destroy()
            logging.info("User chose to be reminded later, continuing work")
        
        def register_again():
            action_taken[0] = "register_again"
            dialog.destroy()
            try:
                if self.on_register_again:
                    self.on_register_again()  # Navigate to login page
                    logging.info("Navigated to login page for re-registration")
                
                root.after(500, lambda: prompt_for_key())
            except Exception as e:
                logging.error(f"Error during re-registration navigation: {str(e)}")
                self._show_error_message(f"Re-registration Error: {str(e)}", root)
        
        def prompt_for_key():
            try:
                if self._prompt_for_default_key(root):
                    if os.path.exists(self.trial_file):
                        self.start_trial()
                    else:
                        self.license_expiry = datetime.now() + timedelta(days=self.full_version_days)
                        self.save_license_locally(self.license_key, self.license_expiry, datetime.now())
                    
                    self._show_success_message(
                        f"{version_type} license renewed. Valid until "
                        f"{self.license_expiry.strftime('%Y-%m-%d') if self.license_expiry else (datetime.now() + timedelta(days=self.trial_days)).strftime('%Y-%m-%d')}.",
                        root
                    )
                else:
                    logging.warning("License key prompt failed or was cancelled")
                    self._show_error_message("Failed to validate new license key. Please try again.", root)
            except Exception as e:
                logging.error(f"Error during license key prompt: {str(e)}")
                self._show_error_message(f"License Prompt Error: {str(e)}", root)
        
        # Remind Later button (left aligned)
        tk.Button(
            button_frame,
            text="Remind Me Later",
            font=("Helvetica", 11, "bold"),
            bg=self.COLORS['primary_accent'],
            fg="#FFFFFF",
            activebackground=self.COLORS['secondary_accent'],
            activeforeground="#FFFFFF",
            width=15,
            command=remind_later
        ).pack(side="left", padx=5)
        
        # Register button (right aligned)
        tk.Button(
            button_frame,
            text="Register Again",
            font=("Helvetica", 11, "bold"),
            bg=self.COLORS['warning'],
            fg="#FFFFFF",
            activebackground='#C53030',
            activeforeground="#FFFFFF",
            width=15,
            command=register_again
        ).pack(side="right", padx=5)
        
        # Center the buttons by adding an expanding frame
        tk.Frame(button_frame, bg=self.COLORS['card_bg']).pack(side="left", expand=True, fill="x")
        
        root.wait_window(dialog)

    def enforce_license(self, root=None) -> bool:
        """Main license enforcement with loading indicator"""
        # Check rate limiting first
        if not self._check_rate_limit():
            self._show_error_message("Too many failed attempts. Please try again later.", root)
            return False
        
        # Check if a valid license exists
        if self._three_step_verification():
            # Check if the license has expired
            if self.license_expiry and datetime.now() > self.license_expiry:
                self._show_expiry_reminder_dialog(root, 0, "Full Version")
                # After the dialog, check if the license is still expired
                if self.license_expiry and datetime.now() > self.license_expiry:
                    self._show_error_message("License has expired. Application will now exit.", root)
                    return False
            # Update last login time
            self.last_login = datetime.now()
            self.save_license_locally(self.license_key, self.license_expiry, self.last_login)
            # Check for expiry reminder after verifying license
            self.check_expiry_reminder(root)
            self._show_success_message("Valid license found.", root)
            self.license_valid = True
            return True

        # Check if trial file exists and is valid
        trial_active = False
        days_remaining = 0
        if os.path.exists(self.trial_file):
            try:
                with open(self.trial_file, 'r') as f:
                    trial_data = json.load(f)
                install_date = datetime.strptime(trial_data['install_date'], "%Y-%m-%d")
                trial_days = trial_data['trial_days']
                days_used = (datetime.now() - install_date).days
                
                if days_used < trial_days:
                    trial_active = True
                    days_remaining = trial_days - days_used
                else:
                    # Show expiry dialog for trial
                    self._show_expiry_reminder_dialog(root, 0, "Trial")
                    # After the dialog, check if trial is still expired
                    if os.path.exists(self.trial_file):
                        with open(self.trial_file, 'r') as f:
                            trial_data = json.load(f)
                        install_date = datetime.strptime(trial_data['install_date'], "%Y-%m-%d")
                        trial_days = trial_data['trial_days']
                        days_used = (datetime.now() - install_date).days
                        if days_used >= trial_days:
                            self._show_error_message("Trial has expired. Application will now exit.", root)
                            return False
                    # Remove expired trial file
                    os.remove(self.trial_file)
            except Exception as e:
                logging.error(f"Trial file read error: {str(e)}")
                # Remove corrupted trial file
                if os.path.exists(self.trial_file):
                    os.remove(self.trial_file)

        # If trial is active, confirm with user
        if trial_active:
            # Update last login time
            self.last_login = datetime.now()
            self.save_license_locally(self.license_key, None, self.last_login)
            # Check for expiry reminder
            self.check_expiry_reminder(root)
            
            dialog = tk.Toplevel(root)
            dialog.title("Trial Active")
            dialog.configure(bg=self.COLORS['background'])
            dialog.transient(root)
            dialog.grab_set()
            
            dialog_width = 400
            dialog_height = 200
            screen_width = root.winfo_screenwidth()
            screen_height = root.winfo_screenheight()
            x = (screen_width - dialog_width) // 2
            y = (screen_height - dialog_height) // 2
            dialog.geometry(f"{dialog_width}x{dialog_height}+{x}+{y}")
            
            frame = tk.Frame(dialog, bg=self.COLORS['card_bg'], padx=20, pady=20)
            frame.pack(fill="both", expand=True)
            
            tk.Label(
                frame,
                text=f"Trial version active. {days_remaining} day{'s' if days_remaining != 1 else ''} remaining.\nDo you want to continue with the trial?",
                font=("Helvetica", 12),
                fg=self.COLORS['text_primary'],
                bg=self.COLORS['card_bg'],
                wraplength=350
            ).pack(pady=(0, 20))
            
            continue_trial = [True]
            
            def continue_with_trial():
                continue_trial[0] = True
                dialog.destroy()
            
            def choose_new_version():
                continue_trial[0] = False
                dialog.destroy()
            
            tk.Button(
                frame,
                text="Continue Trial",
                font=("Helvetica", 12, "bold"),
                bg=self.COLORS['primary_accent'],
                fg="#FFFFFF",
                activebackground=self.COLORS['secondary_accent'],
                activeforeground="#FFFFFF",
                width=15,
                command=continue_with_trial
            ).pack(side="left", padx=5)
            
            tk.Button(
                frame,
                text="Change Version",
                font=("Helvetica", 12, "bold"),
                bg=self.COLORS['warning'],
                fg="#FFFFFF",
                activebackground='#C53030',
                activeforeground="#FFFFFF",
                width=15,
                command=choose_new_version
            ).pack(side="left", padx=5)
            
            root.wait_window(dialog)
            
            if continue_trial[0]:
                self._show_success_message(f"Trial version active. {days_remaining} day{'s' if days_remaining != 1 else ''} remaining.", root)
                self.license_valid = True
                return True
            else:
                # Remove trial file to allow new selection
                if os.path.exists(self.trial_file):
                    os.remove(self.trial_file)

        # Show version selection dialog
        version = self.select_version(root)
        if not version:
            self._show_error_message("No version selected. Application will exit.", root)
            return False
            
        if version == "trial":
            self.start_trial()
            self.last_login = datetime.now()
            self.save_license_locally(None, None, self.last_login)
            self._show_success_message(f"Trial version started. {self.trial_days} days remaining.", root)
            self.license_valid = True
            return True
        elif version == "full":
            # Prompt for license key
            if not self._prompt_for_default_key(root):
                return False
            self.license_valid = True
            return True
        
        return False

    def _three_step_verification(self) -> bool:
        """Perform three-step verification with optimized order"""
        # Step 3: Check for cached license first
        if self._validate_cached_license():
            logging.info("License validated via cached license")
            return True
        
        # Step 2: Validate local license.key file
        if self._validate_license_file():
            logging.info("License validated via local license file")
            return True
        
        # Step 1: Validate against Google Sheet
        if self._validate_google_sheet():
            logging.info("License validated via Google Sheet")
            return True
        
        logging.warning("Three-step verification failed for all methods")
        return False

    def _validate_google_sheet(self, root=None) -> bool:
        """Validate license against Google Sheet with loading indicator"""
        try:
            # Check if we have a local license key first
            local_data = self.check_local_license()
            if not local_data or not local_data.get('license_key'):
                return False
                
            local_key = local_data.get('license_key')
            
            # Show loading indicator
            validation_complete = [False]
            validation_result = [False]
            expiry_date_result = [None]

            def online_validation():
                nonlocal validation_result, expiry_date_result
                online_valid, expiry_date = self.verify_license_online(local_key)
                if online_valid:
                    self.save_license_locally(local_key, expiry_date, datetime.now())
                    self.save_license_cache(local_key, expiry_date, self.offline_grace_period)
                    self._clear_attempts_log()
                    self.license_key = local_key
                    self.license_expiry = expiry_date
                    validation_result[0] = True
                    expiry_date_result[0] = expiry_date
                validation_complete[0] = True
                return validation_result[0]

            if root:
                self._show_loading_indicator(root, lambda: validation_complete[0])
            
            if online_validation():
                if root:
                    self._show_success_message(
                        f"License validated online. Valid until {expiry_date_result[0].strftime('%Y-%m-%d') if expiry_date_result[0] else 'unknown'}.",
                        root
                    )
                self.license_valid = True
                return True
        except Exception as e:
            logging.error(f"Google Sheet validation error: {str(e)}")
            if root:
                self._show_error_message(f"Google Sheet validation error: {str(e)}", root)
        return False

    def _validate_license_file(self) -> bool:
        """Validate local license.key file"""
        try:
            if not os.path.exists(self.license_file_path):
                logging.info("No license file found at %s", self.license_file_path)
                return False
                
            valid, message = self.validate_license_file(self.license_file_path)
            if valid:
                with open(self.license_file_path, 'r') as f:
                    license_data = json.load(f)
                    self.license_key = license_data.get('key')
                    if 'data' in license_data:
                        expiry_str = license_data['data'].get('expiry')
                        if expiry_str:
                            self.license_expiry = datetime.strptime(expiry_str, "%Y-%m-%d")
                    self.license_valid = True
                    return True
            logging.warning(f"License file validation failed: {message}")
        except Exception as e:
            logging.error(f"License file validation error: {str(e)}")
        return False

    def _validate_cached_license(self) -> bool:
        """Validate cached license"""
        cache = self.check_local_license_cache()
        if cache:
            # Validate hardware binding
            if cache.get('hwid') == self.get_hardware_fingerprint():
                self.license_key = cache['license_key']
                self.license_valid = True
                return True
        return False

    def _prompt_for_default_key(self, root=None) -> bool:
        """Prompt user for default key if all verifications fail"""
        if not root:
            logging.error("No root provided for license key prompt")
            return False

        attempts = 0
        while attempts < 3:
            try:
                dialog = tk.Toplevel(root)
                dialog.title("License Required")
                dialog.configure(bg=self.COLORS['background'])
                dialog.transient(root)
                dialog.grab_set()
                
                # Center the dialog
                dialog_width = 400
                dialog_height = 200
                screen_width = root.winfo_screenwidth()
                screen_height = root.winfo_screenheight()
                x = (screen_width - dialog_width) // 2
                y = (screen_height - dialog_height) // 2
                dialog.geometry(f"{dialog_width}x{dialog_height}+{x}+{y}")
                
                frame = tk.Frame(dialog, bg=self.COLORS['card_bg'], padx=20, pady=20)
                frame.pack(fill="both", expand=True)
                
                tk.Label(
                    frame,
                    text="Please enter your license key for Full Version:",
                    font=("Helvetica", 12),
                    fg=self.COLORS['text_primary'],
                    bg=self.COLORS['card_bg']
                ).pack(pady=(0, 10))
                
                license_entry = ttk.Entry(
                    frame,
                    font=("Helvetica", 12),
                    width=30,
                    style='Modern.TEntry'
                )
                license_entry.pack(fill="x", ipady=8)
                
                # Configure entry style to match login.py
                style = ttk.Style()
                style.configure('Modern.TEntry',
                    foreground=self.COLORS['text_primary'],
                    fieldbackground=self.COLORS['input_bg'],
                    borderwidth=1,
                    relief="flat",
                    padding=10
                )
                style.map('Modern.TEntry',
                    fieldbackground=[('active', self.COLORS['input_bg']), ('!disabled', self.COLORS['input_bg'])],
                    bordercolor=[('focus', self.COLORS['primary_accent']), ('!focus', self.COLORS['border'])],
                    lightcolor=[('focus', self.COLORS['primary_accent']), ('!focus', self.COLORS['border'])],
                    darkcolor=[('focus', self.COLORS['primary_accent']), ('!focus', self.COLORS['border'])]
                )
                
                button_frame = tk.Frame(frame, bg=self.COLORS['card_bg'])
                button_frame.pack(pady=20)
                
                license_key = [None]
                
                def submit():
                    license_key[0] = license_entry.get().strip()
                    dialog.destroy()
                
                tk.Button(
                    button_frame,
                    text="Submit",
                    font=("Helvetica", 12, "bold"),
                    bg=self.COLORS['primary_accent'],
                    fg="#FFFFFF",
                    activebackground=self.COLORS['secondary_accent'],
                    activeforeground="#FFFFFF",
                    width=10,
                    command=submit
                ).pack(side="left", padx=5)
                
                tk.Button(
                    button_frame,
                    text="Cancel",
                    font=("Helvetica", 12),
                    bg=self.COLORS['warning'],
                    fg="#FFFFFF",
                    activebackground='#C53030',
                    activeforeground="#FFFFFF",
                    width=10,
                    command=lambda: dialog.destroy()
                ).pack(side="left", padx=5)
                
                root.wait_window(dialog)
                license_key = license_key[0]
            except Exception as e:
                logging.error(f"Error creating license key dialog: {str(e)}")
                self._show_error_message(f"Dialog Error: {str(e)}", root)
                return False
            
            if not license_key:
                self._show_error_message("License key is required for Full Version", root)
                return False
            
            if self.validate_license(license_key, root):
                # On successful validation, set a 9-day expiry
                self.license_key = license_key
                self.last_login = datetime.now()
                if os.path.exists(self.trial_file):
                    self.start_trial()
                else:
                    self.license_expiry = datetime.now() + timedelta(days=self.full_version_days)
                    self.save_license_locally(license_key, self.license_expiry, self.last_login)
                self._show_success_message(
                    f"License accepted. Valid until {self.license_expiry.strftime('%Y-%m-%d') if self.license_expiry else (datetime.now() + timedelta(days=self.trial_days)).strftime('%Y-%m-%d')}.",
                    root
                )
                self._clear_attempts_log()
                return True
            else:
                attempts += 1
                remaining = 3 - attempts
                if remaining > 0:
                    self._show_error_message(f"Invalid license key. {remaining} attempts remaining.", root)
        
        self._show_error_message("Maximum attempts reached. Application will now exit.", root)
        return False

    def _show_error_message(self, message: str, root=None):
        """Show error message to user using the provided root with styled dialog"""
        if root:
            try:
                dialog = tk.Toplevel(root)
                dialog.title("Error")
                dialog.configure(bg=self.COLORS['background'])
                dialog.transient(root)
                dialog.grab_set()
                
                dialog_width = 400
                dialog_height = 150
                screen_width = root.winfo_screenwidth()
                screen_height = root.winfo_screenheight()
                x = (screen_width - dialog_width) // 2
                y = (screen_height - dialog_height) // 2
                dialog.geometry(f"{dialog_width}x{dialog_height}+{x}+{y}")
                
                frame = tk.Frame(dialog, bg=self.COLORS['card_bg'], padx=20, pady=20)
                frame.pack(fill="both", expand=True)
                
                tk.Label(
                    frame,
                    text=message,
                    font=("Helvetica", 12),
                    fg=self.COLORS['warning'],
                    bg=self.COLORS['card_bg'],
                    wraplength=350
                ).pack(pady=(0, 20))
                
                tk.Button(
                    frame,
                    text="OK",
                    font=("Helvetica", 12, "bold"),
                    bg=self.COLORS['primary_accent'],
                    fg="#FFFFFF",
                    activebackground=self.COLORS['secondary_accent'],
                    activeforeground="#FFFFFF",
                    width=10,
                    command=dialog.destroy
                ).pack()
                
                root.wait_window(dialog)
            except Exception as e:
                logging.error(f"Error displaying error message: {str(e)}")
        else:
            logging.error(message)

    def _show_success_message(self, message: str, root=None):
        """Show success message to user using the provided root with styled dialog"""
        if root:
            try:
                dialog = tk.Toplevel(root)
                dialog.title("Success")
                dialog.configure(bg=self.COLORS['background'])
                dialog.transient(root)
                dialog.grab_set()
                
                dialog_width = 400
                dialog_height = 150
                screen_width = root.winfo_screenwidth()
                screen_height = root.winfo_screenheight()
                x = (screen_width - dialog_width) // 2
                y = (screen_height - dialog_height) // 2
                dialog.geometry(f"{dialog_width}x{dialog_height}+{x}+{y}")
                
                frame = tk.Frame(dialog, bg=self.COLORS['card_bg'], padx=20, pady=20)
                frame.pack(fill="both", expand=True)
                
                tk.Label(
                    frame,
                    text=message,
                    font=("Helvetica", 12),
                    fg=self.COLORS['text_primary'],
                    bg=self.COLORS['card_bg'],
                    wraplength=350
                ).pack(pady=(0, 20))
                
                tk.Button(
                    frame,
                    text="OK",
                    font=("Helvetica", 12, "bold"),
                    bg=self.COLORS['primary_accent'],
                    fg="#FFFFFF",
                    activebackground=self.COLORS['secondary_accent'],
                    activeforeground="#FFFFFF",
                    width=10,
                    command=dialog.destroy
                ).pack()
                
                root.wait_window(dialog)
            except Exception as e:
                logging.error(f"Error displaying success message: {str(e)}")
        else:
            logging.info(message)

    def revoke_license(self):
        """Revoke current license both locally and online"""
        if not self.license_key:
            return False
        
        try:
            # Revoke online
            gc = self.setup_online_verify()
            if gc:
                sheet = gc.open_by_url(
                    "https://docs.google.com/spreadsheets/d/1eezyIIAbTf_AYyAeJ_JKvIgSGeTZdG17ksdTaUclfu4/edit#gid=0"
                ).sheet1
                
                cell = sheet.find(self.license_key)
                if cell:
                    sheet.update_cell(cell.row, 2, "NO")  # Update Active status
            
            # Remove local license files
            for path in [self.local_license_path, self.license_cache_path, self.trial_file]:
                if os.path.exists(path):
                    os.remove(path)
            
            self.license_key = None
            self.license_valid = False
            self.license_expiry = None
            self.last_login = None
            self.last_reminder = None
            
            # Stop the asyncio event loop
            if self.loop.is_running():
                self.loop.call_soon_threadsafe(self.loop.stop)
                self.loop.run_until_complete(self.loop.shutdown_asyncgens())
                self.loop.close()
                
            return True
        except Exception as e:
            logging.error(f"License revocation error: {str(e)}")
            return False
