import tkinter as tk
from tkinter import messagebox, filedialog
from database_utils import get_db_connection, execute_with_retry
import os
from datetime import datetime
from reportlab.lib.pagesizes import letter, landscape
from reportlab.lib import colors
from reportlab.platypus import SimpleDocTemplate, Table, TableStyle, Paragraph, Spacer
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib.units import inch
import platform
import subprocess
import threading
import logging
from typing import Optional, Tuple, Dict, List

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class PaymentReceipt:
    # Configurable constants (could be moved to config file)
    COMPANY_NAME = "SANI BROADBAND"
    CONTACT_INFO = "+92321-9550665 | +92321-9550666"
    RECEIPT_FOOTER = ("Make all checks payable to Sani Broadband Communication Pvt.<br/>"
                     "If you have any questions concerning this invoice, please visit our office / call us.")

    COLORS = {
        'primary': '#4CAF50',
        'secondary': '#3C3C3C',
        'success': '#388E3C',
        'danger': '#D32F2F',
        'warning': '#FBC02D',
        'light': '#BBDEFB',
        'background': '#E3F2FD',
        'text_primary': '#212121',
        'text_secondary': '#757575',
        'border': '#1976D2',
        'div_background': '#FFFFFF',
        'grid_color': '#B0BEC5'
    }

    def __init__(self, parent):
        self.parent = parent
        self._setup_logging()

    def _setup_logging(self):
        """Configure logging for receipt generation"""
        log_dir = os.path.join(os.getenv('APPDATA'), 'CRM_System', 'logs')
        os.makedirs(log_dir, exist_ok=True)
        logging.basicConfig(
            filename=os.path.join(log_dir, 'receipt_generation.log'),
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s'
        )
        # Ensure module-specific logger is used
        self.logger = logging.getLogger(__name__)

    def _center_window(self, window, width, height):
        """Center the given window on screen"""
        screen_width = window.winfo_screenwidth()
        screen_height = window.winfo_screenheight()
        x = (screen_width - width) // 2
        y = (screen_height - height) // 2
        window.geometry(f"{width}x{height}+{x}+{y}")

    def ask_for_receipt(self, customer_id: int, amount: float, payment_date: str, 
                       total_pending: float, outstanding_amount: float, bill_id: Optional[int] = None):
        """Show a dialog asking if user wants to generate a receipt"""
        try:
            self._validate_receipt_data(customer_id, amount, payment_date)
            
            dialog = tk.Toplevel(self.parent)
            dialog.title("Receipt Prompt")
            dialog.transient(self.parent)
            dialog.grab_set()
            dialog.configure(bg=self.COLORS['secondary'])
            dialog.resizable(False, False)
            
            self._center_window(dialog, 400, 200)

            main_frame = tk.Frame(dialog, bg=self.COLORS['secondary'], 
                                highlightbackground=self.COLORS['border'], 
                                highlightthickness=2, padx=20, pady=20)
            main_frame.pack(fill="both", expand=True)

            tk.Label(main_frame, text="Generate Bill Receipt", 
                    bg=self.COLORS['secondary'], 
                    font=("Segoe UI", 16, "bold"), 
                    fg="#FFFFFF").pack(anchor="center", pady=(0, 20))

            label_style = {"bg": self.COLORS['secondary'], 
                          "font": ("Segoe UI", 12), 
                          "fg": "#FFFFFF"}
            
            prompt_frame = tk.Frame(main_frame, bg=self.COLORS['secondary'])
            prompt_frame.pack(fill="x", pady=(0, 30))
            tk.Label(prompt_frame, 
                     text="Do you want to generate the bill receipt?", 
                     **label_style, 
                     anchor="center").pack(expand=True)

            def on_yes():
                # Run in background thread to prevent UI freezing
                threading.Thread(
                    target=self._generate_pdf_receipt,
                    args=(customer_id, amount, payment_date, 
                         total_pending, outstanding_amount, bill_id),
                    daemon=True
                ).start()
                dialog.destroy()

            button_frame = tk.Frame(main_frame, bg=self.COLORS['secondary'])
            button_frame.pack(anchor="center")
            
            buttons = [
                ("Yes", on_yes),
                ("No", dialog.destroy)
            ]
            
            for text, command in buttons:
                tk.Button(
                    button_frame, 
                    text=text, 
                    command=command, 
                    bg="#FFFFFF", 
                    fg="#000000",
                    font=("Segoe UI", 12, "bold"), 
                    relief=tk.FLAT, 
                    padx=20, 
                    pady=10,
                    activebackground="#FFFFFF", 
                    activeforeground="#000000"
                ).pack(side="left" if text == "Yes" else "right", padx=20)

        except ValueError as ve:
            messagebox.showerror("Validation Error", str(ve))
        except Exception as e:
            self.logger.error(f"Error in receipt prompt: {str(e)}")
            messagebox.showerror("Error", f"Failed to show receipt prompt: {str(e)}")

    def _validate_receipt_data(self, customer_id: int, amount: float, payment_date: str):
        """Validate receipt data before processing"""
        if not isinstance(customer_id, int) or customer_id <= 0:
            raise ValueError("Invalid customer ID")
        
        if not isinstance(amount, (int, float)) or amount <= 0:
            raise ValueError("Invalid payment amount")
        
        try:
            datetime.strptime(payment_date, '%Y-%m-%d')
        except ValueError:
            raise ValueError("Invalid payment date format. Use YYYY-MM-DD")

    def _get_customer_data(self, customer_id: int) -> Tuple[str, str, str, float]:
        """Retrieve customer data from database"""
        try:
            with get_db_connection() as conn:
                cursor = conn.cursor()
                cursor.execute('''SELECT c.name, c.phone, p.name, p.price 
                                 FROM customers c JOIN packages p ON c.package_id = p.id 
                                 WHERE c.id = ?''', (customer_id,))
                customer = cursor.fetchone()
                
                if not customer:
                    raise ValueError("Customer not found")
                
                return customer
        except Exception as e:
            self.logger.error(f"Database error fetching customer: {str(e)}")
            raise Exception("Failed to retrieve customer data from database")

    def _get_bill_data(self, bill_id: Optional[int], customer_id: int, payment_date: str) -> Dict:
        """Retrieve bill data from database"""
        try:
            with get_db_connection() as conn:
                cursor = conn.cursor()
                bill_data = {
                    'invoice_number': "NONE",
                    'credit_amount': 0.0,
                    'outstanding_amount': 0.0,
                    'purchased_products': [],
                    'future_months': []
                }

                # Get invoice number and payment details
                if bill_id:
                    cursor.execute('''SELECT b.invoice_number, b.credit_amount, b.outstanding_amount
                                     FROM billing b
                                     WHERE b.id = ?''', (bill_id,))
                    bill = cursor.fetchone()
                    if bill:
                        bill_data.update({
                            'invoice_number': bill[0] if bill[0] else "NONE",
                            'credit_amount': bill[1] if bill[1] is not None else 0.0,
                            'outstanding_amount': bill[2] if bill[2] is not None else 0.0
                        })
                else:
                    cursor.execute('''SELECT b.id, b.invoice_number, b.credit_amount, b.outstanding_amount
                                     FROM billing b
                                     WHERE b.customer_id = ? AND b.paid_date = ?
                                     ORDER BY b.paid_date DESC
                                     LIMIT 1''', (customer_id, payment_date))
                    bill = cursor.fetchone()
                    if not bill:
                        raise ValueError("Bill not found")
                    bill_data.update({
                        'invoice_number': bill[1] if bill[1] else "NONE",
                        'credit_amount': bill[2] if bill[2] is not None else 0.0,
                        'outstanding_amount': bill[3] if bill[3] is not None else 0.0
                    })
                    bill_id = bill[0]

                # Get purchased products
                if bill_id:
                    cursor.execute('''SELECT pr.name 
                                     FROM customer_purchases cp
                                     JOIN products pr ON cp.product_id = pr.id
                                     WHERE cp.billing_id = ?''', (bill_id,))
                    bill_data['purchased_products'] = [row[0] for row in cursor.fetchall()]

                    # Get future months for regular bills
                    cursor.execute('''SELECT COUNT(*) 
                                     FROM customer_purchases 
                                     WHERE billing_id = ?''', (bill_id,))
                    has_products = cursor.fetchone()[0] > 0
                    
                    if not has_products:
                        cursor.execute('''SELECT b.amount, p.price 
                                         FROM billing b 
                                         JOIN customers c ON b.customer_id = c.id 
                                         JOIN packages p ON c.package_id = p.id 
                                         WHERE b.id = ?''', (bill_id,))
                        bill_amount, package_price = cursor.fetchone()
                        is_manual_bill = abs(bill_amount - package_price) > 0.01
                        
                        if not is_manual_bill:
                            cursor.execute('''SELECT month, year, amount 
                                             FROM billing 
                                             WHERE customer_id = ? AND paid_date = ? AND id != ?
                                             ORDER BY year, month''', 
                                         (customer_id, payment_date, bill_id))
                            bill_data['future_months'] = cursor.fetchall()

                return bill_data
        except Exception as e:
            self.logger.error(f"Database error fetching bill data: {str(e)}")
            raise Exception("Failed to retrieve bill data from database")

    def _generate_pdf_receipt(self, customer_id: int, amount_paid: float, payment_date: str, 
                            total_pending: float, outstanding_amount: float, bill_id: Optional[int] = None):
        """Generate a PDF receipt with file save dialog"""
        try:
            # Get data in a transaction
            customer_name, customer_phone, package_name, package_price = self._get_customer_data(customer_id)
            bill_data = self._get_bill_data(bill_id, customer_id, payment_date)

            # Prepare filename
            default_dir = os.path.join(os.getenv('APPDATA'), 'CRM_System', 'Receipts')
            os.makedirs(default_dir, exist_ok=True)
            filename = filedialog.asksaveasfilename(
                initialdir=default_dir,
                initialfile=f"Receipt_{customer_name}_{bill_id or 'NA'}_{payment_date}.pdf",
                defaultextension=".pdf",
                filetypes=[("PDF files", "*.pdf")]
            )
            
            if not filename:
                return

            # Generate PDF
            self._create_pdf_file(
                filename=filename,
                customer_name=customer_name,
                customer_phone=customer_phone,
                package_name=package_name,
                package_price=package_price,
                bill_id=bill_id,
                payment_date=payment_date,
                invoice_number=bill_data['invoice_number'],
                purchased_products=bill_data['purchased_products'],
                future_months=bill_data['future_months'],
                amount_paid=amount_paid,
                total_pending=total_pending,
                outstanding_amount=outstanding_amount,
                credit_amount=bill_data['credit_amount']
            )

            self._show_open_dialog(filename)
                
        except ValueError as ve:
            messagebox.showerror("Validation Error", str(ve))
        except Exception as e:
            self.logger.error(f"Failed to generate PDF receipt: {str(e)}", exc_info=True)
            messagebox.showerror("Error", f"Failed to generate PDF receipt: {str(e)}")

    def _create_pdf_file(self, filename: str, customer_name: str, customer_phone: str, 
                        package_name: str, package_price: float, bill_id: Optional[int],
                        payment_date: str, invoice_number: str, purchased_products: List[str],
                        future_months: List[Tuple], amount_paid: float, total_pending: float,
                        outstanding_amount: float, credit_amount: float):
        """Create the PDF file with receipt content"""
        doc = SimpleDocTemplate(
            filename, 
            pagesize=landscape(letter),
            rightMargin=50, 
            leftMargin=50,
            topMargin=50, 
            bottomMargin=50
        )
        
        elements = []
        styles = getSampleStyleSheet()
        
        # Add title
        title_style = ParagraphStyle(
            'Title',
            parent=styles['Heading1'],
            fontSize=16,
            alignment=1,
            spaceAfter=8
        )
        elements.append(Paragraph(self.COMPANY_NAME, title_style))
        
        # Add date and invoice info
        date_invoice_style = ParagraphStyle(
            'DateInvoice',
            parent=styles['Normal'],
            fontSize=10,
            alignment=0,
            spaceAfter=8
        )
        elements.append(Paragraph(f"Date: {payment_date}", date_invoice_style))
        elements.append(Paragraph(f"Invoice Number: {invoice_number}", date_invoice_style))
        elements.append(Spacer(1, 8))
        
        # Add customer info table
        customer_data = [
            ["Customer Name", "Customer Cell", "Bill ID", "Signature"],
            [customer_name, customer_phone, str(bill_id) if bill_id else "N/A", ""]
        ]
        
        customer_table = Table(
            customer_data, 
            colWidths=[2.5*inch, 2.5*inch, 2.5*inch, 2.5*inch]
        )
        customer_table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
            ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
            ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
            ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (-1, 0), 10),
            ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
            ('BACKGROUND', (0, 1), (-1, -1), colors.white),
            ('GRID', (0, 0), (-1, -1), 1, colors.black),
        ]))
        elements.append(customer_table)
        elements.append(Spacer(1, 8))
        
        # Prepare package data
        qty = 1 + len(purchased_products)
        display_package = package_name
        if purchased_products:
            display_package += " + " + ", ".join(purchased_products)

        package_data = [
            ["Qty.", "Package Plan", "Month", "Unit Price (PKR)", "Discount", "Line Total (PKR)"]
        ]

        # Add current month package
        current_month_year = datetime.now().strftime('%B %Y')
        package_data.append([
            str(qty), 
            display_package, 
            current_month_year, 
            f"{package_price:.2f}", 
            "-", 
            f"{total_pending:.2f}"
        ])
        
        # Add future months if any
        for month, year, amount in future_months:
            month_name = datetime(year, month, 1).strftime('%B %Y')
            package_data.append([
                str(qty), 
                display_package, 
                month_name, 
                f"{package_price:.2f}", 
                "-", 
                f"{amount:.2f}"
            ])
        
        # Add empty rows for consistent layout
        remaining_rows = 8 - len(future_months)
        for _ in range(remaining_rows):
            package_data.append(["", "", "", "", "-", ""])
        
        # Format amounts
        credit_display = f"+{abs(credit_amount):.2f}" if credit_amount < 0 else (
                         f"-{credit_amount:.2f}" if credit_amount > 0 else "0.00")
        
        outstanding_display = f"+{abs(outstanding_amount):.2f}" if outstanding_amount < 0 else (
                             f"-{outstanding_amount:.2f}" if outstanding_amount > 0 else "0.00")
        
        # Add payment summary
        package_data.extend([
            ["", "", "", "", "Amount Paid", f"{amount_paid:.2f}"],
            ["", "", "", "", "Credit Amount", credit_display],
            ["", "", "", "", "Outstanding Bill", outstanding_display]
        ])
        
        # Create package table
        package_table = Table(package_data, colWidths=[1.66*inch]*6)
        package_table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
            ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
            ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
            ('ALIGN', (3, 1), (3, -1), 'RIGHT'),
            ('ALIGN', (5, 1), (5, -1), 'RIGHT'),
            ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (-1, 0), 10),
            ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
            ('BACKGROUND', (0, 1), (-1, -1), colors.white),
            ('GRID', (0, 0), (-1, -1), 1, colors.black),
        ]))
        elements.append(package_table)
        elements.append(Spacer(1, 8))
        
        # Add footer
        footer_style = ParagraphStyle(
            'Footer',
            parent=styles['Normal'],
            fontSize=8,
            alignment=1,
            spaceAfter=4
        )
        elements.append(Paragraph(self.RECEIPT_FOOTER, footer_style))
        elements.append(Paragraph(f"<center><b>{self.CONTACT_INFO}</b></center>", footer_style))
        
        thank_style = ParagraphStyle(
            'ThankYou',
            parent=styles['Italic'],
            fontSize=8,
            alignment=1,
        )
        elements.append(Paragraph("Thank you for your business!", thank_style))
        
        doc.build(elements)

    def _show_open_dialog(self, filename: str):
        """Show dialog with option to open the receipt"""
        try:
            dialog = tk.Toplevel(self.parent)
            dialog.title("Receipt Generated")
            dialog.transient(self.parent)
            dialog.grab_set()
            dialog.configure(bg=self.COLORS['secondary'])
            dialog.resizable(False, False)
            
            self._center_window(dialog, 400, 200)
            
            main_frame = tk.Frame(dialog, bg=self.COLORS['secondary'], 
                                highlightbackground=self.COLORS['border'], 
                                highlightthickness=2, padx=20, pady=20)
            main_frame.pack(fill="both", expand=True)

            tk.Label(main_frame, text="Receipt Generated", 
                    bg=self.COLORS['secondary'], 
                    font=("Segoe UI", 16, "bold"), 
                    fg="#FFFFFF").pack(anchor="center", pady=(0, 20))

            label_style = {"bg": self.COLORS['secondary'], 
                          "font": ("Segoe UI", 12), 
                          "fg": "#FFFFFF"}
            
            prompt_frame = tk.Frame(main_frame, bg=self.COLORS['secondary'])
            prompt_frame.pack(fill="x", pady=(0, 30))
            tk.Label(prompt_frame, 
                     text="Receipt generated successfully!", 
                     **label_style, 
                     anchor="center").pack(expand=True)
            
            button_frame = tk.Frame(main_frame, bg=self.COLORS['secondary'])
            button_frame.pack(anchor="center")
            
            tk.Button(
                button_frame,
                text="Open Receipt",
                command=lambda: self._open_receipt(filename),
                bg="#FFFFFF",
                fg="#000000",
                font=("Segoe UI", 12, "bold"),
                relief=tk.FLAT,
                padx=20,
                pady=10,
                activebackground="#FFFFFF",
                activeforeground="#000000"
            ).pack(side="left", padx=20)
            
            tk.Button(
                button_frame,
                text="Close",
                command=dialog.destroy,
                bg="#FFFFFF",
                fg="#000000",
                font=("Segoe UI", 12, "bold"),
                relief=tk.FLAT,
                padx=20,
                pady=10,
                activebackground="#FFFFFF",
                activeforeground="#000000"
            ).pack(side="right", padx=20)

        except Exception as e:
            self.logger.error(f"Error showing open dialog: {str(e)}")
            messagebox.showerror("Error", f"Failed to show open dialog: {str(e)}")

    def _open_receipt(self, filename: str):
        """Open the generated PDF receipt"""
        try:
            if platform.system() == 'Darwin':
                subprocess.call(('open', filename))
            elif platform.system() == 'Windows':
                os.startfile(filename)
            else:
                subprocess.call(('xdg-open', filename))
        except Exception as e:
            self.logger.error(f"Error opening receipt: {str(e)}")
            messagebox.showerror("Error", f"Failed to open receipt: {str(e)}")
