#!/usr/bin/env python3
"""
Quick verification script to test the billing calculation fixes
"""

import sqlite3
import sys
import os

# Add the current directory to the path so we can import our modules
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from database_utils import get_db_connection

def verify_billing_calculations():
    """Verify the billing calculations are working correctly"""
    print("=== Verifying Billing Calculation Fixes ===\n")
    
    with get_db_connection() as conn:
        c = conn.cursor()
        
        # Get a test customer
        c.execute("SELECT id, name FROM customers LIMIT 1")
        customer = c.fetchone()
        if not customer:
            print("❌ No customers found. Please add a customer first.")
            return False
            
        customer_id, customer_name = customer
        print(f"Testing with customer: {customer_name} (ID: {customer_id})")
        
        # Clear existing data for clean testing
        c.execute("DELETE FROM billing WHERE customer_id = ?", (customer_id,))
        c.execute("DELETE FROM payment_history WHERE customer_id = ?", (customer_id,))
        c.execute("UPDATE customers SET outstanding_amount = 0, credit_balance = 0 WHERE id = ?", (customer_id,))
        conn.commit()
        
        print("\n1. Testing Scenario: Unpaid bill + New bill")
        print("   Simulating: First bill 1500 package + 1500 outstanding = 3000 Month Bill")

        # Create first unpaid bill with Month Bill = 3000 (1500 package + 1500 outstanding)
        c.execute('''
            INSERT INTO billing (customer_id, month, year, amount, month_bill_amount, paid_amount, status, outstanding_amount)
            VALUES (?, 7, 2024, 1500, 3000, 0, 'Unpaid', 3000)
        ''', (customer_id,))
        first_bill_id = c.lastrowid
        conn.commit()
        print(f"   ✓ Created first bill: Amount=1500, Month Bill=3000 (unpaid)")

        # Now check what happens when we calculate a new bill
        try:
            from views.billing import BillingManager
            import tkinter as tk

            # Create a minimal root window (hidden)
            root = tk.Tk()
            root.withdraw()

            # Create billing manager instance
            billing_manager = BillingManager(root, {})

            # Calculate month bill for new 2000 PKR manual bill
            temp_bill_id = 999999
            new_bill_amount = 2000
            calculated_month_bill = billing_manager._calculate_month_bill_amount(
                c, customer_id, temp_bill_id, new_bill_amount
            )

            expected_month_bill = 3000 + 2000  # previous Month Bill + new = 5000
            
            print(f"   ✓ New bill amount: {new_bill_amount} PKR")
            print(f"   ✓ Calculated Month Bill: {calculated_month_bill} PKR")
            print(f"   ✓ Expected Month Bill: {expected_month_bill} PKR")
            
            if abs(calculated_month_bill - expected_month_bill) < 0.01:
                print("   ✅ PASS: Month Bill calculation is correct")
                test1_pass = True
            else:
                print("   ❌ FAIL: Month Bill calculation is incorrect")
                test1_pass = False
            
            root.destroy()
            
        except Exception as e:
            print(f"   ❌ ERROR: {str(e)}")
            test1_pass = False
        
        print("\n2. Testing Payment Calculation")
        
        # Create the second bill with calculated month_bill_amount
        c.execute('''
            INSERT INTO billing (customer_id, month, year, amount, month_bill_amount, paid_amount, status, outstanding_amount)
            VALUES (?, 6, 2024, 2000, ?, 0, 'Unpaid', ?)
        ''', (customer_id, calculated_month_bill, calculated_month_bill))
        second_bill_id = c.lastrowid
        conn.commit()
        print(f"   ✓ Created second bill: 2000 PKR (Month Bill: {calculated_month_bill} PKR)")

        # Check payment calculation (what the payment dialog would show)
        c.execute('''
            SELECT b.id, b.month, b.year,
                   b.amount - COALESCE(b.paid_amount, 0) as pending,
                   b.amount as original_amount,
                   b.month_bill_amount
            FROM billing b
            WHERE b.customer_id = ? AND (b.amount - COALESCE(b.paid_amount, 0)) > 0
            ORDER BY b.year, b.month ASC
        ''', (customer_id,))
        unpaid_bills = c.fetchall()

        total_pending = sum(bill[3] for bill in unpaid_bills)  # Sum of pending amounts
        expected_total = 1500 + 2000  # What user should actually pay (base amounts)
        
        print(f"   ✓ Total pending amount: {total_pending} PKR")
        print(f"   ✓ Expected total: {expected_total} PKR")
        
        if abs(total_pending - expected_total) < 0.01:
            print("   ✅ PASS: Payment calculation is correct")
            test2_pass = True
        else:
            print("   ❌ FAIL: Payment calculation is incorrect")
            test2_pass = False
        
        print("\n3. Testing Manual Bill Amount Display")
        
        # Clear and create a manual bill
        c.execute("DELETE FROM billing WHERE customer_id = ?", (customer_id,))
        c.execute("UPDATE customers SET outstanding_amount = 0, credit_balance = 0 WHERE id = ?", (customer_id,))
        conn.commit()
        
        manual_amount = 800
        c.execute('''
            INSERT INTO billing (customer_id, month, year, amount, month_bill_amount, paid_amount, status, outstanding_amount, is_manual)
            VALUES (?, 7, 2024, ?, ?, 0, 'Unpaid', ?, 1)
        ''', (customer_id, manual_amount, manual_amount, manual_amount))
        manual_bill_id = c.lastrowid
        conn.commit()
        
        print(f"   ✓ Created manual bill: {manual_amount} PKR")
        
        # Check if manual bill shows correct amount (this would be used in display logic)
        c.execute('''
            SELECT amount, is_manual FROM billing WHERE id = ?
        ''', (manual_bill_id,))
        bill_data = c.fetchone()
        
        if bill_data and bill_data[1] == 1:  # is_manual = 1
            display_amount = bill_data[0]  # Should show manual amount, not package price
            if abs(display_amount - manual_amount) < 0.01:
                print("   ✅ PASS: Manual bill shows correct amount")
                test3_pass = True
            else:
                print("   ❌ FAIL: Manual bill shows wrong amount")
                test3_pass = False
        else:
            print("   ❌ FAIL: Manual bill not found or not marked as manual")
            test3_pass = False
        
        # Summary
        print(f"\n=== Test Results ===")
        print(f"Month Bill Calculation: {'✅ PASS' if test1_pass else '❌ FAIL'}")
        print(f"Payment Calculation: {'✅ PASS' if test2_pass else '❌ FAIL'}")
        print(f"Manual Bill Display: {'✅ PASS' if test3_pass else '❌ FAIL'}")
        
        all_tests_pass = test1_pass and test2_pass and test3_pass
        print(f"\nOverall Result: {'✅ ALL TESTS PASS' if all_tests_pass else '❌ SOME TESTS FAILED'}")
        
        return all_tests_pass

if __name__ == "__main__":
    try:
        success = verify_billing_calculations()
        if success:
            print("\n🎉 Billing calculation fixes are working correctly!")
        else:
            print("\n⚠️  Some issues remain. Please check the test results above.")
    except Exception as e:
        print(f"\n❌ Error during verification: {str(e)}")
        import traceback
        traceback.print_exc()
