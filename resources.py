# resources.py
import sys
import os

def resource_path(relative_path):
    """Get absolute path to resource, works for dev and for PyInstaller"""
    if relative_path is None:
        raise ValueError("resource_path() received a None argument for relative_path.")
    try:
        # PyInstaller creates a temp folder and stores path in _MEIPASS
        base_path = sys._MEIPASS
    except Exception:
        base_path = os.path.abspath(".")

    path = os.path.join(base_path, relative_path)
    
    # Normalize path for different operating systems
    path = os.path.normpath(path)
    
    # Verify the path exists (for debugging)
    if not os.path.exists(path):
        raise FileNotFoundError(f"Resource not found: {path}")
        
    return path