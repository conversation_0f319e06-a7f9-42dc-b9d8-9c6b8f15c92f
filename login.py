import tkinter as tk
from tkinter import ttk, messagebox
import re
import os
import sqlite3
import bcrypt
from PIL import Image, ImageTk
from database_utils import get_db_connection

# Global variable to store the current logged-in user's username
current_user = None

class LoginPage(tk.Frame):
    def __init__(self, parent, nav_commands):
        super().__init__(parent)
        self.nav_commands = nav_commands
        self.parent = parent

        # Updated color scheme for a more modern look
        self.COLORS = {
            'background': '#F8F9FA',
            'card_bg': '#FFFFFF',
            'primary_accent': '#4A6FA5',
            'secondary_accent': '#6C8FC7',
            'text_primary': '#2D3748',
            'text_secondary': '#718096',
            'button_start': '#4A6FA5',
            'button_end': '#3A5A8C',
            'transparent': self.parent.cget('bg'),
            'warning': '#E53E3E',
            'input_bg': '#EDF2F7',
            'border': '#E2E8F0'
        }

        self.configure(bg=self.COLORS['background'])
        self.pack(fill="both", expand=True)

        # Create a canvas for the background image
        self.canvas = tk.Canvas(self, bg=self.COLORS['background'], highlightthickness=0)
        self.canvas.pack(fill="both", expand=True)

        # Load and display background image
        self.bg_image = None
        self.bg_photo = None
        self.load_background_image()

        # Main container frame
        self.main_frame = tk.Frame(self.canvas, bg=self.COLORS['background'])
        self.main_frame.place(relx=0.5, rely=0.5, anchor="center")

        # Login card
        self.login_card = tk.Frame(
            self.main_frame,
            bg=self.COLORS['card_bg'],
            padx=40,
            pady=40,
            relief="flat",
            borderwidth=0
        )
        self.login_card.pack(pady=20)

        # App title/logo
        self.logo_frame = tk.Frame(self.login_card, bg=self.COLORS['card_bg'])
        self.logo_frame.pack(pady=(0, 30))
        
        self.logo_label = tk.Label(
            self.logo_frame,
            text="SANI BROADBAND",
            font=("Helvetica", 24, "bold"),
            fg=self.COLORS['primary_accent'],
            bg=self.COLORS['card_bg']
        )
        self.logo_label.pack()
        
        self.subtitle_label = tk.Label(
            self.logo_frame,
            text="Login to your account",
            font=("Helvetica", 12),
            fg=self.COLORS['text_secondary'],
            bg=self.COLORS['card_bg']
        )
        self.subtitle_label.pack(pady=(5, 0))

        # Input fields
        self.input_frame = tk.Frame(self.login_card, bg=self.COLORS['card_bg'])
        self.input_frame.pack(fill="x")

        # Username field
        self.username_label = tk.Label(
            self.input_frame,
            text="Username",
            font=("Helvetica", 10),
            fg=self.COLORS['text_primary'],
            bg=self.COLORS['card_bg'],
            anchor='w'
        )
        self.username_label.pack(fill="x", pady=(10, 5))
        
        self.username_entry = ttk.Entry(
            self.input_frame,
            font=("Helvetica", 12),
            width=30,
            style='Modern.TEntry'
        )
        self.username_entry.pack(fill="x", ipady=8)

        # Password field
        self.password_label = tk.Label(
            self.input_frame,
            text="Password",
            font=("Helvetica", 10),
            fg=self.COLORS['text_primary'],
            bg=self.COLORS['card_bg'],
            anchor='w'
        )
        self.password_label.pack(fill="x", pady=(15, 5))
        
        self.password_entry = ttk.Entry(
            self.input_frame,
            font=("Helvetica", 12),
            show="•",
            width=30,
            style='Modern.TEntry'
        )
        self.password_entry.pack(fill="x", ipady=8)

        # Forgot password link
        self.forgot_pass = tk.Label(
            self.input_frame,
            text="Forgot password?",
            font=("Helvetica", 10),
            fg=self.COLORS['primary_accent'],
            bg=self.COLORS['card_bg'],
            cursor="hand2"
        )
        self.forgot_pass.pack(fill="x", pady=(10, 20), anchor="e")
        self.forgot_pass.bind("<Button-1>", lambda e: self.nav_commands['show_forgot_password']())

        # Login button
        self.login_btn = tk.Button(
            self.input_frame,
            text="Login",
            font=("Helvetica", 12, "bold"),
            bg=self.COLORS['primary_accent'],
            fg="#FFFFFF",
            activebackground=self.COLORS['secondary_accent'],
            activeforeground="#FFFFFF",
            width=30,
            height=1,
            bd=0,
            relief="flat",
            command=self.login
        )
        self.login_btn.pack(fill="x", pady=(0, 20), ipady=10)

        # Divider
        self.divider = tk.Frame(
            self.input_frame,
            bg=self.COLORS['border'],
            height=1
        )
        self.divider.pack(fill="x", pady=10)

        # Register link
        self.register_frame = tk.Frame(self.input_frame, bg=self.COLORS['card_bg'])
        self.register_frame.pack()
        
        self.register_label = tk.Label(
            self.register_frame,
            text="Don't have an account?",
            font=("Helvetica", 10),
            fg=self.COLORS['text_secondary'],
            bg=self.COLORS['card_bg']
        )
        self.register_label.pack(side="left")
        
        self.register_link = tk.Label(
            self.register_frame,
            text="Register",
            font=("Helvetica", 10, "bold"),
            fg=self.COLORS['primary_accent'],
            bg=self.COLORS['card_bg'],
            cursor="hand2"
        )
        self.register_link.pack(side="left", padx=5)
        self.register_link.bind("<Button-1>", lambda e: self.nav_commands['show_register']())

        # Configure styles
        self.style = ttk.Style()
        self.style.configure('Modern.TEntry',
            foreground=self.COLORS['text_primary'],
            fieldbackground=self.COLORS['input_bg'],
            borderwidth=1,
            relief="flat",
            padding=10
        )
        self.style.map('Modern.TEntry',
            fieldbackground=[('active', self.COLORS['input_bg']), ('!disabled', self.COLORS['input_bg'])],
            bordercolor=[('focus', self.COLORS['primary_accent']), ('!focus', self.COLORS['border'])],
            lightcolor=[('focus', self.COLORS['primary_accent']), ('!focus', self.COLORS['border'])],
            darkcolor=[('focus', self.COLORS['primary_accent']), ('!focus', self.COLORS['border'])]
        )
        
        # Add the footer
        self.create_footer()

    def create_footer(self):
        """Create an elegant footer with branding information"""
        # Footer frame
        self.footer_frame = tk.Frame(self.main_frame, bg=self.COLORS['background'], pady=2)
        self.footer_frame.pack(fill="x")
        
        # Powered by text
        self.powered_by = tk.Label(
            self.footer_frame,
            text="Powered by",
            font=("Helvetica", 8),
            fg=self.COLORS['text_secondary'],
            bg=self.COLORS['background']
        )
        self.powered_by.pack()
        
        # BISH TECHNOLOGIES (2025)
        self.tech_label = tk.Label(
            self.footer_frame,
            text="BISH TECHNOLOGIES (2025)",
            font=("Helvetica", 9, "bold"),
            fg=self.COLORS['primary_accent'],
            bg=self.COLORS['background']
        )
        self.tech_label.pack(pady=(1, 0))
        
        # Email contact
        self.email_label = tk.Label(
            self.footer_frame,
            text="<EMAIL>",
            font=("Helvetica", 8),
            fg=self.COLORS['text_secondary'],
            bg=self.COLORS['background'],
            cursor="hand2"
        )
        self.email_label.pack(pady=(2, 0))
        
        # Make email clickable (this will open default mail client)
        self.email_label.bind("<Button-1>", lambda e: os.system(f'start mailto:<EMAIL>'))

    def load_background_image(self):
        try:
            self.base_path = os.path.dirname(os.path.abspath(__file__))
            self.bg_image_path = os.path.join(self.base_path, '..', 'assets', 'dashboard', 'background101.png')
            if not os.path.exists(self.bg_image_path):
                self.bg_image_path = r'D:\CRM_System\assets\dashboard\background1.png'
            
            self.bg_image = Image.open(self.bg_image_path)
            self.resize_background()
        except Exception as e:
            # If image fails to load, use solid color background
            self.canvas.configure(bg=self.COLORS['background'])

    def resize_background(self):
        if not self.bg_image:
            return
            
        width = self.winfo_width()
        height = self.winfo_height()
        
        if width > 1 and height > 1:
            resized_image = self.bg_image.resize((width, height), Image.LANCZOS)
            self.bg_photo = ImageTk.PhotoImage(resized_image)
            self.canvas.create_image(0, 0, image=self.bg_photo, anchor="nw")

    def validate_username(self, username):
        if len(username) > 20:
            return False, "Username must be 20 characters or less."
        if not re.match(r'^[a-zA-Z0-9_]+$', username):
            return False, "Username can only contain letters, numbers, and underscores."
        return True, ""

    def validate_password(self, password):
        if not (8 <= len(password) <= 15):
            return False, "Password must be between 8 and 15 characters."
        return True, ""

    def sanitize_input(self, input_str):
        dangerous_patterns = [
            r'\b(ALTER|CREATE|DELETE|DROP|EXEC(UTE)?|INSERT|SELECT|UPDATE)\b',
            r'--',
            r';',
            r'/\*.*?\*/'
        ]
        sanitized = input_str
        for pattern in dangerous_patterns:
            sanitized = re.sub(pattern, '', sanitized, flags=re.IGNORECASE)
        return sanitized

    def login(self):
        global current_user
        username = self.sanitize_input(self.username_entry.get().strip())
        password = self.sanitize_input(self.password_entry.get().strip())

        is_valid_username, username_error = self.validate_username(username)
        is_valid_password, password_error = self.validate_password(password)

        if not is_valid_username:
            messagebox.showerror("Invalid Input", username_error)
            return
        if not is_valid_password:
            messagebox.showerror("Invalid Input", password_error)
            return

        try:
            with get_db_connection() as conn:
                c = conn.cursor()
                c.execute("SELECT password FROM users WHERE username = ?", (username,))
                result = c.fetchone()
                if result:
                    stored_password = result[0].encode('utf-8')
                    if bcrypt.checkpw(password.encode('utf-8'), stored_password):
                        current_user = username  # Store the logged-in user's username
                        messagebox.showinfo("Success", "Login successful!")
                        self.nav_commands['show_dashboard']()
                    else:
                        messagebox.showerror("Error", "Invalid username or password.")
                else:
                    messagebox.showerror("Error", "Invalid username or password.")
        except Exception as e:
            messagebox.showerror("Database Error", f"Could not login: {str(e)}")
