#!/usr/bin/env python3
"""
Test script to verify payment processing fixes:
1. Invoice number generation
2. Payment history recording with credit/outstanding amounts
3. Display of credit/outstanding in billing page
"""

import sqlite3
import sys
import os

# Add the current directory to the path so we can import our modules
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from database import Database

def test_invoice_generation():
    """Test that invoice numbers are generated for bills without them"""
    print("Testing invoice number generation...")
    
    db = Database()
    
    # Check if there are any bills without invoice numbers
    with db.get_connection_context() as conn:
        c = conn.cursor()
        
        # Count bills without invoice numbers
        c.execute("SELECT COUNT(*) FROM billing WHERE invoice_number IS NULL OR invoice_number = ''")
        bills_without_invoices = c.fetchone()[0]
        print(f"Bills without invoice numbers: {bills_without_invoices}")
        
        # Check if invoice numbers follow the correct format
        c.execute("SELECT invoice_number FROM billing WHERE invoice_number IS NOT NULL AND invoice_number != '' LIMIT 5")
        sample_invoices = c.fetchall()
        print(f"Sample invoice numbers: {[inv[0] for inv in sample_invoices]}")
        
        return bills_without_invoices == 0

def test_payment_history():
    """Test that payment history contains credit and outstanding amounts"""
    print("\nTesting payment history structure...")
    
    db = Database()
    
    with db.get_connection_context() as conn:
        c = conn.cursor()
        
        # Check payment_history table structure
        c.execute("PRAGMA table_info(payment_history)")
        columns = [col[1] for col in c.fetchall()]
        
        required_columns = ['credit_amount', 'outstanding_amount', 'remaining_outstanding', 'applied_credit']
        missing_columns = [col for col in required_columns if col not in columns]
        
        print(f"Payment history columns: {columns}")
        print(f"Missing required columns: {missing_columns}")
        
        # Check if there are payment history records with credit/outstanding data
        c.execute("""
            SELECT COUNT(*) FROM payment_history 
            WHERE credit_amount > 0 OR outstanding_amount > 0
        """)
        records_with_amounts = c.fetchone()[0]
        print(f"Payment history records with credit/outstanding amounts: {records_with_amounts}")
        
        return len(missing_columns) == 0

def test_billing_display():
    """Test that billing table has credit and outstanding columns"""
    print("\nTesting billing table structure...")
    
    db = Database()
    
    with db.get_connection_context() as conn:
        c = conn.cursor()
        
        # Check billing table structure
        c.execute("PRAGMA table_info(billing)")
        columns = [col[1] for col in c.fetchall()]
        
        required_columns = ['outstanding_amount', 'credit_amount', 'invoice_number']
        missing_columns = [col for col in required_columns if col not in columns]
        
        print(f"Billing table columns: {columns}")
        print(f"Missing required columns: {missing_columns}")
        
        # Check sample billing data
        c.execute("""
            SELECT id, invoice_number, outstanding_amount, credit_amount 
            FROM billing 
            LIMIT 5
        """)
        sample_bills = c.fetchall()
        print(f"Sample billing data:")
        for bill in sample_bills:
            print(f"  Bill ID: {bill[0]}, Invoice: {bill[1]}, Outstanding: {bill[2]}, Credit: {bill[3]}")
        
        return len(missing_columns) == 0

def main():
    """Run all tests"""
    print("=== Testing Payment Processing Fixes ===\n")
    
    try:
        # Test 1: Invoice number generation
        invoice_test = test_invoice_generation()
        
        # Test 2: Payment history structure
        history_test = test_payment_history()
        
        # Test 3: Billing display structure
        billing_test = test_billing_display()
        
        print(f"\n=== Test Results ===")
        print(f"Invoice generation: {'PASS' if invoice_test else 'FAIL'}")
        print(f"Payment history: {'PASS' if history_test else 'FAIL'}")
        print(f"Billing display: {'PASS' if billing_test else 'FAIL'}")
        
        if all([invoice_test, history_test, billing_test]):
            print("\n✅ All tests passed! Payment processing fixes are working correctly.")
        else:
            print("\n❌ Some tests failed. Please check the implementation.")
            
    except Exception as e:
        print(f"\n❌ Error running tests: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
