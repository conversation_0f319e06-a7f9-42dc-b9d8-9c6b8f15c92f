import sqlite3

DB_PATH = 'crm_database.db'

def add_bill_id_column():
    conn = sqlite3.connect(DB_PATH)
    c = conn.cursor()
    # Check if 'bill_id' column already exists
    c.execute("PRAGMA table_info(payment_history)")
    columns = [row[1] for row in c.fetchall()]
    if 'bill_id' not in columns:
        c.execute("ALTER TABLE payment_history ADD COLUMN bill_id INTEGER")
        print("'bill_id' column added to payment_history table.")
    else:
        print("'bill_id' column already exists in payment_history table.")
    conn.commit()
    conn.close()

if __name__ == '__main__':
    add_bill_id_column() 