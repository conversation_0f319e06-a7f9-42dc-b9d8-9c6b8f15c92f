import sqlite3
import os
from contextlib import contextmanager
import time
import logging
from typing import Any, Optional, Tuple, List, Dict, Union, Iterator

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Configurable settings (can be overridden if needed)
DEFAULT_MAX_RETRIES = 3
DEFAULT_RETRY_DELAY = 1  # seconds
DEFAULT_BUSY_TIMEOUT = 30000  # 30 seconds
DEFAULT_CONNECTION_TIMEOUT = 30  # seconds

def get_db_path() -> str:
    """Get the path to the SQLite database file."""
    app_data_path = os.path.join(os.getenv('APPDATA'), 'CRM_System')
    os.makedirs(app_data_path, exist_ok=True)
    return os.path.join(app_data_path, 'crm_database.db')

@contextmanager
def get_db_connection(max_retries: int = DEFAULT_MAX_RETRIES, 
                     retry_delay: int = DEFAULT_RETRY_DELAY) -> Iterator[sqlite3.Connection]:
    """Context manager for database connections with retry logic.
    
    Args:
        max_retries: Maximum number of connection attempts
        retry_delay: Initial delay between retries (in seconds, will increase with each attempt)
        
    Yields:
        sqlite3.Connection: A database connection
        
    Raises:
        sqlite3.OperationalError: If connection fails after all retries
    """
    conn = None
    for attempt in range(max_retries):
        try:
            conn = sqlite3.connect(
                get_db_path(),
                timeout=DEFAULT_CONNECTION_TIMEOUT,
                isolation_level=None  # Let us control transactions manually
            )
            # Optimize for multi-user access
            conn.execute("PRAGMA journal_mode=WAL")
            conn.execute("PRAGMA synchronous=NORMAL")
            conn.execute(f"PRAGMA busy_timeout={DEFAULT_BUSY_TIMEOUT}")
            conn.execute("PRAGMA foreign_keys=ON")
            
            yield conn
            return  # Success - exit the retry loop
            
        except sqlite3.OperationalError as e:
            if "database is locked" not in str(e) and "database is busy" not in str(e):
                logger.error(f"Database error (attempt {attempt+1}): {str(e)}")
                raise
                
            if attempt == max_retries - 1:
                logger.error(f"Database connection failed after {max_retries} attempts: {str(e)}")
                raise
                
            sleep_time = retry_delay * (attempt + 1)
            logger.warning(f"Database locked/busy, retrying in {sleep_time} seconds...")
            time.sleep(sleep_time)
            
        finally:
            if conn:
                conn.close()

def execute_with_retry(query: str, 
                      params: Union[Tuple, Dict] = (), 
                      max_retries: int = DEFAULT_MAX_RETRIES) -> Optional[int]:
    """Execute a query with automatic retry on locked database.
    
    Args:
        query: SQL query to execute
        params: Parameters for the query
        max_retries: Maximum number of execution attempts
        
    Returns:
        For INSERT: lastrowid, for other queries: rowcount
        Returns None for SELECT queries (use fetch_with_retry for SELECTs)
        
    Raises:
        sqlite3.Error: If execution fails after all retries
    """
    for attempt in range(max_retries):
        cursor = None
        try:
            with get_db_connection(max_retries, DEFAULT_RETRY_DELAY) as conn:
                cursor = conn.cursor()
                cursor.execute("BEGIN")
                cursor.execute(query, params)
                conn.commit()
                
                if "INSERT" in query.upper():
                    return cursor.lastrowid
                return cursor.rowcount
                
        except sqlite3.OperationalError as e:
            if attempt == max_retries - 1:
                logger.error(f"Query execution failed after {max_retries} attempts: {str(e)}")
                raise
                
            if "database is locked" in str(e) or "database is busy" in str(e):
                sleep_time = DEFAULT_RETRY_DELAY * (attempt + 1)
                logger.warning(f"Database locked/busy, retrying in {sleep_time} seconds...")
                time.sleep(sleep_time)
                continue
                
            raise  # Re-raise other operational errors
            
        except sqlite3.Error as e:
            if conn:
                conn.rollback()
            logger.error(f"Database error during query execution: {str(e)}")
            raise
            
        finally:
            if cursor:
                cursor.close()

def fetch_with_retry(query: str, 
                    params: Union[Tuple, Dict] = (), 
                    max_retries: int = DEFAULT_MAX_RETRIES) -> List[Tuple]:
    """Execute a SELECT query and fetch all results with retry logic.
    
    Args:
        query: SELECT query to execute
        params: Parameters for the query
        max_retries: Maximum number of execution attempts
        
    Returns:
        List of rows (each row is a tuple)
        
    Raises:
        sqlite3.Error: If execution fails after all retries
    """
    for attempt in range(max_retries):
        cursor = None
        try:
            with get_db_connection(max_retries, DEFAULT_RETRY_DELAY) as conn:
                cursor = conn.cursor()
                cursor.execute(query, params)
                return cursor.fetchall()
                
        except sqlite3.OperationalError as e:
            if attempt == max_retries - 1:
                logger.error(f"Query execution failed after {max_retries} attempts: {str(e)}")
                raise
                
            if "database is locked" in str(e) or "database is busy" in str(e):
                sleep_time = DEFAULT_RETRY_DELAY * (attempt + 1)
                logger.warning(f"Database locked/busy, retrying in {sleep_time} seconds...")
                time.sleep(sleep_time)
                continue
                
            raise  # Re-raise other operational errors
            
        except sqlite3.Error as e:
            logger.error(f"Database error during query execution: {str(e)}")
            raise
            
        finally:
            if cursor:
                cursor.close()

def migrate_payment_history_table():
    """Add bill_id, remaining_outstanding, and applied_credit columns to payment_history if not present."""
    import sqlite3
    from database_utils import get_db_connection
    with get_db_connection() as conn:
        c = conn.cursor()
        c.execute("PRAGMA table_info(payment_history)")
        columns = [col[1] for col in c.fetchall()]
        if 'bill_id' not in columns:
            c.execute("ALTER TABLE payment_history ADD COLUMN bill_id INTEGER")
        if 'remaining_outstanding' not in columns:
            c.execute("ALTER TABLE payment_history ADD COLUMN remaining_outstanding REAL DEFAULT 0")
        if 'applied_credit' not in columns:
            c.execute("ALTER TABLE payment_history ADD COLUMN applied_credit REAL DEFAULT 0")
        conn.commit()
