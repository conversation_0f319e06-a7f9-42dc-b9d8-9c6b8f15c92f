#!/usr/bin/env python3
"""
Test script to verify the Month Bill calculation logic:

Condition 1: If credit > 0 and credit >= package_price:
            Generate and pay future months until credit = 0 or credit < package_price

Condition 2: If credit > 0 and credit < package_price:
            Month Bill = package_price - credit

Condition 3: If credit = 0 and outstanding > 0:
            Month Bill = package_price + outstanding

Note: Credit and Outstanding cannot both be > 0 for same customer
"""

import sqlite3
import sys
import os
from datetime import datetime

# Add the current directory to the path so we can import our modules
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from database import get_db_connection

def test_month_bill_scenarios():
    """Test different Month Bill calculation scenarios"""
    print("Testing Month Bill calculation logic...")
    
    with get_db_connection() as conn:
        c = conn.cursor()
        
        # Get a test customer
        c.execute("SELECT id, name FROM customers LIMIT 1")
        customer = c.fetchone()
        if not customer:
            print("No customers found. Please add a customer first.")
            return
            
        customer_id, customer_name = customer
        print(f"Testing with customer: {customer_name} (ID: {customer_id})")
        
        # Clear existing bills for clean testing
        c.execute("DELETE FROM billing WHERE customer_id = ?", (customer_id,))
        c.execute("DELETE FROM payment_history WHERE customer_id = ?", (customer_id,))
        
        package_price = 1500.0
        
        # Test scenarios
        scenarios = [
            {
                "name": "Scenario 1: Outstanding > 0, Credit = 0",
                "outstanding": 500.0,
                "credit": 0.0,
                "expected_month_bill": package_price + 500.0,  # 1500 + 500 = 2000
                "description": "Month Bill = Package Price + Outstanding"
            },
            {
                "name": "Scenario 2: Credit < Package Price",
                "outstanding": 0.0,
                "credit": 300.0,
                "expected_month_bill": package_price - 300.0,  # 1500 - 300 = 1200
                "description": "Month Bill = Package Price - Credit"
            },
            {
                "name": "Scenario 3: Credit >= Package Price",
                "outstanding": 0.0,
                "credit": 2000.0,
                "expected_month_bill": 0.0,  # Bill covered by credit
                "description": "Month Bill = 0 (covered by credit, future bills generated)"
            },
            {
                "name": "Scenario 4: No Credit, No Outstanding",
                "outstanding": 0.0,
                "credit": 0.0,
                "expected_month_bill": package_price,  # 1500
                "description": "Month Bill = Package Price"
            }
        ]
        
        for i, scenario in enumerate(scenarios, 1):
            print(f"\n{'='*60}")
            print(f"Testing {scenario['name']}")
            print(f"Description: {scenario['description']}")
            print(f"Outstanding: {scenario['outstanding']} PKR")
            print(f"Credit: {scenario['credit']} PKR")
            print(f"Expected Month Bill: {scenario['expected_month_bill']} PKR")
            
            # Create test bill
            c.execute('''
                INSERT INTO billing (
                    customer_id, month, year, amount, paid_amount, outstanding_amount,
                    credit_amount, status, invoice_number, is_manual
                ) VALUES (?, ?, ?, ?, 0, ?, ?, 'Unpaid', ?, 0)
            ''', (customer_id, i, 2025, package_price, scenario['outstanding'], 
                  scenario['credit'], f'TEST-{i:03d}'))
            
            bill_id = c.lastrowid
            
            # Test the Month Bill calculation
            from views.billing import BillingManager
            import tkinter as tk
            
            root = tk.Tk()
            root.withdraw()
            
            billing_manager = BillingManager(root, {})
            
            # Calculate Month Bill
            calculated_month_bill = billing_manager._calculate_month_bill_amount(
                c, customer_id, bill_id, package_price, scenario['outstanding']
            )
            
            print(f"Calculated Month Bill: {calculated_month_bill} PKR")
            
            # Verify result
            if abs(calculated_month_bill - scenario['expected_month_bill']) < 0.01:
                print("✅ CORRECT: Month Bill calculation matches expected result")
            else:
                print("❌ INCORRECT: Month Bill calculation is wrong")
                print(f"   Expected: {scenario['expected_month_bill']} PKR")
                print(f"   Got: {calculated_month_bill} PKR")
            
            root.destroy()
            
            # Clean up for next test
            c.execute("DELETE FROM billing WHERE id = ?", (bill_id,))
            
        conn.commit()

def test_imported_bill_scenario():
    """Test the specific imported bill scenario from the user's data"""
    print(f"\n{'='*60}")
    print("Testing Imported Bill Scenario (User's Data)")
    
    with get_db_connection() as conn:
        c = conn.cursor()
        
        # Find Irfan Anwar's bill
        c.execute('''
            SELECT b.id, b.customer_id, b.amount, b.outstanding_amount, b.credit_amount, c.name
            FROM billing b
            JOIN customers c ON b.customer_id = c.id
            WHERE c.name LIKE '%Irfan%'
            ORDER BY b.id DESC
            LIMIT 1
        ''')
        
        bill = c.fetchone()
        if not bill:
            print("No bill found for Irfan Anwar")
            return
            
        bill_id, customer_id, amount, outstanding, credit, customer_name = bill
        
        print(f"Customer: {customer_name}")
        print(f"Package Amount: {amount} PKR")
        print(f"Outstanding: {outstanding} PKR")
        print(f"Credit: {credit} PKR")
        
        # Test the Month Bill calculation
        from views.billing import BillingManager
        import tkinter as tk
        
        root = tk.Tk()
        root.withdraw()
        
        billing_manager = BillingManager(root, {})
        
        calculated_month_bill = billing_manager._calculate_month_bill_amount(
            c, customer_id, bill_id, amount, outstanding
        )
        
        print(f"Calculated Month Bill: {calculated_month_bill} PKR")
        
        # For imported bill with outstanding, expected logic:
        if credit == 0 and outstanding > 0:
            expected = amount + outstanding
            print(f"Expected (Package + Outstanding): {expected} PKR")
        elif credit > 0 and credit < amount:
            expected = amount - credit
            print(f"Expected (Package - Credit): {expected} PKR")
        elif credit >= amount:
            expected = 0
            print(f"Expected (Covered by Credit): {expected} PKR")
        else:
            expected = amount
            print(f"Expected (Package Price): {expected} PKR")
        
        if abs(calculated_month_bill - expected) < 0.01:
            print("✅ CORRECT: Month Bill calculation is correct for imported data")
        else:
            print("❌ INCORRECT: Month Bill calculation is wrong for imported data")
        
        root.destroy()

def main():
    """Run all Month Bill calculation tests"""
    print("=== Testing Month Bill Calculation Logic ===\n")
    
    try:
        test_month_bill_scenarios()
        test_imported_bill_scenario()
        
        print(f"\n{'='*60}")
        print("Month Bill Calculation Rules Summary:")
        print("1. Credit >= Package Price → Month Bill = 0 (generate future bills)")
        print("2. Credit < Package Price → Month Bill = Package Price - Credit")
        print("3. Outstanding > 0, Credit = 0 → Month Bill = Package Price + Outstanding")
        print("4. No Credit, No Outstanding → Month Bill = Package Price")
        print("\nNote: Credit and Outstanding cannot both be > 0 for same customer")
        
    except Exception as e:
        print(f"\n❌ Error during testing: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
