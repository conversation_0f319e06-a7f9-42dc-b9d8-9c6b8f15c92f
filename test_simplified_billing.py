#!/usr/bin/env python3
"""
Test script to verify the simplified Month Bill calculation logic:

Scenario 1: When there is any unpaid bill
           new bill = Previous Month bill + new amount (Manual Amount/Product price/System generated Bill)

Scenario 2A: When all bills are paid and there are previous outstandings
            month bill = new amount + Previous Outstandings

Scenario 2B: When all bills are paid and there are previous credits
            month bill = new amount - Previous Credits
"""

import sqlite3
import sys
import os
from datetime import datetime

# Add the current directory to the path so we can import our modules
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from database_utils import get_db_connection

def test_simplified_billing_scenarios():
    """Test different simplified billing calculation scenarios"""
    print("Testing Simplified Billing calculation logic...")
    
    with get_db_connection() as conn:
        c = conn.cursor()
        
        # Get a test customer
        c.execute("SELECT id, name FROM customers LIMIT 1")
        customer = c.fetchone()
        if not customer:
            print("No customers found. Please add a customer first.")
            return
            
        customer_id, customer_name = customer
        print(f"Testing with customer: {customer_name} (ID: {customer_id})")
        
        # Clear existing data for clean testing
        c.execute("DELETE FROM billing WHERE customer_id = ?", (customer_id,))
        c.execute("DELETE FROM payment_history WHERE customer_id = ?", (customer_id,))
        c.execute("UPDATE customers SET outstanding_amount = 0, credit_balance = 0 WHERE id = ?", (customer_id,))
        conn.commit()
        
        package_price = 1500.0
        
        # Test scenarios for simplified billing rules
        scenarios = [
            {
                'name': 'Scenario 1: Unpaid bills exist',
                'setup': 'unpaid_bill',
                'unpaid_amount': 1200.0,  # Previous unpaid bill
                'new_amount': 1500.0,     # New bill amount
                'expected_month_bill': 2700.0,  # 1200 + 1500 = 2700
                'description': 'When unpaid bills exist, new bill = Previous Month bill + new amount'
            },
            {
                'name': 'Scenario 2A: All paid, previous outstanding',
                'setup': 'customer_outstanding',
                'customer_outstanding': 300.0,
                'new_amount': 1500.0,
                'expected_month_bill': 1800.0,  # 1500 + 300 = 1800
                'description': 'All bills paid with previous outstanding'
            },
            {
                'name': 'Scenario 2B: All paid, previous credit',
                'setup': 'customer_credit',
                'customer_credit': 400.0,
                'new_amount': 1500.0,
                'expected_month_bill': 1100.0,  # 1500 - 400 = 1100
                'description': 'All bills paid with previous credit'
            },
            {
                'name': 'Default: All paid, no outstanding/credit',
                'setup': 'clean_slate',
                'new_amount': 1500.0,
                'expected_month_bill': 1500.0,  # Just the package price
                'description': 'Default case with no outstanding or credit'
            }
        ]
        
        for i, scenario in enumerate(scenarios, 1):
            print(f"\n--- Test {i}: {scenario['name']} ---")
            print(f"Description: {scenario['description']}")
            
            # Clear previous test data
            c.execute("DELETE FROM billing WHERE customer_id = ?", (customer_id,))
            c.execute("UPDATE customers SET outstanding_amount = 0, credit_balance = 0 WHERE id = ?", (customer_id,))
            conn.commit()
            
            # Setup scenario
            if scenario['setup'] == 'unpaid_bill':
                # Create an unpaid bill
                c.execute('''
                    INSERT INTO billing (customer_id, month, year, amount, month_bill_amount, paid_amount, status, outstanding_amount)
                    VALUES (?, 1, 2024, ?, ?, 0, 'Unpaid', ?)
                ''', (customer_id, scenario['unpaid_amount'], scenario['unpaid_amount'], scenario['unpaid_amount']))
                conn.commit()
                print(f"Created unpaid bill: {scenario['unpaid_amount']} PKR")
                
            elif scenario['setup'] == 'customer_outstanding':
                # Set customer outstanding amount
                c.execute("UPDATE customers SET outstanding_amount = ? WHERE id = ?", 
                         (scenario['customer_outstanding'], customer_id))
                conn.commit()
                print(f"Set customer outstanding: {scenario['customer_outstanding']} PKR")
                
            elif scenario['setup'] == 'customer_credit':
                # Set customer credit balance
                c.execute("UPDATE customers SET credit_balance = ? WHERE id = ?", 
                         (scenario['customer_credit'], customer_id))
                conn.commit()
                print(f"Set customer credit: {scenario['customer_credit']} PKR")
            
            # Test the Month Bill calculation
            from views.billing import BillingManager
            import tkinter as tk
            
            root = tk.Tk()
            root.withdraw()
            
            billing_manager = BillingManager(root, {})
            
            # Calculate Month Bill for new bill
            temp_bill_id = 999999  # Temporary ID for calculation
            calculated_month_bill = billing_manager._calculate_month_bill_amount(
                c, customer_id, temp_bill_id, scenario['new_amount']
            )
            
            print(f"New amount: {scenario['new_amount']} PKR")
            print(f"Calculated Month Bill: {calculated_month_bill} PKR")
            print(f"Expected Month Bill: {scenario['expected_month_bill']} PKR")
            
            # Verify result
            if abs(calculated_month_bill - scenario['expected_month_bill']) < 0.01:
                print("✅ CORRECT: Month Bill calculation matches expected result")
            else:
                print("❌ INCORRECT: Month Bill calculation is wrong")
                print(f"   Expected: {scenario['expected_month_bill']} PKR")
                print(f"   Got: {calculated_month_bill} PKR")
            
            root.destroy()

def test_manual_bill_creation():
    """Test manual bill creation with simplified billing rules"""
    print("\n\n=== Testing Manual Bill Creation ===")
    
    with get_db_connection() as conn:
        c = conn.cursor()
        
        # Get a test customer
        c.execute("SELECT id, name FROM customers LIMIT 1")
        customer = c.fetchone()
        if not customer:
            print("No customers found. Please add a customer first.")
            return
            
        customer_id, customer_name = customer
        print(f"Testing manual bill creation for: {customer_name} (ID: {customer_id})")
        
        # Clear existing data
        c.execute("DELETE FROM billing WHERE customer_id = ?", (customer_id,))
        c.execute("UPDATE customers SET outstanding_amount = 300, credit_balance = 0 WHERE id = ?", (customer_id,))
        conn.commit()
        
        # Test manual bill creation
        print("Creating manual bill with customer outstanding of 300 PKR...")
        
        from views.billing import BillingManager
        import tkinter as tk
        
        root = tk.Tk()
        root.withdraw()
        
        billing_manager = BillingManager(root, {})
        
        # Simulate manual bill creation
        manual_amount = 800.0
        temp_bill_id = 999999
        calculated_month_bill = billing_manager._calculate_month_bill_amount(
            c, customer_id, temp_bill_id, manual_amount
        )
        
        print(f"Manual bill amount: {manual_amount} PKR")
        print(f"Customer outstanding: 300 PKR")
        print(f"Expected Month Bill: {manual_amount + 300} = 1100 PKR")
        print(f"Calculated Month Bill: {calculated_month_bill} PKR")
        
        if abs(calculated_month_bill - 1100.0) < 0.01:
            print("✅ CORRECT: Manual bill calculation is working")
        else:
            print("❌ INCORRECT: Manual bill calculation failed")
        
        root.destroy()

if __name__ == "__main__":
    try:
        test_simplified_billing_scenarios()
        test_manual_bill_creation()
        print("\n=== Testing Complete ===")
    except Exception as e:
        print(f"Error during testing: {str(e)}")
        import traceback
        traceback.print_exc()
