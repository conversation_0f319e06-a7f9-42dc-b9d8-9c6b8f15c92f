import tkinter as tk
from tkinter import ttk, messagebox
import sqlite3
from database_utils import get_db_connection
import os
from datetime import datetime
from PIL import Image, ImageTk
from resources import resource_path
import re
from database import Database

class ProductManager(tk.Frame):
    COLORS = {
        'background': '#F8F9FA',        # Light gray background
        'card_bg': '#FFFFFF',           # White for cards
        'primary_accent': '#4A6FA5',    # Primary blue accent
        'secondary_accent': '#6C8FC7',  # Lighter blue for hover
        'text_primary': '#2D3748',      # Dark gray for primary text
        'text_secondary': '#718096',    # Lighter gray for secondary text
        'button_start': '#4A6FA5',      # Button default color
        'button_end': '#3A5A8C',        # Button hover/pressed color
        'border': '#E2E8F0',            # Light gray border
        'warning': '#E53E3E',           # Red for warnings/errors
        'input_bg': '#EDF2F7',          # Light gray for input fields
        'bar_colors': [
            '#4A6FA5',  # Primary accent
            '#6C8FC7',  # Secondary accent
            '#FFC107',  # Warning yellow
            '#E53E3E',  # Danger red
            '#2196F3',  # Info blue
            '#9E9E9E'   # Gray
        ],
        'pie_color1': '#4A6FA5',        # Primary accent for Paid
        'pie_color2': '#E53E3E',        # Danger red for Unpaid
        'pie_divider': '#FFFFFF',       # White
        'line_chart': '#4A6FA5',        # Primary accent
        'div_background': '#FFFFFF',    # White for div backgrounds
        'button_border': '#E2E8F0'      # Muted gray
    }

    def __init__(self, parent, nav_commands):
        super().__init__(parent)
        self.nav = nav_commands
        self.current_year = datetime.now().year
        self.current_month = datetime.now().month
        self.db_path = os.path.join(os.getenv('APPDATA'), 'CRM_System', 'crm_database.db')
        self.db = Database()  # Add database instance
        self._setup_ui()

    def _setup_ui(self):
        """Initialize the UI with header and product table."""
        self.config(bg=self.COLORS['background'])
        self.pack(fill="both", expand=True)
        self._create_header()
        self._create_product_table()

    def _create_header(self):
        """Create the header with navigation and action buttons."""
        header = tk.Frame(self, bg=self.COLORS['primary_accent'], height=90)
        header.pack(fill="x", pady=(0, 20))

        # Consolidated icon loading function
        def load_and_process_image(relative_path):
            try:
                full_path = resource_path(relative_path)
                if os.path.exists(full_path):
                    img = Image.open(full_path).convert("RGBA")
                    data = img.getdata()
                    new_data = []
                    for item in data:
                        if item[3] > 0:  # If pixel is not transparent
                            new_data.append((255, 255, 255, item[3]))  # Set to white
                        else:
                            new_data.append(item)
                    img.putdata(new_data)
                    img = img.resize((30, 30), Image.Resampling.LANCZOS)
                    return ImageTk.PhotoImage(img)
                else:
                    print(f"Icon not found: {full_path}")
                    return ImageTk.PhotoImage(Image.new('RGBA', (30, 30), (255, 255, 255, 0)))
            except Exception as e:
                print(f"Error loading image: {str(e)}")
                return ImageTk.PhotoImage(Image.new('RGBA', (30, 30), (255, 255, 255, 0)))

        # Load all icons
        self.dashboard_img = load_and_process_image('assets/dashboard/products/dashboard.png')
        self.add_img = load_and_process_image('assets/dashboard/products/addproduct.png')
        self.edit_img = load_and_process_image('assets/dashboard/products/editproduct.png')
        self.delete_img = load_and_process_image('assets/dashboard/products/deleteproduct.png')
        self.buy_img = load_and_process_image('assets/dashboard/products/buyproduct.png')

        # Dashboard button
        dashboard_btn = tk.Button(header, image=self.dashboard_img, command=self.nav['show_dashboard'],
                                bg=self.COLORS['primary_accent'], relief=tk.FLAT,
                                activebackground=self.COLORS['secondary_accent'],
                                width=30, height=30)
        dashboard_btn.pack(side="left", padx=10)
        Tooltip(dashboard_btn, "Dashboard")

        header_label = tk.Label(header, text="Product Management", font=("Helvetica", 24, "bold"),
                               fg="#FFFFFF", bg=self.COLORS['primary_accent'])
        header_label.place(relx=0.5, rely=0.5, anchor="center")

        button_frame = tk.Frame(header, bg=self.COLORS['primary_accent'])
        button_frame.pack(side="right", padx=10)

        # Add button
        add_btn = tk.Button(button_frame, image=self.add_img, command=self._add_product,
                           bg=self.COLORS['primary_accent'], relief=tk.FLAT,
                           activebackground=self.COLORS['secondary_accent'],
                           width=30, height=30)
        add_btn.pack(side="left", padx=5)
        Tooltip(add_btn, "Add Product")

        # Edit button
        edit_btn = tk.Button(button_frame, image=self.edit_img, command=self._edit_product,
                            bg=self.COLORS['primary_accent'], relief=tk.FLAT,
                            activebackground=self.COLORS['secondary_accent'],
                            width=30, height=30)
        edit_btn.pack(side="left", padx=5)
        Tooltip(edit_btn, "Edit Product")

        # Delete button
        delete_btn = tk.Button(button_frame, image=self.delete_img, command=self._delete_product,
                              bg=self.COLORS['primary_accent'], relief=tk.FLAT,
                              activebackground=self.COLORS['secondary_accent'],
                              width=30, height=30)
        delete_btn.pack(side="left", padx=5)
        Tooltip(delete_btn, "Delete Product")

        # Buy button
        buy_btn = tk.Button(button_frame, image=self.buy_img, command=self._buy_product,
                           bg=self.COLORS['primary_accent'], relief=tk.FLAT,
                           activebackground=self.COLORS['secondary_accent'],
                           width=30, height=30)
        buy_btn.pack(side="left", padx=5)
        Tooltip(buy_btn, "Buy Product")

    def _create_product_table(self):
        """Create the product table with columns for ID, Name, Description, and Price."""
        content = tk.Frame(self, bg=self.COLORS['div_background'])
        content.pack(fill="both", expand=True, padx=5, pady=5)

        tree_frame = tk.Frame(content, bg=self.COLORS['div_background'])
        tree_frame.pack(fill="both", expand=True, pady=(0, 5))

        style = ttk.Style()
        style.configure("Treeview",
                        background=self.COLORS['div_background'],
                        foreground=self.COLORS['text_primary'],
                        fieldbackground=self.COLORS['div_background'],
                        font=("Helvetica", 10))
        style.configure("Treeview.Heading",
                        font=("Helvetica", 10, "bold"),
                        foreground=self.COLORS['text_primary'])
        style.map("Treeview", background=[('selected', self.COLORS['primary_accent'])])
        style.layout("Treeview", [('Treeview.treearea', {'sticky': 'nswe'})])
        style.configure("Treeview", borderwidth=1, relief="solid", bordercolor=self.COLORS['border'])
        style.configure("Treeview", rowheight=25)

        scrollbar = ttk.Scrollbar(tree_frame, orient=tk.VERTICAL)
        scrollbar.pack(side="right", fill="y")

        self.tree = ttk.Treeview(tree_frame, columns=("id", "name", "description", "price"),
                               selectmode="browse", show="headings", yscrollcommand=scrollbar.set,
                               style="Treeview")
        scrollbar.config(command=self.tree.yview)

        self.tree.heading("id", text="ID", anchor="center")
        self.tree.heading("name", text="Name", anchor="center")
        self.tree.heading("description", text="Description", anchor="center")
        self.tree.heading("price", text="Price (PKR)", anchor="center")

        self.tree.column("id", width=80, anchor="center")
        self.tree.column("name", width=120, anchor="center")
        self.tree.column("description", width=200, anchor="center")
        self.tree.column("price", width=100, anchor="center")

        self.tree.pack(fill="both", expand=True)
        self._load_products()

    def _load_products(self):
        """Load products from the database into the table."""
        try:
            with get_db_connection() as conn:
                c = conn.cursor()

            for item in self.tree.get_children():
                self.tree.delete(item)

                c.execute("SELECT id, name, description, price FROM products")
                products = c.fetchall()
                for product in products:
                    self.tree.insert("", "end", values=product)
        except Exception as e:
            messagebox.showerror("Error", f"Failed to load products: {str(e)}")

    def _validate_input(self, char, current_text):
        """Validate input to allow only alphanumeric characters and limit to 25 characters."""
        if len(current_text) >= 25:
            return False
        return bool(re.match(r'^[a-zA-Z0-9\s]*$', char))

    def _add_product(self):
        """Open a dialog to add a new product and update stock."""
        dialog = tk.Toplevel(self)
        dialog.title("Add Product")
        dialog.transient(self)
        dialog.grab_set()
        dialog.configure(bg=self.COLORS['background'])
        dialog.geometry("560x270")
        dialog.update_idletasks()
        width = dialog.winfo_width()
        height = dialog.winfo_height()
        x = (dialog.winfo_screenwidth() // 2) - (width // 2)
        y = (dialog.winfo_screenheight() // 2) - (height // 2)
        dialog.geometry(f"{width}x{height}+{x}+{y}")

        main_frame = tk.Frame(dialog, bg=self.COLORS['card_bg'], highlightbackground=self.COLORS['border'],
                             highlightthickness=1, padx=20, pady=20)
        main_frame.pack(fill="both", expand=True)

        label_style = {"bg": self.COLORS['card_bg'], "font": ("Helvetica", 10), "fg": self.COLORS['text_primary']}
        entry_style = {"font": ("Helvetica", 10), "bg": self.COLORS['input_bg']}

        validate_cmd = (self.register(self._validate_input), '%S', '%P')

        tk.Label(main_frame, text="Product Name:", **label_style).grid(row=0, column=0, sticky="e", padx=(0, 10), pady=10)
        name_entry = tk.Entry(main_frame, width=40, **entry_style, validate="key", validatecommand=validate_cmd)
        name_entry.grid(row=0, column=1, padx=(0, 10), pady=10)

        tk.Label(main_frame, text="Description:", **label_style).grid(row=1, column=0, sticky="e", padx=(0, 10), pady=10)
        desc_entry = tk.Entry(main_frame, width=40, **entry_style, validate="key", validatecommand=validate_cmd)
        desc_entry.grid(row=1, column=1, padx=(0, 10), pady=10)

        tk.Label(main_frame, text="Price (PKR):", **label_style).grid(row=2, column=0, sticky="e", padx=(0, 10), pady=10)
        price_entry = tk.Entry(main_frame, width=40, **entry_style)
        price_entry.grid(row=2, column=1, padx=(0, 10), pady=10)

        def submit():
            name = name_entry.get().strip()
            if not name:
                messagebox.showerror("Error", "Product name cannot be empty")
                return

            try:
                price = float(price_entry.get())
                if price < 0:
                    raise ValueError("Price cannot be negative")
            except ValueError:
                messagebox.showerror("Error", "Please enter a valid price")
                return

            try:
                with get_db_connection() as conn:
                    c = conn.cursor()
                    conn.execute("BEGIN TRANSACTION")
                    c.execute("INSERT INTO products (name, description, price) VALUES (?, ?, ?)",
                             (name, desc_entry.get(), price))
                    product_id = c.lastrowid
                    c.execute("INSERT INTO stock (product_id, quantity, sold) VALUES (?, ?, ?)",
                             (product_id, 0, 0))
                messagebox.showinfo("Success", "Product added successfully")
                dialog.destroy()
                self._load_products()
                if 'refresh_stock' in self.nav:
                    self.nav['refresh_stock']()
            except sqlite3.IntegrityError:
                messagebox.showerror("Error", f"Product '{name}' already exists")
            except Exception as e:
                messagebox.showerror("Error", f"Failed to add product: {str(e)}")

        button_frame = tk.Frame(main_frame, bg=self.COLORS['card_bg'])
        button_frame.grid(row=3, columnspan=2, pady=20)
        tk.Button(button_frame, text="Save", command=submit, bg=self.COLORS['button_start'],
                  fg="#FFFFFF", font=("Helvetica", 10, "bold"), relief=tk.FLAT, padx=20, pady=10,
                  activebackground=self.COLORS['button_end']).pack()

    def _edit_product(self):
        """Open a dialog to edit an existing product."""
        selected = self.tree.selection()
        if not selected:
            messagebox.showwarning("Warning", "Please select a product to edit")
            return

        product_id = self.tree.item(selected[0])['values'][0]

        dialog = tk.Toplevel(self)
        dialog.title("Edit Product")
        dialog.transient(self)
        dialog.grab_set()
        dialog.configure(bg=self.COLORS['background'])
        dialog.geometry("560x270")
        dialog.update_idletasks()
        width = dialog.winfo_width()
        height = dialog.winfo_height()
        x = (dialog.winfo_screenwidth() // 2) - (width // 2)
        y = (dialog.winfo_screenheight() // 2) - (height // 2)
        dialog.geometry(f"{width}x{height}+{x}+{y}")

        main_frame = tk.Frame(dialog, bg=self.COLORS['card_bg'], highlightbackground=self.COLORS['border'],
                             highlightthickness=1, padx=20, pady=20)
        main_frame.pack(fill="both", expand=True)

        label_style = {"bg": self.COLORS['card_bg'], "font": ("Helvetica", 10), "fg": self.COLORS['text_primary']}
        entry_style = {"font": ("Helvetica", 10), "bg": self.COLORS['input_bg']}

        with get_db_connection() as conn:
            c = conn.cursor()
            c.execute("SELECT name, description, price FROM products WHERE id = ?", (product_id,))
            current_name, desc, price = c.fetchone()

        tk.Label(main_frame, text="Product Name:", **label_style).grid(row=0, column=0, sticky="e", padx=(0, 10), pady=10)
        name_entry = tk.Entry(main_frame, width=40, **entry_style)
        name_entry.insert(0, current_name)
        name_entry.grid(row=0, column=1, padx=(0, 10), pady=10)

        tk.Label(main_frame, text="Description:", **label_style).grid(row=1, column=0, sticky="e", padx=(0, 10), pady=10)
        desc_entry = tk.Entry(main_frame, width=40, **entry_style)
        desc_entry.insert(0, desc)
        desc_entry.grid(row=1, column=1, padx=(0, 10), pady=10)

        tk.Label(main_frame, text="Price (PKR):", **label_style).grid(row=2, column=0, sticky="e", padx=(0, 10), pady=10)
        price_entry = tk.Entry(main_frame, width=40, **entry_style)
        price_entry.insert(0, str(price))
        price_entry.grid(row=2, column=1, padx=(0, 10), pady=10)

        def submit():
            new_name = name_entry.get().strip()
            if not new_name:
                messagebox.showerror("Error", "Product name cannot be empty")
                return

            try:
                new_price = float(price_entry.get())
                if new_price < 0:
                    raise ValueError("Price cannot be negative")
            except ValueError:
                messagebox.showerror("Error", "Please enter a valid price")
                return

            try:
                with get_db_connection() as conn:
                    c = conn.cursor()
                    c.execute("UPDATE products SET name = ?, description = ?, price = ? WHERE id = ?",
                             (new_name, desc_entry.get(), new_price, product_id))
                messagebox.showinfo("Success", "Product updated successfully")
                dialog.destroy()
                self._load_products()
            except sqlite3.IntegrityError:
                messagebox.showerror("Error", f"Product '{new_name}' already exists")
            except Exception as e:
                messagebox.showerror("Error", f"Failed to update product: {str(e)}")

        button_frame = tk.Frame(main_frame, bg=self.COLORS['card_bg'])
        button_frame.grid(row=3, columnspan=2, pady=20)
        tk.Button(button_frame, text="Save", command=submit, bg=self.COLORS['button_start'],
                  fg="#FFFFFF", font=("Helvetica", 10, "bold"), relief=tk.FLAT, padx=20, pady=10,
                  activebackground=self.COLORS['button_end']).pack()

    def _delete_product(self):
        """Delete a selected product and its stock entry."""
        selected = self.tree.selection()
        if not selected:
            messagebox.showwarning("Warning", "Please select a product to delete")
            return

        product_id = self.tree.item(selected[0])['values'][0]
        product_name = self.tree.item(selected[0])['values'][1]

        if not messagebox.askyesno("Confirm", f"Delete product '{product_name}'?\nThis cannot be undone."):
            return

        try:
            with get_db_connection() as conn:
                c = conn.cursor()

                c.execute("SELECT COUNT(*) FROM customer_purchases WHERE product_id = ?", (product_id,))
                if c.fetchone()[0] > 0:
                    messagebox.showerror("Error", "Cannot delete product - it has been sold to customers")
                    return

                c.execute("DELETE FROM stock WHERE product_id = ?", (product_id,))
                c.execute("DELETE FROM products WHERE id = ?", (product_id,))
            messagebox.showinfo("Success", "Product deleted successfully")
            self._load_products()
            if 'refresh_stock' in self.nav:
                self.nav['refresh_stock']()
        except Exception as e:
            messagebox.showerror("Error", f"Failed to delete product: {str(e)}")

    def _buy_product(self):
        """Open a dialog to buy products for a customer, updating billing and stock."""
        customers = self._load_customers()
        if not customers:
            messagebox.showerror("Error", "No customers available to buy products.")
            return
        try:
            with get_db_connection() as conn:
                c = conn.cursor()
                c.execute("SELECT id, name, price FROM products")
                products = c.fetchall()
                c.execute("SELECT product_id, quantity, sold FROM stock")
                stock_data = {row[0]: {'quantity': row[1], 'sold': row[2]} for row in c.fetchall()}
            if not products:
                messagebox.showerror("Error", "No products available to buy.")
                return
        except Exception as e:
            messagebox.showerror("Error", f"Failed to load products: {str(e)}")
            return
        dialog = tk.Toplevel(self)
        dialog.title("Buy Product for Customer")
        dialog.transient(self)
        dialog.grab_set()
        dialog.configure(bg=self.COLORS['background'])
        dialog.geometry("560x600")
        dialog.update_idletasks()
        width = dialog.winfo_width()
        height = dialog.winfo_height()
        x = (dialog.winfo_screenwidth() // 2) - (width // 2)
        y = (dialog.winfo_screenheight() // 2) - (height // 2)
        dialog.geometry(f"{width}x{height}+{x}+{y}")
        main_frame = tk.Frame(dialog, bg=self.COLORS['card_bg'], highlightbackground=self.COLORS['border'],
                             highlightthickness=1, padx=20, pady=20)
        main_frame.pack(fill="both", expand=True)
        label_style = {"bg": self.COLORS['card_bg'], "font": ("Helvetica", 10), "fg": self.COLORS['text_primary']}
        entry_style = {"font": ("Helvetica", 10), "bg": self.COLORS['input_bg']}
        tk.Label(main_frame, text="Select Customer:", **label_style).pack(anchor="w", pady=(0, 10))
        customer_var = tk.StringVar()
        customer_dropdown = ttk.Combobox(main_frame, textvariable=customer_var,
                                       values=list(customers.keys()),
                                       state="readonly", width=50, font=("Helvetica", 10))
        customer_dropdown.pack(fill="x", pady=(0, 20))
        tk.Label(main_frame, text="Select Products:", **label_style).pack(anchor="w", pady=(0, 10))
        product_vars = {}
        for product_id, name, price in products:
            var = tk.IntVar()
            product_vars[(product_id, name, price)] = var
            remaining = 0
            if product_id in stock_data:
                remaining = stock_data[product_id]['quantity'] - stock_data[product_id]['sold']
            frame = tk.Frame(main_frame, bg=self.COLORS['card_bg'])
            frame.pack(anchor="w", pady=5)
            tk.Checkbutton(frame, text=f"{name} ({price:.2f} PKR)", variable=var,
                          bg=self.COLORS['card_bg'], fg=self.COLORS['text_primary'], selectcolor=self.COLORS['input_bg']).pack(side="left")
            tk.Label(frame, text=f"Available: {remaining}", **label_style).pack(side="left", padx=10)
        total_label = tk.Label(main_frame, text="Total: 0.00 PKR", **label_style)
        total_label.pack(pady=(20, 10))
        def update_total():
            total = sum(price for (pid, name, price), var in product_vars.items() if var.get() == 1)
            total_label.config(text=f"Total: {total:.2f} PKR")
        for (pid, name, price), var in product_vars.items():
            var.trace_add("write", lambda *args: update_total())
        def submit():
            selected_customer = customer_var.get()
            if not selected_customer:
                messagebox.showerror("Error", "Please select a customer")
                return
            selected_products = [(pid, name, price) for (pid, name, price), var in product_vars.items()
                               if var.get() == 1]
            if not selected_products:
                messagebox.showerror("Error", "Please select at least one product to buy")
                return
            # Check stock availability
            for product_id, name, price in selected_products:
                if product_id not in stock_data:
                    messagebox.showerror("Error", f"No stock available for {name}")
                    return
                remaining = stock_data[product_id]['quantity'] - stock_data[product_id]['sold']
                if remaining < 1:
                    messagebox.showerror("Error", f"Insufficient stock for {name}. Remaining: {remaining}")
                    return
            total_amount = sum(price for pid, name, price in selected_products)
            customer_id = customers[selected_customer]
            try:
                # Use direct database operations with connection context manager
                product_ids = [pid for pid, name, price in selected_products]
                purchase_date = datetime.now().strftime('%Y-%m-%d %H:%M:%S')

                with get_db_connection() as conn:
                    try:
                        conn.execute("BEGIN")
                        cursor = conn.cursor()

                        # Check if bill already exists for this customer, month, and year
                        cursor.execute('''
                            SELECT id FROM billing
                            WHERE customer_id = ? AND month = ? AND year = ?
                        ''', (customer_id, self.current_month, self.current_year))
                        existing_bill = cursor.fetchone()

                        if existing_bill:
                            raise ValueError(f"A bill already exists for {selected_customer} for {self.current_month:02d}/{self.current_year}")

                        # Generate invoice number
                        cursor.execute("SELECT MAX(CAST(SUBSTR(invoice_number, 5) AS INTEGER)) FROM billing WHERE invoice_number IS NOT NULL")
                        max_inv_num = cursor.fetchone()[0] or 0
                        invoice_number = f"INV-{str(max_inv_num + 1).zfill(4)}"

                        # Calculate month bill amount using simplified billing rules
                        # Import the billing calculation method
                        from views.billing import BillingManager
                        billing_manager = BillingManager(None, {})
                        temp_bill_id = 999999  # Temporary ID for calculation
                        month_bill_amount = billing_manager._calculate_month_bill_amount(cursor, customer_id, temp_bill_id, total_amount)

                        # Get customer's available credit to determine payment status
                        cursor.execute('SELECT credit_balance FROM customers WHERE id = ?', (customer_id,))
                        row = cursor.fetchone()
                        available_credit = row[0] if row and row[0] else 0.0

                        # Apply credit to the new bill
                        if available_credit >= month_bill_amount:
                            paid_amount = month_bill_amount
                            outstanding_amount = 0.0
                            new_credit_balance = available_credit - month_bill_amount
                            status = 'Paid'
                        else:
                            paid_amount = available_credit
                            outstanding_amount = month_bill_amount - available_credit
                            new_credit_balance = 0.0
                            status = 'Unpaid'

                        # Create the product purchase bill
                        cursor.execute('''
                            INSERT INTO billing (
                                customer_id, month, year, amount, month_bill_amount, paid_amount, outstanding_amount,
                                credit_amount, status, invoice_number, is_manual, created_date
                            ) VALUES (?, ?, ?, ?, ?, ?, ?, 0, ?, ?, 1, datetime('now'))
                        ''', (customer_id, self.current_month, self.current_year, total_amount, month_bill_amount, paid_amount, outstanding_amount, status, invoice_number))

                        bill_id = cursor.lastrowid

                        # Record customer purchases
                        for product_id, name, price in selected_products:
                            cursor.execute('''
                                INSERT INTO customer_purchases (customer_id, product_id, billing_id, purchase_date, price)
                                VALUES (?, ?, ?, ?, ?)
                            ''', (customer_id, product_id, bill_id, purchase_date, price))

                            # Update stock
                            cursor.execute('''UPDATE stock SET sold = sold + 1 WHERE product_id = ?''', (product_id,))

                        # Update customer credit balance and clear previous outstanding/credit since it's now included in this bill
                        cursor.execute('UPDATE customers SET credit_balance = ?, outstanding_amount = 0 WHERE id = ?', (new_credit_balance, customer_id))

                        # Sync customer financials with current bill amounts
                        cursor.execute('''
                            SELECT COALESCE(SUM(outstanding_amount), 0)
                            FROM billing
                            WHERE customer_id = ?
                        ''', (customer_id,))
                        total_outstanding = cursor.fetchone()[0] or 0.0

                        cursor.execute('''
                            UPDATE customers SET outstanding_amount = ?
                            WHERE id = ?
                        ''', (total_outstanding, customer_id))

                        conn.commit()

                        messagebox.showinfo("Success", f"Products purchased successfully for {selected_customer}.")
                        dialog.destroy()
                        self._load_products()
                        if 'refresh_billing' in self.nav:
                            self.nav['refresh_billing']()
                        if 'refresh_stock' in self.nav:
                            self.nav['refresh_stock']()

                    except Exception as e:
                        conn.rollback()
                        raise e

            except Exception as e:
                messagebox.showerror("Error", f"Failed to process purchase: {str(e)}")
        button_frame = tk.Frame(main_frame, bg=self.COLORS['card_bg'])
        button_frame.pack(pady=20)
        tk.Button(button_frame, text="Buy", command=submit, bg=self.COLORS['button_start'],
                  fg="#FFFFFF", font=("Helvetica", 10, "bold"), relief=tk.FLAT, padx=20, pady=10,
                  activebackground=self.COLORS['button_end']).pack()

    def _load_customers(self):
        """Load customer names and IDs from the database."""
        try:
            with get_db_connection() as conn:
                c = conn.cursor()
                c.execute("SELECT id, name FROM customers ORDER BY name")
                customers = {name: id for id, name in c.fetchall()}
            return customers
        except Exception as e:
            messagebox.showerror("Error", f"Failed to load customers: {str(e)}")
            return {}

class Tooltip:
    def __init__(self, widget, text):
        self.widget = widget
        self.text = text
        self.tooltip = None
        self.widget.bind("<Enter>", self.enter)
        self.widget.bind("<Leave>", self.leave)
        self.widget.bind("<ButtonPress>", self.leave)  # Close on click

    def enter(self, event=None):
        # Always show tooltip regardless of window state
        self.tooltip = tk.Toplevel(self.widget)
        self.tooltip.wm_overrideredirect(True)
        self.tooltip.wm_attributes("-topmost", True)  # Ensure it's on top
        
        label = tk.Label(
            self.tooltip,
            text=self.text,
            background="#4A6FA5",
            relief="solid",
            borderwidth=1,
            font=("Helvetica", 10),
            fg="#FFFFFF"
        )
        label.pack()
        
        # Get widget position relative to screen
        widget_x = self.widget.winfo_rootx()
        widget_y = self.widget.winfo_rooty()
        widget_width = self.widget.winfo_width()
        widget_height = self.widget.winfo_height()
        
        # Get tooltip dimensions
        self.tooltip.update_idletasks()  # Ensure size is calculated
        tooltip_width = self.tooltip.winfo_width()
        tooltip_height = self.tooltip.winfo_height()
        
        # Get screen dimensions
        screen_width = self.widget.winfo_screenwidth()
        screen_height = self.widget.winfo_screenheight()
        
        # Calculate position - below widget by default
        x = widget_x + (widget_width - tooltip_width) // 2
        y = widget_y + widget_height + 5
        
        # Adjust if tooltip would go off screen
        if x + tooltip_width > screen_width:
            x = screen_width - tooltip_width - 5
        elif x < 0:
            x = 5
            
        if y + tooltip_height > screen_height:
            y = widget_y - tooltip_height - 5
            if y < 0:
                y = 5
        
        self.tooltip.wm_geometry(f"+{int(x)}+{int(y)}")

    def leave(self, event=None):
        if self.tooltip:
            self.tooltip.destroy()
            self.tooltip = None
