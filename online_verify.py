import gspread
from oauth2client.service_account import ServiceAccountCredentials
import json
import os
import logging

def setup_online_verify():
    """Initialize Google Sheets connection"""
    try:
        scope = ['https://spreadsheets.google.com/feeds',
                'https://www.googleapis.com/auth/drive']
        
        # Look for credentials in multiple locations
        creds_paths = [
            os.path.join(os.getenv('APPDATA', ''), 'CRM_System', 'google_auth.json'),
            os.path.join(os.path.dirname(__file__), 'google_auth.json'),
            r'D:\CRM_System\google_auth.json'
        ]
        
        creds_path = None
        for path in creds_paths:
            if os.path.exists(path):
                creds_path = path
                break
        
        if not creds_path:
            logging.error("Google auth credentials not found")
            return None
        
        creds = ServiceAccountCredentials.from_json_keyfile_name(creds_path, scope)
        return gspread.authorize(creds)
    except Exception as e:
        logging.error(f"Google Sheets setup error: {str(e)}")
        return None

def verify_license_online(license_key):
    """Check license against Google Sheet"""
    try:
        gc = setup_online_verify()
        if not gc:
            logging.warning("Could not connect to Google Sheets")
            return False
        
        # Open the spreadsheet by URL
        sheet = gc.open_by_url(
            "https://docs.google.com/spreadsheets/d/1eezyIIAbTf_AYyAeJ_JKvIgSGeTZdG17ksdTaUclfu4/edit#gid=0"
        ).sheet1
        
        records = sheet.get_all_records()
        
        for record in records:
            if record.get('LicenseKey', '').strip() == license_key.strip():
                return record.get('Active', '').strip().upper() == "YES"
        
        logging.warning(f"License key not found: {license_key}")
        return False
    except Exception as e:
        logging.error(f"Online verification error: {str(e)}")
        return False

def revoke_license_online(license_key):
    """Mark license as revoked in Google Sheet"""
    try:
        gc = setup_online_verify()
        if not gc:
            return False
        
        sheet = gc.open_by_url(
            "https://docs.google.com/spreadsheets/d/1eezyIIAbTf_AYyAeJ_JKvIgSGeTZdG17ksdTaUclfu4/edit#gid=0"
        ).sheet1
        
        cell = sheet.find(license_key)
        if cell:
            sheet.update_cell(cell.row, 2, "NO")  # Update Active status
            logging.info(f"Revoked license: {license_key}")
            return True
        
        logging.warning(f"License key not found for revocation: {license_key}")
        return False
    except Exception as e:
        logging.error(f"License revocation error: {str(e)}")
        return False
