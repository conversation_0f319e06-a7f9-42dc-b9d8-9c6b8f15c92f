import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import smtplib
from email.mime.text import MIMEText
from email.mime.multipart import MI<PERSON><PERSON><PERSON>ipart
from email.mime.application import MIMEApplication
import schedule
import time
from datetime import datetime, timedelta
from database import Database
import os
import threading

class BackupConfig:
    def __init__(self, root):
        self.root = root
        self.root.title("Backup Configuration")
        self.db = Database()
        self.job_id_counter = 1
        self.scheduled_jobs = []
        self.setup_ui()
        self.load_scheduled_backups()

    def setup_ui(self):
        # Main frame
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        # Email Configuration Section
        email_frame = ttk.LabelFrame(main_frame, text="EMAIL CONFIGURATION", padding="10")
        email_frame.grid(row=0, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=5)

        ttk.Label(email_frame, text="SMTP Email:").grid(row=0, column=0, padx=5, pady=5, sticky=tk.W)
        self.smtp_email_entry = ttk.Entry(email_frame, width=30)
        self.smtp_email_entry.grid(row=0, column=1, padx=5, pady=5)

        ttk.Label(email_frame, text="SMTP Password:").grid(row=1, column=0, padx=5, pady=5, sticky=tk.W)
        self.smtp_password_entry = ttk.Entry(email_frame, width=30, show="*")
        self.smtp_password_entry.grid(row=1, column=1, padx=5, pady=5)

        ttk.Label(email_frame, text="SMTP Server:").grid(row=2, column=0, padx=5, pady=5, sticky=tk.W)
        self.smtp_server_entry = ttk.Entry(email_frame, width=30)
        self.smtp_server_entry.grid(row=2, column=1, padx=5, pady=5)
        self.smtp_server_entry.insert(0, "smtp.gmail.com")

        ttk.Label(email_frame, text="SMTP Port:").grid(row=3, column=0, padx=5, pady=5, sticky=tk.W)
        self.smtp_port_entry = ttk.Entry(email_frame, width=30)
        self.smtp_port_entry.grid(row=3, column=1, padx=5, pady=5)
        self.smtp_port_entry.insert(0, "587")

        ttk.Label(email_frame, text="Backup Email:").grid(row=4, column=0, padx=5, pady=5, sticky=tk.W)
        self.backup_email_entry = ttk.Entry(email_frame, width=30)
        self.backup_email_entry.grid(row=4, column=1, padx=5, pady=5)

        ttk.Button(email_frame, text="Test Email", command=self.test_email).grid(row=5, column=0, columnspan=2, pady=10)

        # Load existing email configuration
        config = self.db.get_backup_config()
        if config:
            self.smtp_email_entry.insert(0, config[1])
            self.smtp_password_entry.insert(0, config[2])
            self.smtp_server_entry.delete(0, tk.END)
            self.smtp_server_entry.insert(0, config[3])
            self.smtp_port_entry.delete(0, tk.END)
            self.smtp_port_entry.insert(0, config[4])
            self.backup_email_entry.insert(0, config[5])
            self.backup_dir = config[6]
        else:
            self.backup_dir = os.path.join(os.getenv('APPDATA'), 'CRM_System', 'Backups')

        # Schedule Full Backup Section
        full_backup_frame = ttk.LabelFrame(main_frame, text="SCHEDULE FULL BACKUP", padding="10")
        full_backup_frame.grid(row=1, column=0, sticky=(tk.W, tk.E), pady=5)

        ttk.Label(full_backup_frame, text="Schedule weekly full backups").grid(row=0, column=0, columnspan=2, pady=5)

        ttk.Label(full_backup_frame, text="Storage Location:").grid(row=1, column=0, padx=5, pady=5, sticky=tk.W)
        self.full_storage_var = tk.StringVar(value="Local")
        ttk.Radiobutton(full_backup_frame, text="Local", variable=self.full_storage_var, value="Local").grid(row=1, column=1, padx=5, pady=5, sticky=tk.W)
        ttk.Radiobutton(full_backup_frame, text="Email", variable=self.full_storage_var, value="Email").grid(row=2, column=1, padx=5, pady=5, sticky=tk.W)
        ttk.Radiobutton(full_backup_frame, text="Cloud (Google Drive)", variable=self.full_storage_var, value="Cloud").grid(row=3, column=1, padx=5, pady=5, sticky=tk.W)

        ttk.Label(full_backup_frame, text="Backup Day:").grid(row=4, column=0, padx=5, pady=5, sticky=tk.W)
        self.full_day_var = tk.StringVar(value="Monday")
        ttk.Combobox(full_backup_frame, textvariable=self.full_day_var, values=["Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday", "Sunday"]).grid(row=4, column=1, padx=5, pady=5)

        ttk.Label(full_backup_frame, text="Backup Time:").grid(row=5, column=0, padx=5, pady=5, sticky=tk.W)
        self.full_time_var = tk.StringVar(value="05:30")
        ttk.Combobox(full_backup_frame, textvariable=self.full_time_var, values=[f"{hour:02d}:{minute:02d}" for hour in range(24) for minute in (0, 30)]).grid(row=5, column=1, padx=5, pady=5)

        ttk.Button(full_backup_frame, text="Save Settings", command=self.save_full_backup_settings).grid(row=6, column=0, padx=5, pady=10)
        ttk.Button(full_backup_frame, text="Schedule", command=lambda: self.schedule_backup("full")).grid(row=6, column=1, padx=5, pady=10)

        # Schedule Differential Backup Section
        diff_backup_frame = ttk.LabelFrame(main_frame, text="SCHEDULE DIFF BACKUP", padding="10")
        diff_backup_frame.grid(row=1, column=1, sticky=(tk.W, tk.E), pady=5)

        ttk.Label(diff_backup_frame, text="Schedule daily differential backups").grid(row=0, column=0, columnspan=2, pady=5)

        ttk.Label(diff_backup_frame, text="Storage Location:").grid(row=1, column=0, padx=5, pady=5, sticky=tk.W)
        self.diff_storage_var = tk.StringVar(value="Local")
        ttk.Radiobutton(diff_backup_frame, text="Local", variable=self.diff_storage_var, value="Local").grid(row=1, column=1, padx=5, pady=5, sticky=tk.W)
        ttk.Radiobutton(diff_backup_frame, text="Email", variable=self.diff_storage_var, value="Email").grid(row=2, column=1, padx=5, pady=5, sticky=tk.W)
        ttk.Radiobutton(diff_backup_frame, text="Cloud (Google Drive)", variable=self.diff_storage_var, value="Cloud").grid(row=3, column=1, padx=5, pady=5, sticky=tk.W)

        ttk.Label(diff_backup_frame, text="Backup Time:").grid(row=4, column=0, padx=5, pady=5, sticky=tk.W)
        self.diff_time_var = tk.StringVar(value="15:00")
        ttk.Combobox(diff_backup_frame, textvariable=self.diff_time_var, values=[f"{hour:02d}:{minute:02d}" for hour in range(24) for minute in (0, 30)]).grid(row=4, column=1, padx=5, pady=5)

        ttk.Button(diff_backup_frame, text="Schedule", command=lambda: self.schedule_backup("differential")).grid(row=5, column=0, columnspan=2, pady=10)

        # Scheduled Backups Section
        scheduled_frame = ttk.LabelFrame(main_frame, text="SCHEDULED BACKUPS", padding="10")
        scheduled_frame.grid(row=2, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=5)

        self.scheduled_listbox = tk.Listbox(scheduled_frame, width=50, height=5)
        self.scheduled_listbox.grid(row=0, column=0, padx=5, pady=5)

        ttk.Button(scheduled_frame, text="Remove Selected", command=self.remove_scheduled_backup).grid(row=1, column=0, pady=10)

    def test_email(self):
        smtp_email = self.smtp_email_entry.get()
        smtp_password = self.smtp_password_entry.get()
        smtp_server = self.smtp_server_entry.get()
        smtp_port = self.smtp_port_entry.get()
        backup_email = self.backup_email_entry.get()

        if not all([smtp_email, smtp_password, smtp_server, smtp_port, backup_email]):
            messagebox.showerror("Error", "Please fill in all email configuration fields")
            return

        try:
            smtp_port = int(smtp_port)
            msg = MIMEMultipart()
            msg['From'] = smtp_email
            msg['To'] = backup_email
            msg['Subject'] = "Test Email from CRM System"
            msg.attach(MIMEText("This is a test email from the CRM System.", 'plain'))

            server = smtplib.SMTP(smtp_server, smtp_port)
            server.starttls()
            server.login(smtp_email, smtp_password)
            server.sendmail(smtp_email, backup_email, msg.as_string())
            server.quit()

            messagebox.showinfo("Success", "Test email sent successfully!")
        except Exception as e:
            messagebox.showerror("Error", f"Failed to send test email: {str(e)}")

    def save_full_backup_settings(self):
        smtp_email = self.smtp_email_entry.get()
        smtp_password = self.smtp_password_entry.get()
        smtp_server = self.smtp_server_entry.get()
        smtp_port = self.smtp_port_entry.get()
        backup_email = self.backup_email_entry.get()

        if not all([smtp_email, smtp_password, smtp_server, smtp_port, backup_email]):
            messagebox.showerror("Error", "Please fill in all email configuration fields")
            return

        self.db.save_backup_config(smtp_email, smtp_password, smtp_server, int(smtp_port), backup_email, self.backup_dir)
        messagebox.showinfo("Success", "Full backup settings saved successfully!")

    def schedule_backup(self, backup_type):
        storage_location = self.full_storage_var.get() if backup_type == "full" else self.diff_storage_var.get()
        backup_email = self.backup_email_entry.get()

        if storage_location == "Email" and not backup_email:
            messagebox.showerror("Error", "Please provide a backup email address for email storage")
            return

        if storage_location == "Cloud":
            config = self.db.get_google_drive_config()
            if not config or not config[4]:
                result = self.db.run_google_drive_config_wizard()
                if result:
                    self.db.save_google_drive_config(*result)
                else:
                    messagebox.showerror("Error", "Google Drive configuration failed")
                    return

        if backup_type == "full":
            date_str = self.full_day_var.get()
            time_str = self.full_time_var.get()
        else:
            date_str = "Daily"
            time_str = self.diff_time_var.get()

        backup_path = ""
        if storage_location == "Local":
            backup_path = filedialog.asksaveasfilename(
                defaultextension=".db",
                filetypes=[("Database files", "*.db")],
                initialdir=self.backup_dir
            )
            if not backup_path:
                return

        job_id = self.job_id_counter
        self.db.save_scheduled_backup(job_id, date_str, time_str, backup_type, backup_path, storage_location, backup_email)
        self.job_id_counter += 1

        display_text = f"ID: {job_id} | Type: {backup_type} | Day: {date_str} | Time: {time_str} | Path/Email: {backup_path or backup_email} | {storage_location}"
        self.scheduled_listbox.insert(tk.END, display_text)
        self.scheduled_jobs.append((job_id, backup_type, date_str, time_str, storage_location))

    def load_scheduled_backups(self):
        backups = self.db.get_scheduled_backups()
        for backup in backups:
            job_id, backup_type, date_str, time_str, backup_path, storage_location, backup_email = backup
            display_text = f"ID: {job_id} | Type: {backup_type} | Day: {date_str} | Time: {time_str} | Path/Email: {backup_path or backup_email} | {storage_location}"
            self.scheduled_listbox.insert(tk.END, display_text)
            self.scheduled_jobs.append((job_id, backup_type, date_str, time_str, storage_location))
            self.job_id_counter = max(self.job_id_counter, job_id + 1)

    def remove_scheduled_backup(self):
        selection = self.scheduled_listbox.curselection()
        if not selection:
            messagebox.showerror("Error", "Please select a backup to remove")
            return

        index = selection[0]
        job_id = self.scheduled_jobs[index][0]
        self.db.remove_scheduled_backup(job_id)
        self.scheduled_listbox.delete(index)
        self.scheduled_jobs.pop(index)

if __name__ == "__main__":
    root = tk.Tk()
    app = BackupConfig(root)
    root.mainloop()
