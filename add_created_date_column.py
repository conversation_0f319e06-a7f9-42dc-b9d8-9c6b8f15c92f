import sqlite3
import os

# Get the AppData path and database path
appdata_path = os.getenv('APPDATA')
db_path = os.path.join(appdata_path, 'CRM_System', 'crm_database.db')

if __name__ == "__main__":
    conn = sqlite3.connect(db_path)
    try:
        c = conn.cursor()
        c.execute("PRAGMA table_info(billing)")
        billing_columns = [col[1] for col in c.fetchall()]
        if 'created_date' not in billing_columns:
            c.execute("ALTER TABLE billing ADD COLUMN created_date TEXT")
            print("Added 'created_date' column to 'billing' table.")
        else:
            print("'created_date' column already exists in 'billing' table.")
        conn.commit()
    except Exception as e:
        print(f"Error: {e}")
    finally:
        conn.close() 