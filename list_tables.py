import sqlite3

DB_PATH = 'crm_database.db'

if __name__ == "__main__":
    conn = sqlite3.connect(DB_PATH)
    try:
        cursor = conn.execute("SELECT name FROM sqlite_master WHERE type='table';")
        tables = [row[0] for row in cursor.fetchall()]
        print("Tables in database:")
        for t in tables:
            print(f"- {t}")
    except Exception as e:
        print(f"Error: {e}")
    finally:
        conn.close() 