#!/usr/bin/env python3
"""
Test script to verify payment calculation is working correctly:

Scenario: 
1. Imported outstanding is 1500, That month bill is 1500 (unpaid)
2. User adds new bill of 2000 for June
3. Total payable should be 5000 (1500 + 2000 + 1500 outstanding)

But payment dialog should show correct amounts, not inflated ones.
"""

import sqlite3
import sys
import os
from datetime import datetime

# Add the current directory to the path so we can import our modules
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from database_utils import get_db_connection

def test_payment_calculation():
    """Test payment calculation scenario"""
    print("Testing Payment Calculation...")
    
    with get_db_connection() as conn:
        c = conn.cursor()
        
        # Get a test customer
        c.execute("SELECT id, name FROM customers LIMIT 1")
        customer = c.fetchone()
        if not customer:
            print("No customers found. Please add a customer first.")
            return
            
        customer_id, customer_name = customer
        print(f"Testing with customer: {customer_name} (ID: {customer_id})")
        
        # Clear existing data for clean testing
        c.execute("DELETE FROM billing WHERE customer_id = ?", (customer_id,))
        c.execute("DELETE FROM payment_history WHERE customer_id = ?", (customer_id,))
        c.execute("UPDATE customers SET outstanding_amount = 0, credit_balance = 0 WHERE id = ?", (customer_id,))
        conn.commit()
        
        print("\n=== Setting up scenario ===")
        
        # Step 1: Create first bill with 1500 outstanding (imported scenario)
        print("1. Creating first bill with 1500 outstanding (simulating imported data)")
        c.execute('''
            INSERT INTO billing (customer_id, month, year, amount, month_bill_amount, paid_amount, status, outstanding_amount)
            VALUES (?, 5, 2024, 1500, 1500, 0, 'Unpaid', 1500)
        ''', (customer_id,))
        first_bill_id = c.lastrowid
        conn.commit()
        print(f"   Created bill ID {first_bill_id}: Amount=1500, Month Bill=1500, Status=Unpaid")
        
        # Step 2: Add new manual bill of 2000 for June
        print("2. Adding new manual bill of 2000 for June")
        
        # Calculate what the month_bill_amount should be for the new bill
        from views.billing import BillingManager
        import tkinter as tk
        
        root = tk.Tk()
        root.withdraw()
        
        billing_manager = BillingManager(root, {})
        
        # Calculate Month Bill for new bill (should be 1500 unpaid + 2000 new = 3500)
        temp_bill_id = 999999
        calculated_month_bill = billing_manager._calculate_month_bill_amount(
            c, customer_id, temp_bill_id, 2000
        )
        
        print(f"   Calculated Month Bill for new bill: {calculated_month_bill}")
        print(f"   Expected: 1500 (unpaid) + 2000 (new) = 3500")
        
        # Create the second bill
        c.execute('''
            INSERT INTO billing (customer_id, month, year, amount, month_bill_amount, paid_amount, status, outstanding_amount)
            VALUES (?, 6, 2024, 2000, ?, 0, 'Unpaid', ?)
        ''', (customer_id, calculated_month_bill, calculated_month_bill))
        second_bill_id = c.lastrowid
        conn.commit()
        print(f"   Created bill ID {second_bill_id}: Amount=2000, Month Bill={calculated_month_bill}, Status=Unpaid")
        
        # Step 3: Check what the payment dialog would show
        print("\n=== Checking payment calculation ===")
        
        # Get unpaid bills like the payment dialog does
        c.execute('''
            SELECT b.id, b.month, b.year,
                   b.amount - COALESCE(b.paid_amount, 0) as pending,
                   b.amount as original_amount,
                   b.is_manual, b.outstanding_amount, b.credit_amount, b.month_bill_amount
            FROM billing b
            WHERE b.customer_id = ? AND (b.amount - COALESCE(b.paid_amount, 0)) > 0
            ORDER BY b.year, b.month ASC
        ''', (customer_id,))
        unpaid_bills = c.fetchall()
        
        print("Unpaid bills found:")
        total_pending = 0
        for bill in unpaid_bills:
            bill_id, month, year, pending, original_amount, is_manual, outstanding_amount, credit_amount, stored_month_bill_amount = bill
            total_pending += pending
            print(f"   Bill {bill_id}: Month={month}/{year}, Pending={pending}, Original={original_amount}, Month Bill={stored_month_bill_amount}")
        
        print(f"\nTotal pending amount (what user should pay): {total_pending}")
        print(f"Expected total: 1500 + 2000 = 3500")
        
        if abs(total_pending - 3500) < 0.01:
            print("✅ CORRECT: Payment calculation is working correctly")
        else:
            print("❌ INCORRECT: Payment calculation is wrong")
            print(f"   Expected: 3500")
            print(f"   Got: {total_pending}")
        
        root.destroy()

def test_manual_bill_amount_display():
    """Test that manual bills show correct amounts"""
    print("\n\n=== Testing Manual Bill Amount Display ===")
    
    with get_db_connection() as conn:
        c = conn.cursor()
        
        # Get a test customer
        c.execute("SELECT id, name FROM customers LIMIT 1")
        customer = c.fetchone()
        if not customer:
            print("No customers found.")
            return
            
        customer_id, customer_name = customer
        print(f"Testing manual bill display for: {customer_name}")
        
        # Clear existing data
        c.execute("DELETE FROM billing WHERE customer_id = ?", (customer_id,))
        c.execute("UPDATE customers SET outstanding_amount = 0, credit_balance = 0 WHERE id = ?", (customer_id,))
        conn.commit()
        
        # Create a manual bill
        manual_amount = 800.0
        c.execute('''
            INSERT INTO billing (customer_id, month, year, amount, month_bill_amount, paid_amount, status, outstanding_amount, is_manual)
            VALUES (?, 7, 2024, ?, ?, 0, 'Unpaid', ?, 1)
        ''', (customer_id, manual_amount, manual_amount, manual_amount))
        bill_id = c.lastrowid
        conn.commit()
        
        print(f"Created manual bill: Amount={manual_amount}")
        
        # Test how it would be displayed (simulate the billing display logic)
        c.execute('''
            SELECT b.id, b.amount, b.is_manual, b.month_bill_amount
            FROM billing b
            WHERE b.id = ?
        ''', (bill_id,))
        bill_data = c.fetchone()
        
        if bill_data:
            bill_id, amount, is_manual, month_bill_amount = bill_data
            
            # This should show the manual amount, not package price
            if is_manual == 1:
                display_package_price = amount  # Should be the manual amount
            else:
                display_package_price = 1500  # Would be package price for regular bills
            
            print(f"Manual bill display:")
            print(f"   Package Price column should show: {display_package_price}")
            print(f"   Month Bill column should show: {month_bill_amount}")
            
            if abs(display_package_price - manual_amount) < 0.01:
                print("✅ CORRECT: Manual bill shows correct amount")
            else:
                print("❌ INCORRECT: Manual bill shows wrong amount")

if __name__ == "__main__":
    try:
        test_payment_calculation()
        test_manual_bill_amount_display()
        print("\n=== Testing Complete ===")
    except Exception as e:
        print(f"Error during testing: {str(e)}")
        import traceback
        traceback.print_exc()
